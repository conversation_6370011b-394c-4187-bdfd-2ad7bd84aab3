# eth_data_processor.py
import pandas as pd
import numpy as np
# 引入您可能需要的技术指标库, 例如 talib
# import talib

class ETHDataProcessor:
    def __init__(self, ohlcv_df, instrument_name='ETHUSDT'):
        """
        初始化数据处理器
        :param ohlcv_df: Pandas DataFrame, 包含ETH的OHLCV数据，
                         必须有 'datetime' (pd.Timestamp), 'open', 'high', 'low', 'close', 'volume' 列。
                         'datetime' 列应该是唯一的，并已排序。
        :param instrument_name: 字符串, 交易对的名称，如 'ETHUSDT'
        """
        self.df = ohlcv_df.copy()
        self.instrument_name = instrument_name
        self.feature_columns = []
        self.label_column_name = 'label_return' # LARA中的 self.out

        if not isinstance(self.df.index, pd.DatetimeIndex):
            if 'datetime' in self.df.columns:
                self.df['datetime'] = pd.to_datetime(self.df['datetime'])
                self.df = self.df.set_index('datetime', drop=True)
            else:
                raise ValueError("DataFrame必须包含 'datetime' 列或拥有 DatetimeIndex.")
        self.df = self.df.sort_index()


    def generate_features_alpha158(self):
        """
        生成158个技术指标因子，计算方法基于Alpha158DL中的158因子公式。
        包含：
          1. kbar因子 (9个因子)
          2. Price因子（仅使用OPEN, HIGH, LOW, CLOSE; window=0，共4个因子）
          3. Rolling因子 (29组因子，每组对窗口[w]计算, w in [5,10,20,30,60]，共145个因子)
        """
        eps = 1e-12
        df = self.df.copy()

        # 1. KBar因子 (9个因子)
        df['feature_KMID']   = (df['close'] - df['open']) / df['open']
        df['feature_KLEN']   = (df['high'] - df['low']) / df['open']
        df['feature_KMID2']  = (df['close'] - df['open']) / (df['high'] - df['low'] + eps)
        df['feature_KUP']    = (df['high'] - df[['open', 'close']].max(axis=1)) / df['open']
        df['feature_KUP2']   = (df['high'] - df[['open', 'close']].max(axis=1)) / (df['high'] - df['low'] + eps)
        df['feature_KLOW']   = (df[['open', 'close']].min(axis=1) - df['low']) / df['open']
        df['feature_KLOW2']  = (df[['open', 'close']].min(axis=1) - df['low']) / (df['high'] - df['low'] + eps)
        df['feature_KSFT']   = (2 * df['close'] - df['high'] - df['low']) / df['open']
        df['feature_KSFT2']  = (2 * df['close'] - df['high'] - df['low']) / (df['high'] - df['low'] + eps)

        # 2. Price因子 (仅使用OPEN, HIGH, LOW, CLOSE; window=0), 共4个因子
        for field in ['open', 'high', 'low', 'close']:
            col_name = f'feature_PRICE_{field.upper()}0'
            df[col_name] = df[field] / df['close']

        # 3. Rolling因子 (29组因子，每组在窗口 [5,10,20,30,60] 下计算，共145个因子)
        windows = [5, 10, 20, 30, 60]

        # 定义滚动线性回归函数，返回斜率、R平方和最后一个残差
        def rolling_linreg(x):
            t = np.arange(len(x))
            if np.all(np.isnan(x)):
                return (np.nan, np.nan, np.nan)
            try:
                slope, intercept = np.polyfit(t, x, 1)
                pred = slope * t + intercept
                ss_res = np.sum((x - pred) ** 2)
                ss_tot = np.sum((x - np.mean(x)) ** 2)
                r2 = 1 - ss_res / ss_tot if ss_tot != 0 else np.nan
                resid = x[-1] - (slope * (len(x) - 1) + intercept)
            except Exception:
                slope, r2, resid = np.nan, np.nan, np.nan
            return slope, r2, resid

        for w in windows:
            # ROC: 前w期的close与当前close比值
            df[f'feature_ROC{w}'] = df['close'].shift(w) / df['close']

            # MA: w期移动平均
            df[f'feature_MA{w}'] = df['close'].rolling(window=w).mean() / df['close']

            # STD: w期标准差
            df[f'feature_STD{w}'] = df['close'].rolling(window=w).std() / df['close']

            # BETA, RSQR, RESI：采用滚动线性回归计算
            df[f'feature_BETA{w}'] = df['close'].rolling(window=w).apply(lambda x: rolling_linreg(x)[0], raw=True) / df['close']
            df[f'feature_RSQR{w}'] = df['close'].rolling(window=w).apply(lambda x: rolling_linreg(x)[1], raw=True)
            df[f'feature_RESI{w}'] = df['close'].rolling(window=w).apply(lambda x: rolling_linreg(x)[2], raw=True) / df['close']

            # MAX: w期内最高价除以当前close
            df[f'feature_MAX{w}'] = df['high'].rolling(window=w).max() / df['close']

            # LOW: w期内最低价除以当前close
            df[f'feature_LOW{w}'] = df['low'].rolling(window=w).min() / df['close']

            # QTLU: w期内close的0.8分位数
            df[f'feature_QTLU{w}'] = df['close'].rolling(window=w).quantile(0.8) / df['close']

            # QTLD: w期内close的0.2分位数
            df[f'feature_QTLD{w}'] = df['close'].rolling(window=w).quantile(0.2) / df['close']

            # RANK: 当前close在w期内的百分位排名
            df[f'feature_RANK{w}'] = df['close'].rolling(window=w).apply(lambda x: pd.Series(x).rank(pct=True).iloc[-1], raw=False)

            # RSV: (close - w期内最低低价) / (w期内最高价 - w期内最低低价)
            roll_low  = df['low'].rolling(window=w).min()
            roll_high = df['high'].rolling(window=w).max()
            df[f'feature_RSV{w}'] = (df['close'] - roll_low) / (roll_high - roll_low + eps)

            # IMAX: w期内最高价出现位置（归一化）
            df[f'feature_IMAX{w}'] = df['high'].rolling(window=w).apply(lambda x: np.argmax(x) / w, raw=True)

            # IMIN: w期内最低价出现位置（归一化）
            df[f'feature_IMIN{w}'] = df['low'].rolling(window=w).apply(lambda x: np.argmin(x) / w, raw=True)

            # IMXD: 最高价和最低价位置差（归一化）
            imax = df['high'].rolling(window=w).apply(lambda x: np.argmax(x), raw=True)
            imin = df['low'].rolling(window=w).apply(lambda x: np.argmin(x), raw=True)
            df[f'feature_IMXD{w}'] = (imax - imin) / w

            # CORR: w期内close与log(volume+1)的相关系数
            log_vol = np.log(df['volume'] + 1)
            df[f'feature_CORR{w}'] = df['close'].rolling(window=w).corr(log_vol)

            # CORD: w期内 (close/前一日close) 与 log( volume/前一日volume + 1) 的相关系数
            ret = df['close'] / df['close'].shift(1)
            vol_ret = np.log(df['volume'] / df['volume'].shift(1) + 1)
            df[f'feature_CORD{w}'] = ret.rolling(window=w).corr(vol_ret)

            # CNTP: w期内上涨比例
            df[f'feature_CNTP{w}'] = df['close'].diff().rolling(window=w).apply(lambda x: np.mean(x > 0))

            # CNTN: w期内下跌比例
            df[f'feature_CNTN{w}'] = df['close'].diff().rolling(window=w).apply(lambda x: np.mean(x < 0))

            # CNTD: 上涨比例与下跌比例之差
            df[f'feature_CNTD{w}'] = df[f'feature_CNTP{w}'] - df[f'feature_CNTN{w}']

            # SUMP: 上涨天数占比 (正差的天数/绝对变化之和)
            def sump(x):
                pos = np.sum(x > 0)
                denom = np.sum(np.abs(x))
                return pos / (denom + eps)
            df[f'feature_SUMP{w}'] = df['close'].diff().rolling(window=w).apply(sump, raw=True)

            # SUMN: 下跌天数占比 (负差的天数/绝对变化之和)
            def sumn(x):
                neg = np.sum(x < 0)
                denom = np.sum(np.abs(x))
                return neg / (denom + eps)
            df[f'feature_SUMN{w}'] = df['close'].diff().rolling(window=w).apply(sumn, raw=True)

            # SUMD: SUMP与SUMN的差值
            df[f'feature_SUMD{w}'] = df[f'feature_SUMP{w}'] - df[f'feature_SUMN{w}']

            # VMA: w期内成交量均值与当前成交量比值
            df[f'feature_VMA{w}'] = df['volume'].rolling(window=w).mean() / (df['volume'] + eps)

            # VSTD: w期内成交量标准差与当前成交量比值
            df[f'feature_VSTD{w}'] = df['volume'].rolling(window=w).std() / (df['volume'] + eps)

            # WVMA: 利用成交量加权的波动率指标
            vol_factor = abs(df['close'] / df['close'].shift(1) - 1) * df['volume']
            df[f'feature_WVMA{w}'] = vol_factor.rolling(window=w).std() / (vol_factor.rolling(window=w).mean() + eps)

            # VSUMP: w期内成交量上升日占比
            def vsump(x):
                pos = np.sum(x > 0)
                denom = np.sum(np.abs(x))
                return pos / (denom + eps)
            df[f'feature_VSUMP{w}'] = df['volume'].diff().rolling(window=w).apply(vsump, raw=True)

            # VSUMN: w期内成交量下降日占比
            def vsumn(x):
                neg = np.sum(x < 0)
                denom = np.sum(np.abs(x))
                return neg / (denom + eps)
            df[f'feature_VSUMN{w}'] = df['volume'].diff().rolling(window=w).apply(vsumn, raw=True)

            # VSUMD: VSUMP与VSUMN的差值
            df[f'feature_VSUMD{w}'] = df[f'feature_VSUMP{w}'] - df[f'feature_VSUMN{w}']

        # 记录所有生成的特征列名
        self.feature_columns = [col for col in df.columns if col.startswith('feature_')]
        print(f"Generated {len(self.feature_columns)} features.")
        self.df = df
        return self.df
    
    def generate_features_alpha360(self):
        """
        根据Alpha360DL里面360个因子的计算方法生成特征因子，共360个因子。
        每组因子的计算方法如下:
          CLOSE系列: (过去lag期的close)/(当前close)，lag取值59~1，lag=0时为 $close/$close
          OPEN系列: (过去lag期的open)/(当前close)
          HIGH系列: (过去lag期的high)/(当前close)
          LOW系列: (过去lag期的low)/(当前close)
          VWAP系列: (过去lag期的vwap)/(当前close)，如果vwap不存在，则用 (high+low+close)/3
          VOLUME系列: (过去lag期的volume)/(volume+1e-12)，lag=0时为 $volume/($volume+1e-12)
        """
        df = self.df.copy()
        # 如果vwap不存在，则计算vwap = (high + low + close) / 3
        if 'vwap' not in df.columns:
            df['vwap'] = (df['high'] + df['low'] + df['close']) / 3

        # CLOSE系列
        for lag in range(59, 0, -1):
            df[f'feature_CLOSE{lag}'] = df['close'].shift(lag) / df['close']
        df['feature_CLOSE0'] = df['close'] / df['close']

        # OPEN系列
        for lag in range(59, 0, -1):
            df[f'feature_OPEN{lag}'] = df['open'].shift(lag) / df['close']
        df['feature_OPEN0'] = df['open'] / df['close']

        # HIGH系列
        for lag in range(59, 0, -1):
            df[f'feature_HIGH{lag}'] = df['high'].shift(lag) / df['close']
        df['feature_HIGH0'] = df['high'] / df['close']

        # LOW系列
        for lag in range(59, 0, -1):
            df[f'feature_LOW{lag}'] = df['low'].shift(lag) / df['close']
        df['feature_LOW0'] = df['low'] / df['close']

        # VWAP系列
        for lag in range(59, 0, -1):
            df[f'feature_VWAP{lag}'] = df['vwap'].shift(lag) / df['close']
        df['feature_VWAP0'] = df['vwap'] / df['close']

        # VOLUME系列
        for lag in range(59, 0, -1):
            df[f'feature_VOLUME{lag}'] = df['volume'].shift(lag) / (df['volume'] + 1e-12)
        df['feature_VOLUME0'] = df['volume'] / (df['volume'] + 1e-12)

        # 更新特征列并返回处理后的df
        self.feature_columns = [col for col in df.columns if col.startswith('feature_')]
        print(f"Generated {len(self.feature_columns)} features.")
        self.df = df
        return self.df
    
    def _calculate_rsi(self, series, period=14):
        delta = series.diff(1)
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def generate_labels(self, future_periods=20):
        """
        生成标签，即未来N期的收益率。
        :param future_periods: 预测未来多少个周期的收益率。
                               根据您的K线数据频率（如1小时、4小时、1天）来确定这个值。
        """
        self.label_column_name = f'label_fwd_return_{future_periods}'
        self.df[self.label_column_name] = (self.df['close'].shift(-future_periods) / self.df['close']) - 1
        print(f"Generated label: {self.label_column_name}")
        return self.df

    def get_processed_data(self):
        """
        清理数据，去除因滚动窗口或shift产生的NaN值。
        """
        self.df = self.df.dropna()
        # 确保所有特征列都是数值类型
        for col in self.feature_columns:
            self.df[col] = pd.to_numeric(self.df[col], errors='coerce')
        self.df = self.df.dropna() # 再次dropna以防有转换错误
        return self.df

    def split_data_for_lara(self, processed_df, train_start_date, train_end_date,
                            valid_start_date, valid_end_date,
                            test_start_date, test_end_date):
        """
        将数据划分为训练集、验证集、测试集，并构造成LARA.fit所需的格式。
        日期应该是字符串 'YYYY-MM-DD' 或 'YYYY-MM-DD HH:MM:SS'
        """
        datasets_dict = {}
        for periode_name, start_date, end_date in [
            ("train", train_start_date, train_end_date),
            ("valid", valid_start_date, valid_end_date),
            ("test", test_start_date, test_end_date)
        ]:
            # 获取索引的时区
            tz = processed_df.index.tz

            # 转换 start_date 和 end_date 为带时区的时间
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            if start_dt.tzinfo is None:
                start_dt = start_dt.tz_localize(tz)
            else:
                start_dt = start_dt.tz_convert(tz)
            if end_dt.tzinfo is None:
                end_dt = end_dt.tz_localize(tz)
            else:
                end_dt = end_dt.tz_convert(tz)
            subset_df = processed_df[(processed_df.index >= pd.to_datetime(start_dt)) &
                                     (processed_df.index <= pd.to_datetime(end_dt))].copy()

            if subset_df.empty:
                print(f"Warning: {periode_name} dataset is empty for the given date range.")
                features_df = pd.DataFrame(columns=self.feature_columns)
                labels_df = pd.DataFrame(columns=[self.label_column_name, 'instrument', 'datetime'])
            else:
                # 特征 DataFrame
                features_df = subset_df[self.feature_columns].copy()
                features_df['instrument'] = self.instrument_name
                features_df['datetime'] = subset_df.index
                features_df = features_df.set_index(['datetime', 'instrument'])

                # 标签 DataFrame
                labels_df = subset_df[[self.label_column_name]].copy()
                labels_df['instrument'] = self.instrument_name
                labels_df['datetime'] = subset_df.index
                labels_df = labels_df.set_index(['datetime', 'instrument'])

            datasets_dict[periode_name] = {"feature": features_df, "label": labels_df}
            print(f"{periode_name} set: features shape {features_df.shape}, labels shape {labels_df.shape}")

        # LARA.fit 需要一个 prepare 方法返回 [train_dict, valid_dict, test_dict]
        class MockDatasetH:
            def __init__(self, train_d, valid_d, test_d):
                self.train_dict = train_d
                self.valid_dict = valid_d
                self.test_dict = test_d

            def prepare(self, segments, col_set, data_key):
                # segments is ["train", "valid", "test"]
                # col_set is ["feature", "label"]
                # data_key is DataHandlerLP.DK_L (not directly used here, as we pre-construct)
                prepared_data = []
                if "train" in segments:
                    prepared_data.append(self.train_dict)
                if "valid" in segments:
                    prepared_data.append(self.valid_dict)
                if "test" in segments:
                    prepared_data.append(self.test_dict)
                return prepared_data

        return MockDatasetH(datasets_dict["train"], datasets_dict["valid"], datasets_dict["test"])
