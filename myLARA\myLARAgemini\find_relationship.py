# run_rsica_filter_eth.py

import os
import re
from datetime import datetime, timedelta
import pandas as pd
from eth_data_processor import ETHDataProcessor

from rsica_label import label_sup_order_simple,label_sup_order,label_sup_order_simple_1,label_remaining_area,label_shape
from calculate_indicator import calculate_rsica


# ----------------------------
# 1. 配置区域
# ----------------------------
USE_FEATURE_TYPE = "158"   # "158" or "360"
instrument_name = 'ETHUSDT'
current_directory = os.getcwd()
eth_kline_filepath = f'{current_directory}/data/ETHUSDT_BINANCE_1m_2025-01-01_00_00_00_2025-05-31_00_00_00.csv'
# pkl 路径
features_pkl_158 = eth_kline_filepath.replace('.csv', '_158_features.pkl')
features_pkl_360 = eth_kline_filepath.replace('.csv', '_360_features.pkl')

# 从文件名提取开始/结束日期并计算 8:1:1 划分
match = re.search(
    r'_(\d{4}-\d{2}-\d{2})_\d{2}_\d{2}_\d{2}_(\d{4}-\d{2}-\d{2})_\d{2}_\d{2}_\d{2}',
    eth_kline_filepath
)
if not match:
    raise ValueError(f"无法从文件名中提取日期: {eth_kline_filepath}")
data_start = datetime.strptime(match.group(1), '%Y-%m-%d')
data_end   = datetime.strptime(match.group(2), '%Y-%m-%d')
total_days = (data_end - data_start).days + 1
train_days = int(total_days * 0.8)
valid_days = int(total_days * 0.0)
test_days  = total_days - train_days - valid_days

train_start = data_start
train_end   = data_start + timedelta(days=train_days - 1)
valid_start = train_end   + timedelta(days=1)
valid_end   = valid_start + timedelta(days=valid_days - 1)
test_start  = valid_end   + timedelta(days=1)
test_end    = data_end

print(f"Train : {train_start} ~ {train_end}")
print(f"Valid : {valid_start} ~ {valid_end}")
print(f"Test  : {test_start} ~ {test_end}")

# ------------------------------
# 2. 特征加载或生成
# ------------------------------
# 先把原始 CSV 读一下（下面只是为了构造 ETHDataProcessor 需要的 ohlcv）
raw_csv = pd.read_csv(eth_kline_filepath)
# 确保 datetime 列
raw_csv['datetime'] = pd.to_datetime(raw_csv['datetime'])
raw_csv = raw_csv.set_index('datetime', drop=False).sort_index()
# 构造 ETHDataProcessor 需要的标准 OHLCV DataFrame
ohlcv_df = pd.DataFrame({
    'datetime': raw_csv.index,
    'open':     raw_csv['open'],
    'high':     raw_csv['high'],
    'low':      raw_csv['low'],
    'close':    raw_csv['close'],
    'volume':   raw_csv.get('volume', 0.0),
})
ohlcv_df = ohlcv_df.set_index('datetime')
# 根据 USE_FEATURE_TYPE 决定加载/生成哪一套特征
if USE_FEATURE_TYPE == "158":
    if os.path.exists(features_pkl_158):
        print(f"[INFO] 加载已有158特征文件 {features_pkl_158}")
        df_feat = pd.read_pickle(features_pkl_158)
        processor = ETHDataProcessor(df_feat, instrument_name=instrument_name)
        processor.feature_columns = [c for c in df_feat.columns if c.startswith('feature_')]
    else:
        print("[INFO] 从 CSV 计算 Alpha158 特征")
        processor = ETHDataProcessor(ohlcv_df, instrument_name=instrument_name)
        processor.generate_features_alpha158()
        pd.to_pickle(processor.df, features_pkl_158)
        print(f"[INFO] 158 特征已保存到 {features_pkl_158}")
elif USE_FEATURE_TYPE == "360":
    if os.path.exists(features_pkl_360):
        print(f"[INFO] 加载已有360特征文件 {features_pkl_360}")
        df_feat = pd.read_pickle(features_pkl_360)
        processor = ETHDataProcessor(df_feat, instrument_name=instrument_name)
        processor.feature_columns = [c for c in df_feat.columns if c.startswith('feature_')]
    else:
        print("[INFO] 从 CSV 计算 Alpha360 特征")
        processor = ETHDataProcessor(ohlcv_df, instrument_name=instrument_name)
        processor.generate_features_alpha360()
        pd.to_pickle(processor.df, features_pkl_360)
        print(f"[INFO] 360 特征已保存到 {features_pkl_360}")
else:
    raise ValueError(f"不支持的 USE_FEATURE_TYPE: {USE_FEATURE_TYPE}")

# ------------------------------------------------------------------
# 3. 生成信号标签 + RSI累积面积 + 过滤
# ------------------------------------------------------------------
print("[INFO] 生成信号标签 label_sup_order ...")
labels = label_sup_order_simple_1(raw_csv.reset_index(drop=True))  # 返回一个 Series, index 对应 0..N-1
#labels = label_shape(raw_csv.reset_index(drop=True))
# 因为 raw 的 index 是 datetime，我们要把 labels 和 features 对齐
# 首先把 labels 放到一个按时间索引的 Series
label_ser = pd.Series(labels.values, index=raw_csv.index, name='rsica_return')

# 计算 RSI 累积面积
print("[INFO] 计算 RSI_cumulative_area ...")
rsi_ca = calculate_rsica(raw_csv.reset_index(drop=True), rsi_period=14, rsi_threshold=30)
rsi_ca_ser = pd.Series(rsi_ca, index=raw_csv.index, name='RSI_cumulative_area')

# 把标签和 RSI_cum_area 合并到特征表里
df = processor.df.copy()
df['rsica_return'] = label_ser
df['RSI_cumulative_area'] = rsi_ca_ser

# 只保留那些真正触发补单的行（label 不为空）
df = df.dropna(subset=['rsica_return'])

# 设置好用于 LARA 的 label 列名
processor.df = df
processor.feature_columns = [c for c in df.columns if c.startswith('feature_')]
processor.label_column_name = 'rsica_return'

# ----------------------------
# 4. 数据清洗 & 划分
# ----------------------------
print("[INFO] 清洗 NaN 并划分数据集 ...")
processed_df = processor.get_processed_data()


import matplotlib.pyplot as plt
# 假设 processed_df 已经准备好，并且所有 feature_* 列和 rsica_return 列都在里面
df = processed_df.copy()
# 只拿出 feature 列
feat_cols = [c for c in df.columns if c.startswith('feature_')]
target_col = 'rsica_return'
# 计算相关系数
corrs = df[feat_cols + [target_col]].corr()[target_col].drop(target_col)
# 排序
corrs_abs = corrs.abs().sort_values(ascending=False)
# 打印 top20
print("Top 20 features by absolute Pearson correlation:")
print(corrs_abs.head(20))
# 可视化
plt.figure(figsize=(6,8))
corrs_abs.head(20).plot(kind='barh')
plt.xlabel('|Pearson corr| with rsica_return')
plt.gca().invert_yaxis()
plt.tight_layout()
plt.show()