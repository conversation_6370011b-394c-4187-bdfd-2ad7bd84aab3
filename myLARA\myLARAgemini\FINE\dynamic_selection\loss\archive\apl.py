import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class NCELoss(torch.nn.Module):
    def __init__(self):
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.num_classes = num_classes
        self.cross_entropy = nn.CrossEntropyLoss()
    
    def forward(self, pred, labels, index=None):
        
        # denominator
        denom = torch.log(nn.softmax(pred)).sum(-1)
        
        # numerator
        ce = self.cross_entropy(pred, labels)
        
        return ce / denom
        

# class APLoss(torch.nn.Module):
#     def __init__(self, alpha, beta, num_classes=10):
#         super(SCELoss, self).__init__()
#         self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
#         self.alpha = alpha
#         self.beta = beta
#         self.num_classes = num_classes
#         self.cross_entropy = torch.nn.CrossEntropyLoss()
#         self.A = math.exp(-4)
        
#     def forward(self, pred, labels, index=None):
#         # index is redundant input for SCELoss
        
#         # CCE
#         ce = self.cross_entropy(pred, labels)

#         # RCE
#         pred = F.softmax(pred, dim=1)
#         pred = torch.clamp(pred, min=1e-7, max=1.0)
#         label_one_hot = torch.nn.functional.one_hot(labels, self.num_classes).float().to(self.device)
#         label_one_hot = torch.clamp(label_one_hot, min=self.A, max=1.0)
#         rce = (-1*torch.sum(pred * torch.log(label_one_hot), dim=1))

#         # Loss
#         loss = self.alpha * ce + self.beta * rce.mean()
#         return loss