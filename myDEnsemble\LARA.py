# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.

import os
import lightgbm as lgb
import numpy as np
import pandas as pd
from typing import Text, Union

from qlib.model.base import Model
from qlib.data.dataset import DatasetH
from qlib.data.dataset.handler import DataHandlerLP
from qlib.log import get_module_logger

import warnings
warnings.filterwarnings('ignore')

from config import THRESHOLD,label_thresholds


import importlib.util
def load_module_from_path(module_name, file_path):
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module
# 假设 model_all_utils.py 和 model_all.py 与 LARA.py 在同一目录下
# 如果不在同一目录，请修改路径
model_all_utils_path = os.path.join(os.path.dirname(__file__), 'model_all_utils.py') # 或者直接 'model_all_utils.py'
model_all_path = os.path.join(os.path.dirname(__file__), 'model_all.py') # 或者直接 'model_all.py'

model_all_utils = load_module_from_path('model_all_utils', model_all_utils_path)
model_all = load_module_from_path('model_all', model_all_path)

class LARA(Model):
    """LARA Model"""

    def __init__(
        self,
        use_metric = 'True',
        use_multi_label = True,
        shuffle = True,
        preprocessing = 'None',
        metric_method = 'ITML_Supervised',
        ping = "True",
        knn_rnn = 'knn',
        points = 100,
        radius = 100,
        use_dual = True,
        dual_numbers = 5,
        dual_ratio = 0.01,
        **kwargs
    ):

        self.logger = get_module_logger("LARA")

        self.use_metric = use_metric
        self.use_multi_label = use_multi_label
        self.shuffle = shuffle
        self.preprocessing = preprocessing
        self.metric_method = metric_method
        self.ping = ping
        self.knn_rnn = knn_rnn
        self.points = points
        self.radius = radius
        self.use_dual = use_dual
        self.dual_numbers = dual_numbers
        self.dual_ratio = dual_ratio

        self.params = {
            'objective': 'binary',
            'boosting_type': 'gbdt',
            'metric': {'binary_logloss', 'auc'},
            'silent': 1
        }
        #model_all_utils.set_seed(0, self.params)
        import random
        random_seed = random.randint(0, 10000)
        model_all_utils.set_seed(random_seed, self.params)

    def fit(self, dataset: DatasetH):
        df_train, df_valid, df_test = dataset.prepare(
            ["train", "valid", "test"], col_set=["feature", "label"], data_key=DataHandlerLP.DK_L
        )

        x_valid, y_valid = df_valid["feature"], df_valid["label"]
        x_train, y_train = df_train["feature"], df_train["label"]
        x_test, y_test = df_test["feature"], df_test["label"]

        y_train['instrument'] = y_train.index.get_level_values('instrument')
        y_train['datetime'] = y_train.index.get_level_values('datetime')
        y_test['instrument'] = y_test.index.get_level_values('instrument')
        y_test['datetime'] = y_test.index.get_level_values('datetime')

        self.usefv = x_train.columns
        self.out = y_train.columns[0]

        print(x_valid.shape[0], 
              x_train.shape[0], 
              x_test.shape[0])
        print(y_valid[y_valid[self.out] > THRESHOLD].shape[0], 
              y_train[y_train[self.out] > THRESHOLD].shape[0], 
              y_test[y_test[self.out] > THRESHOLD].shape[0])

        df_train = pd.concat([x_train[self.usefv], y_train], axis=1)
        df_test = pd.concat([x_test[self.usefv], y_test], axis=1)

        df_train, df_train_ping, train_label_metric_learning = self.data_processing(df_train)
        df_test = self.data_processing(df_test, False)

        train_dataset = (df_train[self.usefv], df_train[[self.out, 'label', 'instrument', 'datetime']])
        test_dataset = (df_test[self.usefv], df_test[[self.out, 'label', 'instrument', 'datetime']])
        ping_dataset = (df_train_ping[self.usefv], df_train_ping[[self.out, 'label', 'instrument', 'datetime']])

        if self.use_metric == "True":
            x_train_transform, y_train_transform, x_test_transform, y_test_transform, x_ping_transform, y_ping_transform, self.metric_model = model_all.metric_learning(self, train_dataset, test_dataset, ping_dataset, train_label_metric_learning)
        else:
            x_train_transform, y_train_transform, x_test_transform, y_test_transform, x_ping_transform, y_ping_transform \
            = train_dataset[0], train_dataset[1], test_dataset[0], test_dataset[1], ping_dataset[0], ping_dataset[1]

        self.logger.info("Complete Metric Learning")

        if self.ping == "True":
            x_train_transform = pd.concat(
                [x_train_transform, x_ping_transform], axis=0)
            y_train_transform = pd.concat(
                [y_train_transform, y_ping_transform], axis=0)
            x_train_transform.index = np.arange(x_train_transform.shape[0])
            y_train_transform.index = np.arange(y_train_transform.shape[0])
        
        if self.knn_rnn == "None":
            pass
        else:
            ann_path = os.path.join('./', 'ann_graph.bin')
            x_train_transform, y_train_transform, x_test_transform, y_test_transform = model_all.KNN(
                x_train_transform, y_train_transform, self, self.params, x_test_transform, y_test_transform, ann_path, train_test='train')

        test_date = y_test_transform[['instrument', 'datetime']]
        test_date.index = np.arange(test_date.shape[0])

        print(y_train_transform.head(10))

        self.logger.info("Complete KNN")

        bst = lgb.train(self.params, 
                    lgb.Dataset(x_train_transform, y_train_transform['label'])) 
        
        self.bst = bst                                       # 新增：保存训练好的模型
        self.x_train_transform = x_train_transform           # 新增：保存训练集中经过转换的特征
        self.y_train_transform = y_train_transform           # 新增：保存训练集中经过转换的标签

        if self.use_dual:

            changes, gbm_list, predicted_df = model_all_utils.dual_label_one(x_train_transform, 
                                                        y_train_transform, 
                                                        x_test_transform, 
                                                        y_test_transform, 
                                                        bst, 
                                                        self, 
                                                        self.params, 
                                                        self.usefv)

            predicted_copy = predicted_df.copy()
            predicted_copy['signal'] = predicted_df.mean(1)
            predicted_copy.index = pd.MultiIndex.from_frame(test_date[['datetime', 'instrument']])

            self.predicted = predicted_copy

        else:
            self.predicted = pd.DataFrame(bst.predict(x_test_transform), columns=['signal'], index=pd.MultiIndex.from_frame(test_date[['datetime', 'instrument']]))

    def data_processing(self, df_train, train=True):
        df_train['label'] = 0
        df_train.loc[df_train[self.out] <= THRESHOLD, 'label'] = 0
        df_train.loc[df_train[self.out] >= THRESHOLD, 'label'] = 1

        if train == False:
            return df_train

        metric_learning_label = pd.DataFrame([])
        metric_learning_label[self.out] = df_train[self.out].copy()
        metric_learning_label['label'] = [0]*metric_learning_label.shape[0]
        metric_learning_label.loc[metric_learning_label[self.out] >= label_thresholds["upper"], 'label'] = 5
        metric_learning_label.loc[
            (metric_learning_label[self.out] < label_thresholds["upper"]) & 
            (metric_learning_label[self.out] >= label_thresholds["upper_mid"]), 'label'
        ] = 4
        metric_learning_label.loc[
            (metric_learning_label[self.out] < label_thresholds["upper_mid"]) & 
            (metric_learning_label[self.out] >= label_thresholds["zero"]), 'label'
        ] = 3
        metric_learning_label.loc[
            (metric_learning_label[self.out] < label_thresholds["zero"]) & 
            (metric_learning_label[self.out] >= label_thresholds["lower_mid"]), 'label'
        ] = 2
        metric_learning_label.loc[
            (metric_learning_label[self.out] < label_thresholds["lower_mid"]) & 
            (metric_learning_label[self.out] >= label_thresholds["lower"]), 'label'
        ] = 1
        metric_learning_label.loc[metric_learning_label[self.out] < label_thresholds["lower"], 'label'] = 0

        df_train, df_train_ping, train_label_metric_learning = model_all_utils.sample_equal(df_train, metric_learning_label, pcteject=THRESHOLD, out=self.out)


        return df_train, df_train_ping, train_label_metric_learning

    def predict(self, dataset: DatasetH, segment: Union[Text, slice] = "test"):
        
        return self.predicted['signal']

    def predict_valid(self, dataset: DatasetH, segment: Union[Text, slice] = "valid"):
        """
        根据验证集（valid）数据预测信号，处理方式参照fit中test集的处理方式
        """
        # 从数据集中获取验证集数据
        df_valid = dataset.prepare([segment], col_set=["feature", "label"], data_key=DataHandlerLP.DK_L)[0]
        x_valid, y_valid = df_valid["feature"], df_valid["label"]
        # 提取 instrument 和 datetime
        y_valid = y_valid.copy()
        y_valid['instrument'] = y_valid.index.get_level_values('instrument')
        y_valid['datetime'] = y_valid.index.get_level_values('datetime')

        df_valid = pd.concat([x_valid[self.usefv], y_valid], axis=1)

        # 数据预处理
        df_valid = self.data_processing(df_valid, False)
        valid_dataset = (df_valid[self.usefv], df_valid[[self.out, "label", "instrument", "datetime"]])
        
        # 通过metric learning处理验证集数据
        if self.use_metric == "True":
            # 假设metric_model具有transform方法
            x_valid_transform = self.metric_model.transform(valid_dataset[0])
            y_valid_transform = valid_dataset[1]
        else:
            x_valid_transform, y_valid_transform = valid_dataset[0], valid_dataset[1]
        
        # 如果使用knn, 则需调用相同的KNN变换
        if self.knn_rnn != "None":
            import os
            ann_path = os.path.join('./', 'ann_graph.bin')
            x_train_trans, y_train_trans, x_valid_transform, y_valid_transform = model_all.KNN(
                self.x_train_transform, self.y_train_transform, self, self.params,
                x_valid_transform, y_valid_transform, ann_path, train_test='train'
            )
        
        valid_date = y_valid_transform[['instrument', 'datetime']]
        valid_date.index = np.arange(valid_date.shape[0])
        
        # 根据是否使用dual label选择预测方式
        if self.use_dual:
            changes, gbm_list, predicted_df = model_all_utils.dual_label_one(
                self.x_train_transform, self.y_train_transform,
                x_valid_transform, y_valid_transform,
                self.bst, self, self.params, self.usefv
            )
            predicted_copy = predicted_df.copy()
            predicted_copy['signal'] = predicted_df.mean(1)
            predicted_copy.index = pd.MultiIndex.from_frame(valid_date[['datetime', 'instrument']])
            return predicted_copy['signal']
        else:
            predicted_valid = pd.DataFrame(
                self.bst.predict(x_valid_transform), columns=['signal'],
                index=pd.MultiIndex.from_frame(valid_date[['datetime', 'instrument']])
            )
            return predicted_valid['signal']