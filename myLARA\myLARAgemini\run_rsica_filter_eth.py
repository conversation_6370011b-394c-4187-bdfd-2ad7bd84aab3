# run_rsica_filter_eth.py

import os
import re
from datetime import datetime, timedelta
import pandas as pd
from eth_data_processor import ETHDataProcessor
from LARA import LARA

from rsica_label import label_sup_order_simple,label_sup_order,label_sup_order_simple_1,label_remaining_area,label_shape
from calculate_indicator import calculate_rsica

def main():
    # ----------------------------
    # 1. 配置区域
    # ----------------------------
    USE_FEATURE_TYPE = "158"   # "158" or "360"
    instrument_name = 'ETHUSDT'
    current_directory = os.getcwd()
    eth_kline_filepath = f'{current_directory}/data/ETHUSDT_BINANCE_1m_2025-01-01_00_00_00_2025-05-31_00_00_00.csv'
    features_pkl = eth_kline_filepath.replace('.csv', '_158_features.pkl')
    # pkl 路径
    features_pkl_158 = eth_kline_filepath.replace('.csv', '_158_features.pkl')
    features_pkl_360 = eth_kline_filepath.replace('.csv', '_360_features.pkl')

    # 从文件名提取开始/结束日期并计算 8:1:1 划分
    match = re.search(
        r'_(\d{4}-\d{2}-\d{2})_\d{2}_\d{2}_\d{2}_(\d{4}-\d{2}-\d{2})_\d{2}_\d{2}_\d{2}',
        eth_kline_filepath
    )
    if not match:
        raise ValueError(f"无法从文件名中提取日期: {eth_kline_filepath}")
    data_start = datetime.strptime(match.group(1), '%Y-%m-%d')
    data_end   = datetime.strptime(match.group(2), '%Y-%m-%d')
    total_days = (data_end - data_start).days + 1
    train_days = int(total_days * 0.8)
    valid_days = int(total_days * 0.0)
    test_days  = total_days - train_days - valid_days

    train_start = data_start
    train_end   = data_start + timedelta(days=train_days - 1)
    valid_start = train_end   + timedelta(days=1)
    valid_end   = valid_start + timedelta(days=valid_days - 1)
    test_start  = valid_end   + timedelta(days=1)
    test_end    = data_end

    print(f"Train : {train_start} ~ {train_end}")
    print(f"Valid : {valid_start} ~ {valid_end}")
    print(f"Test  : {test_start} ~ {test_end}")

    # ------------------------------
    # 2. 特征加载或生成
    # ------------------------------
    # 先把原始 CSV 读一下（下面只是为了构造 ETHDataProcessor 需要的 ohlcv）
    raw_csv = pd.read_csv(eth_kline_filepath)
    # 确保 datetime 列
    raw_csv['datetime'] = pd.to_datetime(raw_csv['datetime'])
    raw_csv = raw_csv.set_index('datetime', drop=False).sort_index()
    # 构造 ETHDataProcessor 需要的标准 OHLCV DataFrame
    ohlcv_df = pd.DataFrame({
        'datetime': raw_csv.index,
        'open':     raw_csv['open'],
        'high':     raw_csv['high'],
        'low':      raw_csv['low'],
        'close':    raw_csv['close'],
        'volume':   raw_csv.get('volume', 0.0),
    })
    ohlcv_df = ohlcv_df.set_index('datetime')
    # 根据 USE_FEATURE_TYPE 决定加载/生成哪一套特征
    if USE_FEATURE_TYPE == "158":
        if os.path.exists(features_pkl_158):
            print(f"[INFO] 加载已有158特征文件 {features_pkl_158}")
            df_feat = pd.read_pickle(features_pkl_158)
            processor = ETHDataProcessor(df_feat, instrument_name=instrument_name)
            processor.feature_columns = [c for c in df_feat.columns if c.startswith('feature_')]
        else:
            print("[INFO] 从 CSV 计算 Alpha158 特征")
            processor = ETHDataProcessor(ohlcv_df, instrument_name=instrument_name)
            processor.generate_features_alpha158()
            pd.to_pickle(processor.df, features_pkl_158)
            print(f"[INFO] 158 特征已保存到 {features_pkl_158}")
    elif USE_FEATURE_TYPE == "360":
        if os.path.exists(features_pkl_360):
            print(f"[INFO] 加载已有360特征文件 {features_pkl_360}")
            df_feat = pd.read_pickle(features_pkl_360)
            processor = ETHDataProcessor(df_feat, instrument_name=instrument_name)
            processor.feature_columns = [c for c in df_feat.columns if c.startswith('feature_')]
        else:
            print("[INFO] 从 CSV 计算 Alpha360 特征")
            processor = ETHDataProcessor(ohlcv_df, instrument_name=instrument_name)
            processor.generate_features_alpha360()
            pd.to_pickle(processor.df, features_pkl_360)
            print(f"[INFO] 360 特征已保存到 {features_pkl_360}")
    else:
        raise ValueError(f"不支持的 USE_FEATURE_TYPE: {USE_FEATURE_TYPE}")

    # ------------------------------------------------------------------
    # 3. 生成信号标签 + RSI累积面积 + 过滤
    # ------------------------------------------------------------------
    print("[INFO] 生成信号标签 label_sup_order ...")
    labels = label_sup_order_simple_1(raw_csv.reset_index(drop=True))  # 返回一个 Series, index 对应 0..N-1
    #labels = label_shape(raw_csv.reset_index(drop=True))
    # 因为 raw 的 index 是 datetime，我们要把 labels 和 features 对齐
    # 首先把 labels 放到一个按时间索引的 Series
    label_ser = pd.Series(labels.values, index=raw_csv.index, name='rsica_return')

    # 计算 RSI 累积面积
    print("[INFO] 计算 RSI_cumulative_area ...")
    rsi_ca = calculate_rsica(raw_csv.reset_index(drop=True), rsi_period=14, rsi_threshold=30)
    rsi_ca_ser = pd.Series(rsi_ca, index=raw_csv.index, name='RSI_cumulative_area')

    # 把标签和 RSI_cum_area 合并到特征表里
    df = processor.df.copy()
    df['rsica_return'] = label_ser
    df['RSI_cumulative_area'] = rsi_ca_ser

    # 只保留那些真正触发补单的行（label 不为空）
    df = df.dropna(subset=['rsica_return'])

    # 设置好用于 LARA 的 label 列名
    processor.df = df
    processor.feature_columns = [c for c in df.columns if c.startswith('feature_')]
    processor.label_column_name = 'rsica_return'

    # ----------------------------
    # 4. 数据清洗 & 划分
    # ----------------------------
    print("[INFO] 清洗 NaN 并划分数据集 ...")
    processed_df = processor.get_processed_data()
    lara_dataset = processor.split_data_for_lara(
        processed_df,
        train_start.strftime('%Y-%m-%d'),
        train_end.strftime('%Y-%m-%d'),
        valid_start.strftime('%Y-%m-%d'),
        valid_end.strftime('%Y-%m-%d'),
        test_start.strftime('%Y-%m-%d'),
        test_end.strftime('%Y-%m-%d'),
    )
    # ----------------------------
    # 5. 初始化并训练 LARA
    # ----------------------------
    lara_params = {
        'use_metric': 'False',        # 是否使用度量学习
        'use_multi_label': True,     # 度量学习是否使用多级标签
        'shuffle': True,             # 是否打乱度量学习的训练数据
        'preprocessing': 'None',     # 特征预处理方法 ('StandardScaler', 'MinMaxScaler', 'None', etc.)
        'metric_method': 'LSML_Supervised', # 度量学习算法'LMNN':'ITML_Supervised':'SDML_Supervised':'LSML_Supervised':'MMC_Supervised':'NCA':'LFDA':'RCA_Supervised':'MLKR':'ITML':
        'ping': "True",              # 是否使用 ping 数据 (数据增强)
        'knn_rnn': 'rnn',            # 'knn', 'rnn', or 'None'. K近邻或R近邻过滤
        'points': 40,               # K值 (for KNN/RNN). PDF Table 5: 90-150
        'radius': 60,                # R值 (for RNN). PDF Table 5: 30-100
        'use_dual': False,            # 是否使用双重标签迭代优化
        'dual_numbers': 9,           # 双重标签迭代次数. PDF Table 5 "k r": k is 7 or 9. LARA default 5.
        'dual_ratio': 0.01,          # 双重标签调整比率. PDF Table 5 "k r": r is 0.03-0.10. LARA default 0.01.
        # LightGBM参数在LARA类内部定义，但可以通过kwargs传递覆盖，但通常不需要
    }
    print(f"[INFO] 初始化 LARA: {lara_params}")
    lara = LARA(**lara_params)

    print("[INFO] 开始训练 LARA ...")
    lara.fit(lara_dataset)
    print("[INFO] LARA 训练完成。")

    # ----------------------------
    # 6. 用 LARA 预测测试集
    # ----------------------------
    print("[INFO] 对测试集进行预测 ...")
    preds = lara.predict(dataset=None, segment="test")
    print(preds.head())
    print(f"[INFO] 共 {len(preds)} 条预测结果。")

    # ------------------------------
    # 7. 简单评估（可选）
    # ------------------------------
    test_label_df = lara_dataset.test_dict["label"]
    # 合并信号和真实标签
    df_ = pd.concat([preds.rename("signal"), test_label_df], axis=1, join="inner")
    if df_.shape[0] == 0:
        print("[WARN] 无法合并预测与真实标签")
        return

    # 按 signal 排序，取 top N 做精度评估
    N = min(1000, len(df_))
    thresh_label = 0.001
    top_df = df_.sort_values("signal", ascending=False).iloc[:N].copy()
    top_df["hit"] = (top_df[processor.label_column_name] >= thresh_label).astype(int)
    precision = top_df["hit"].mean()
    # 计算 Win/Loss Ratio (胜率/亏损率)
    win = top_df.loc[top_df[processor.label_column_name] > 0, processor.label_column_name].mean()
    loss = top_df.loc[top_df[processor.label_column_name] < 0, processor.label_column_name].mean()
    wlr = abs(win / loss) if loss and loss != 0 else float('nan')
    avg_ret = top_df[processor.label_column_name].mean()
    print(f"[RESULT] Top {N} 精度: {precision:.2%},Win/Loss Ratio: {wlr:.2f}, 平均真实收益: {avg_ret:.4%}")

    # 只统计 signal 大于 0.6 的所有结果
    filtered_df = df_[df_["signal"] > 0.6].copy()
    if filtered_df.shape[0] > 0:
        filtered_df["hit"] = (filtered_df[processor.label_column_name] >= thresh_label).astype(int)
        precision_all = filtered_df["hit"].mean()
        win_all = filtered_df.loc[filtered_df[processor.label_column_name] > 0, processor.label_column_name].mean()
        loss_all = filtered_df.loc[filtered_df[processor.label_column_name] < 0, processor.label_column_name].mean()
        wlr_all = abs(win_all / loss_all) if loss_all and loss_all != 0 else float('nan')
        avg_ret_all = filtered_df[processor.label_column_name].mean()
    else:
        precision_all = float('nan')
        win_all = float('nan')
        loss_all = float('nan')
        wlr_all = float('nan')
        avg_ret_all = float('nan')
    print(f"[RESULT] 所有 signal>0.6 样本的整体精度: {precision_all:.2%}, Win/Loss Ratio: {wlr_all:.2f}, 平均真实收益: {avg_ret_all:.4%}")


if __name__ == '__main__':
    main()
