import numpy as np
from sklearn.mixture import GaussianMixture as GMM
from sklearn import cluster

def get_singular_vector(features, labels):
    '''
    计算每个类别的奇异向量
    features: 特征向量 (numpy数组)
    labels: 对应的标签列表
    '''
    singular_vector_dict = {}
    for index in np.unique(labels):
        _, _, v = np.linalg.svd(features[labels==index])
        singular_vector_dict[index] = v[0]
    return singular_vector_dict

def get_score(singular_vector_dict, features, labels, normalization=True):
    '''
    计算每个样本的分数，表示样本是否为干净样本
    '''
    if normalization:
        scores = [np.abs(np.inner(singular_vector_dict[labels[indx]], feat/np.linalg.norm(feat))) for indx, feat in enumerate(features)]
    else:
        scores = [np.abs(np.inner(singular_vector_dict[labels[indx]], feat)) for indx, feat in enumerate(features)]
    return np.array(scores)

def fit_mixture(scores, labels, p_threshold=0.5):
    '''
    使用高斯混合模型拟合分数分布，返回属于干净聚类的样本索引
    '''
    clean_labels = []
    indexes = np.array(range(len(scores)))
    for cls in np.unique(labels):
        cls_index = indexes[labels==cls]
        feats = scores[labels==cls]
        feats_ = np.ravel(feats).astype(np.float64).reshape(-1, 1)
        gmm = GMM(n_components=2, covariance_type='full', tol=1e-6, max_iter=100)
        
        gmm.fit(feats_)
        prob = gmm.predict_proba(feats_)
        prob = prob[:,gmm.means_.argmax()]
        clean_labels += [cls_index[clean_idx] for clean_idx in range(len(cls_index)) if prob[clean_idx] > p_threshold] 
    
    return np.array(clean_labels, dtype=np.int64)

def cleansing(scores, labels):
    '''
    使用KMeans聚类将样本分为干净和噪声两类
    '''
    indexes = np.array(range(len(scores)))
    clean_labels = []
    for cls in np.unique(labels):
        cls_index = indexes[labels==cls]
        kmeans = cluster.KMeans(n_clusters=2, random_state=0).fit(scores[cls_index].reshape(-1, 1))
        if np.mean(scores[cls_index][kmeans.labels_==0]) < np.mean(scores[cls_index][kmeans.labels_==1]): 
            kmeans.labels_ = 1 - kmeans.labels_
        clean_labels += cls_index[kmeans.labels_ == 0].tolist()
    return np.array(clean_labels, dtype=np.int64)

def fine(current_features, current_labels, fit='kmeans', prev_features=None, prev_labels=None, p_threshold=0.5, norm=True):
    '''
    FINE算法的主函数，根据特征和标签选择干净样本
    
    参数:
    - current_features: 当前数据的特征
    - current_labels: 当前数据的标签
    - fit: 使用的拟合方法，可选'kmeans'或'gmm'
    - prev_features: 上一轮数据的特征（可选）
    - prev_labels: 上一轮数据的标签（可选）
    - p_threshold: GMM方法的概率阈值
    - norm: 是否对特征进行归一化
    
    返回:
    - 干净样本的索引
    '''
    # 计算奇异向量
    if prev_features is not None and prev_labels is not None:
        vector_dict = get_singular_vector(prev_features, prev_labels)
    else:
        vector_dict = get_singular_vector(current_features, current_labels)
    
    # 计算分数
    scores = get_score(vector_dict, features=current_features, labels=current_labels, normalization=norm)
    
    # 根据拟合方法选择干净样本
    if 'kmeans' in fit:
        clean_labels = cleansing(scores, current_labels)
    elif 'gmm' in fit:
        clean_labels = fit_mixture(scores, current_labels, p_threshold=p_threshold)
    else:
        raise NotImplementedError("只支持'kmeans'和'gmm'拟合方法")
    
    return clean_labels