import numpy as np
from sklearn.mixture import GaussianMixture
from tqdm import tqdm
import torch
from sklearn import cluster


def get_features(model, dataloader):
    """
    获取模型对数据集的特征表示
    
    参数:
        model: 神经网络模型
        dataloader: 数据加载器
    
    返回:
        features: 特征表示
        labels: 对应标签
    """
    labels = np.empty((0,))
    
    model.eval()
    with torch.no_grad():
        with tqdm(dataloader) as progress:
            for batch_idx, (data, label, _, _) in enumerate(progress):
                data, label = data.cuda() if torch.cuda.is_available() else data, label.long()
                feature, _ = model(data)
                
                labels = np.concatenate((labels, label.cpu().numpy()))
                if batch_idx == 0:
                    features = feature.detach().cpu().numpy()
                else:
                    features = np.concatenate((features, feature.detach().cpu().numpy()), axis=0)
    
    return features, labels

def get_singular_vector(features, labels):
    """
    计算每个类别的主奇异向量
    
    参数:
        features: 特征表示
        labels: 对应标签
    
    返回:
        singular_vector_dict: 每个类别的主奇异向量字典
    """
    singular_vector_dict = {}
    with tqdm(total=len(np.unique(labels))) as pbar:
        for index in np.unique(labels):
            _, _, v = np.linalg.svd(features[labels==index])
            singular_vector_dict[index] = v[0]
            pbar.update(1)
    
    return singular_vector_dict

def get_score(singular_vector_dict, features, labels, normalization=True):
    """
    计算每个样本的分数，表示样本的可靠性
    
    参数:
        singular_vector_dict: 每个类别的主奇异向量字典
        features: 特征表示
        labels: 对应标签
        normalization: 是否归一化特征
    
    返回:
        scores: 样本分数数组
    """
    if normalization:
        scores = [np.abs(np.inner(singular_vector_dict[labels[indx]], feat/np.linalg.norm(feat))) 
                 for indx, feat in enumerate(tqdm(features))]
    else:
        scores = [np.abs(np.inner(singular_vector_dict[labels[indx]], feat)) 
                 for indx, feat in enumerate(tqdm(features))]
    
    return np.array(scores)

def select_clean_samples(scores, labels, method='gmm', p_threshold=0.5):
    """
    基于分数选择干净样本
    
    参数:
        scores: 样本分数数组
        labels: 对应标签
        method: 选择方法 ('kmeans', 'gmm')
        p_threshold: GMM方法的概率阈值
    
    返回:
        clean_indices: 干净样本的索引
    """
    indices = np.array(range(len(scores)))
    clean_indices = []
    
    if method == 'kmeans':
        # 使用K-means聚类分离干净和噪声样本
        for cls in np.unique(labels):
            cls_indices = indices[labels==cls]
            kmeans = cluster.KMeans(n_clusters=2, random_state=0).fit(scores[cls_indices].reshape(-1, 1))
            
            # 假设平均分数较高的簇是干净样本
            if np.mean(scores[cls_indices][kmeans.labels_==0]) > np.mean(scores[cls_indices][kmeans.labels_==1]):
                clean_indices += cls_indices[kmeans.labels_ == 0].tolist()
            else:
                clean_indices += cls_indices[kmeans.labels_ == 1].tolist()
    
    elif method == 'gmm':
        # 使用高斯混合模型分离干净和噪声样本
        for cls in np.unique(labels):
            cls_indices = indices[labels==cls]
            cls_scores = scores[cls_indices].reshape(-1, 1)
            
            gmm = GaussianMixture(n_components=2, random_state=0)
            gmm.fit(cls_scores)
            
            # 获取每个样本属于每个组件的概率
            probs = gmm.predict_proba(cls_scores)
            
            # 确定哪个组件对应干净样本（均值较高的组件）
            if gmm.means_[0] > gmm.means_[1]:
                clean_component = 0
            else:
                clean_component = 1
            
            # 选择属于干净组件概率高于阈值的样本
            clean_indices += cls_indices[probs[:, clean_component] > p_threshold].tolist()
    
    return np.array(clean_indices, dtype=np.int64)