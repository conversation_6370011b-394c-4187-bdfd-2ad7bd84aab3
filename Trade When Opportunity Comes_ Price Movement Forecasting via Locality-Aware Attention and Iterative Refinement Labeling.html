<h1 class="ltx_title ltx_title_document">Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling</h1>
<div class="ltx_authors">
<span class="ltx_creator ltx_role_author">
<span class="ltx_personname">
<PERSON><sup class="ltx_sup ltx_markedasmath" id="id9.2.id1">1</sup>
</span></span>
<span class="ltx_author_before">  </span><span class="ltx_creator ltx_role_author">
<span class="ltx_personname"><PERSON><PERSON><sup class="ltx_sup ltx_markedasmath" id="id10.2.id1">1</sup>
</span></span>
<span class="ltx_author_before">  </span><span class="ltx_creator ltx_role_author">
<span class="ltx_personname"><PERSON><sup class="ltx_sup ltx_markedasmath" id="id11.2.id1">1</sup>
</span></span>
<span class="ltx_author_before">  </span><span class="ltx_creator ltx_role_author">
<span class="ltx_personname">Ruchen Zhang<sup class="ltx_sup ltx_markedasmath" id="id12.2.id1">2</sup>
</span></span>
<span class="ltx_author_before">  </span><span class="ltx_creator ltx_role_author">
<span class="ltx_personname">Ling Wang<sup class="ltx_sup ltx_markedasmath" id="id13.5.id1">2</sup>
and Jian Li<sup class="ltx_sup ltx_markedasmath" id="id14.6.id2">1</sup>
<br class="ltx_break"/><sup class="ltx_sup ltx_markedasmath" id="id15.7.id3">1</sup>Institute for Interdisciplinary Information Sciences (IIIS), Tsinghua University, China

<br class="ltx_break"/><sup class="ltx_sup ltx_markedasmath" id="id16.8.id4">2</sup>Huatai Securities Co., Ltd, China

<br class="ltx_break"/>{zengl18, wanglei20, niuh17}@mails.tsinghua.edu.cn; {zhangruchen, wangling}@htsc.com 
<br class="ltx_break"/><EMAIL>
</span></span>
</div>
<div class="ltx_abstract">
<h6 class="ltx_title ltx_title_abstract">Abstract</h6>
<p class="ltx_p" id="id17.id1">Price movement forecasting, aimed at predicting financial asset trends based on current market information, has achieved promising advancements through machine learning (ML) methods.
Most existing ML methods, however, struggle with the extremely low signal-to-noise ratio and stochastic nature of financial data, often mistaking noises for real trading signals without careful selection of potentially profitable samples.
To address this issue, we propose LARA,
a novel price movement forecasting framework with two main components: <span class="ltx_text ltx_framed ltx_framed_underline" id="id17.id1.1">L</span>ocality-Aware <span class="ltx_text ltx_framed ltx_framed_underline" id="id17.id1.2">A</span>ttention (LA-Attention) and Iterative <span class="ltx_text ltx_framed ltx_framed_underline" id="id17.id1.3">R</span>efinement L<span class="ltx_text ltx_framed ltx_framed_underline" id="id17.id1.4">a</span>beling (RA-Labeling).
(1) LA-Attention, enhanced by metric learning techniques, automatically extracts the potentially profitable samples through masked attention scheme and task-specific distance metrics.
(2) RA-Labeling further iteratively refines the noisy labels of potentially profitable samples, and combines the learned predictors robust to the unseen and noisy samples.
In a set of experiments on three real-world financial markets: stocks, cryptocurrencies, and ETFs, LARA significantly outperforms several machine learning based methods on the Qlib quantitative investment platform. Extensive ablation studies confirm LARA’s superior ability in capturing more reliable trading opportunities.</p>
</div>
<section class="ltx_section" id="S1">
<h2 class="ltx_title ltx_title_section">
<span class="ltx_tag ltx_tag_section">1 </span>Introduction</h2>
<div class="ltx_para" id="S1.p1">
<p class="ltx_p" id="S1.p1.1">Price movement forecasting, a crucial yet challenging task in quantitative finance, is notoriously difficult largely due to the financial market’s inherently stochastic, dynamic, and volatile nature <cite class="ltx_cite ltx_citemacro_cite">De Prado (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib7" title="">2018</a>)</cite>.
The classical Efficient Market Hypothesis (EMH) <cite class="ltx_cite ltx_citemacro_cite">Fama (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib9" title="">1960</a>); Lo (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib21" title="">2019</a>)</cite> states that in an informationally efficient market, price changes must be unforecastable if all relevant information is reflected immediately.
However, perfect efficiency is impossible to achieve in practice
<cite class="ltx_cite ltx_citemacro_cite">Campbell <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib5" title="">1997</a>)</cite> due to informational asymmetry and “noisy” behaviors among the traders <cite class="ltx_cite ltx_citemacro_cite">Bloomfield <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib4" title="">2009</a>)</cite>.
Indeed, hundreds of abnormal returns have been discovered in the literature (see the classical review by <cite class="ltx_cite ltx_citemacro_cite">Grossman and Stiglitz (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib10" title="">1980</a>)</cite> and the recent list <cite class="ltx_cite ltx_citemacro_cite">Harvey <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib12" title="">2016</a>)</cite>).
As the more recent endeavor to “beat the market” and achieve excess returns, machine learning based solutions, taking advantage of the nonlinear expressive power, have become increasingly popular and achieved promising results <cite class="ltx_cite ltx_citemacro_cite">De Prado (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib7" title="">2018</a>); Gu <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib11" title="">2018</a>); Zhang <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib37" title="">2020</a>); Xu <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib34" title="">2020</a>); Wang <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib28" title="">2019a</a>)</cite>.</p>
</div>
<div class="ltx_para" id="S1.p2">
<p class="ltx_p" id="S1.p2.1">The straightforward approach to utilizing machine learning techniques for financial forecasting can be distilled into two primary steps <cite class="ltx_cite ltx_citemacro_cite">Gu <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib11" title="">2018</a>)</cite>.
First, mine effective factors (either manually or automatically <cite class="ltx_cite ltx_citemacro_cite">Li <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib18" title="">2019</a>)</cite>) correlated/predictive with asset returns.
Second, feed these factors as features into some off-the-shelf machine learning algorithms to generate trading signals. Recently, much effort has been devoted to designing new machine learning algorithms in the second step to make more accurate predictions <cite class="ltx_cite ltx_citemacro_cite">Zhang <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib37" title="">2020</a>); Xu <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib34" title="">2020</a>); Zhang <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib36" title="">2017</a>); Wang <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib29" title="">2019b</a>); Yang <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib35" title="">2020</a>); De Prado (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib7" title="">2018</a>)</cite>.
Most works mentioned above trains the model over the entire set of training data (typically spans a consecutive period of time)<span class="ltx_note ltx_role_footnote" id="footnote1"><sup class="ltx_note_mark">1</sup><span class="ltx_note_outer"><span class="ltx_note_content"><sup class="ltx_note_mark">1</sup><span class="ltx_tag ltx_tag_note">1</span>Note that in financial time-series forecasting problems, one should not split the training and testing set randomly, since the data points are not i.i.d., but strongly and temporally correlated.</span></span></span>.
However, it is well known that financial time-series data has an extremely low signal-to-noise ratio <cite class="ltx_cite ltx_citemacro_cite">De Prado (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib7" title="">2018</a>)</cite>, to the degree that modern machine algorithms are prone to pick up patterns of noise instead of real profitable signals (<em class="ltx_emph ltx_font_italic" id="S1.p2.1.1">a.k.a.,</em> overfit the training set).
As depicted in Fig. <a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#S1.F1" title="Figure 1 ‣ 1 Introduction ‣ Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling"><span class="ltx_text ltx_ref_tag">1</span></a>, samples are mixed together in the feature space, making it challenging to clearly distinguish between them.
Without careful selection of potentially profitable samples from the entire set of training data, tuning the models to generalize well can be very challenging <cite class="ltx_cite ltx_citemacro_cite">Zhang <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib36" title="">2017</a>); Kim <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib16" title="">2021</a>)</cite>. Even for the most experienced investment experts, asset prices often exhibit behaviors akin to unpredictable random walks, with profitable opportunities occurring only sporadically.
In light of this fact, we argue that it can be more effective and robust to focus on specific samples that are potentially more predictable and profitable (Fig. <a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#S1.F1" title="Figure 1 ‣ 1 Introduction ‣ Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling"><span class="ltx_text ltx_ref_tag">1</span></a>), as the proverb says, <em class="ltx_emph ltx_font_italic" id="S1.p2.1.2">trade when opportunity comes</em>. Consequently, our goal is to develop a powerful and generalizable model for price movement forecasting over highly noisy data. However, this realistic financial scenario brings two unique technical challenges, which we will tackle in this paper.</p>
</div>
<figure class="ltx_figure" id="S1.F1"><img alt="Refer to caption" class="ltx_graphics ltx_centering ltx_img_landscape" height="129" id="S1.F1.g1" src="x1.png" width="398"/>
<figcaption class="ltx_caption ltx_centering"><span class="ltx_tag ltx_tag_figure">Figure 1: </span>Illustration of the potential profitable samples. The half top figure represents the probability density function (PDF) of expected return over corresponding samples (best viewed in color).</figcaption>
</figure>
<div class="ltx_para" id="S1.p3">
<p class="ltx_p" id="S1.p3.1"><span class="ltx_text ltx_font_bold" id="S1.p3.1.1">Challenge 1: How to effectively extract potentially profitable samples?</span></p>
</div>
<div class="ltx_para" id="S1.p4">
<p class="ltx_p" id="S1.p4.1"><span class="ltx_text ltx_font_bold" id="S1.p4.1.1">Solution 1:</span> <em class="ltx_emph ltx_font_italic" id="S1.p4.1.2">Locality-aware attention</em> (LA-Attention).
We design two main modules for LA-Attention: a <em class="ltx_emph ltx_font_italic" id="S1.p4.1.3">metric learning module</em> for constructing a better metric, and a <em class="ltx_emph ltx_font_italic" id="S1.p4.1.4">localization module</em> for explicitly extracting potentially profitable samples.
Specifically, many algorithms rely critically on being given a good metric over their inputs due to the complex internal relations among different features of financial data <cite class="ltx_cite ltx_citemacro_cite">Xing <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib33" title="">2002</a>)</cite>.
It motivates us to introduce the <em class="ltx_emph ltx_font_italic" id="S1.p4.1.5">metric learning module</em> to assist the following <em class="ltx_emph ltx_font_italic" id="S1.p4.1.6">localization module</em> by learning a more compatible distance metric <cite class="ltx_cite ltx_citemacro_cite">De Vazelhes <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib8" title="">2020</a>)</cite>.
The <em class="ltx_emph ltx_font_italic" id="S1.p4.1.7">localization module</em> intends to model an auxiliary relation between input samples and their labels by locally integrating label information to extract the desired potentially profitable samples.</p>
</div>
<div class="ltx_para" id="S1.p5">
<p class="ltx_p" id="S1.p5.1"><span class="ltx_text ltx_font_bold" id="S1.p5.1.1">Challenge 2: How to adaptively refine the labels of potentially profitable samples?</span></p>
</div>
<div class="ltx_para" id="S1.p6">
<p class="ltx_p" id="S1.p6.1"><span class="ltx_text ltx_font_bold" id="S1.p6.1.1">Solution 2:</span> <em class="ltx_emph ltx_font_italic" id="S1.p6.1.2">Iterative refinement labeling</em> (RA-Labeling). We empirically identify that in the actual financial market, two samples may have similar features but yield completely opposite labels because of the stochastic and chaotic nature of financial data <cite class="ltx_cite ltx_citemacro_cite">De Prado (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib7" title="">2018</a>); Lo and MacKinlay (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib20" title="">2011</a>)</cite>.
Such <em class="ltx_emph ltx_font_italic" id="S1.p6.1.3">noisy samples</em>, <span class="ltx_text ltx_font_italic" id="S1.p6.1.4">i.e.,</span> similar samples with the opposite labels, hamper the model generalization in practical financial applications <cite class="ltx_cite ltx_citemacro_cite">Song <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib25" title="">2022</a>)</cite>.
In response, we propose the novel RA-Labeling method to adaptively distinguish noisy labels of potentially profitable samples according to their training losses, iteratively refine their labels with multiple predictors, and combine the learned predictors to enhance the overall performance.</p>
</div>
<figure class="ltx_figure" id="S1.F2"><img alt="Refer to caption" class="ltx_graphics ltx_centering ltx_img_landscape" height="150" id="S1.F2.g1" src="extracted/5721909/pics/framework2.png" width="687"/>
<figcaption class="ltx_caption ltx_centering"><span class="ltx_tag ltx_tag_figure">Figure 2: </span>The workflow of our proposed LARA framework. LARA first extracts potentially profitable samples from the noisy market and then refines their labels. LARA consists of two sequential components: LA-Attention and RA-Labeling.</figcaption>
</figure>
<div class="ltx_para" id="S1.p7">
<p class="ltx_p" id="S1.p7.1">In short, our main <span class="ltx_text ltx_font_bold" id="S1.p7.1.1">contributions</span> are as follows:</p>
<ul class="ltx_itemize" id="S1.I1">
<li class="ltx_item" id="S1.I1.i1" style="list-style-type:none;">
<span class="ltx_tag ltx_tag_item">•</span>
<div class="ltx_para" id="S1.I1.i1.p1">
<p class="ltx_p" id="S1.I1.i1.p1.1">We propose the two-step LA-Attention method to first locally extract potentially profitable samples by attending to label information and then construct a more accurate classifier based on these selected samples. Moreover, we utilize a metric learning module to learn a well-suited distance metric to assist LA-Attention.</p>
</div>
</li>
<li class="ltx_item" id="S1.I1.i2" style="list-style-type:none;">
<span class="ltx_tag ltx_tag_item">•</span>
<div class="ltx_para" id="S1.I1.i2.p1">
<p class="ltx_p" id="S1.I1.i2.p1.1">We introduce the RA-Labeling method to adaptively refine the noisy labels of potentially profitable samples. We obtain a noise-robust model with our proposed combination schemes to integrate the multiple learned predictors and boost generalization ability.</p>
</div>
</li>
<li class="ltx_item" id="S1.I1.i3" style="list-style-type:none;">
<span class="ltx_tag ltx_tag_item">•</span>
<div class="ltx_para" id="S1.I1.i3.p1">
<p class="ltx_p" id="S1.I1.i3.p1.1">We conduct comprehensive experiments on three real-world financial markets. LARA significantly outperforms a set of machine learning competitors on the Qlib quantitative investment platform <cite class="ltx_cite ltx_citemacro_cite">Yang <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib35" title="">2020</a>)</cite>. Extensive ablation studies and experiments demonstrate the effectiveness of each component in LARA and suggest that LARA indeed captures more reliable trading opportunities.</p>
</div>
</li>
</ul>
</div>
</section>
<section class="ltx_section" id="S2">
<h2 class="ltx_title ltx_title_section">
<span class="ltx_tag ltx_tag_section">2 </span>Related Work</h2>
<section class="ltx_paragraph" id="S2.SS0.SSS0.Px1">
<h4 class="ltx_title ltx_title_paragraph">Price Movement Forecasting.</h4>
<div class="ltx_para" id="S2.SS0.SSS0.Px1.p1">
<p class="ltx_p" id="S2.SS0.SSS0.Px1.p1.1">There are two main research threads in price movement forecasting based on machine learning methods.
(1) <cite class="ltx_cite ltx_citemacro_cite">Li <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib18" title="">2019</a>); Achelis (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib2" title="">2001</a>); Sawhney <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib24" title="">2021</a>); Xu <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib34" title="">2020</a>)</cite> focus on improving prediction by introducing extra financial data sources. <cite class="ltx_cite ltx_citemacro_cite">Gu <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib11" title="">2018</a>)</cite> used sentiment analysis to extract supplementary financial information from various textual data sources.
Besides, there also exists much literature (<span class="ltx_text ltx_font_italic" id="S2.SS0.SSS0.Px1.p1.1.1">e</span>.<span class="ltx_text ltx_font_italic" id="S2.SS0.SSS0.Px1.p1.1.2">g</span>., <cite class="ltx_cite ltx_citemacro_cite">Li <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib18" title="">2019</a>); Achelis (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib2" title="">2001</a>)</cite>) studying new methods to construct more informative features for financial data.
(2) <cite class="ltx_cite ltx_citemacro_cite">Zhang <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib37" title="">2020</a>); Wu <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib30" title="">2021</a>); Jiang <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib13" title="">2022</a>); Zhang <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib36" title="">2017</a>)</cite> aim to develop specialized prediction models to enhance prediction accuracy.
<cite class="ltx_cite ltx_citemacro_cite">Zhang <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib37" title="">2020</a>)</cite> proposed a new ensemble method based on the sample reweighting and feature selection method for stock price prediction.
Despite increasing efforts in price movement forecasting, few of them explicitly incorporate auxiliary label information in an extremely noisy market.
We propose the two-step LA-Attention to effectively extract potentially profitable samples.</p>
</div>
</section>
<section class="ltx_paragraph" id="S2.SS0.SSS0.Px2">
<h4 class="ltx_title ltx_title_paragraph">Handling Label Noise.</h4>
<div class="ltx_para" id="S2.SS0.SSS0.Px2.p1">
<p class="ltx_p" id="S2.SS0.SSS0.Px2.p1.1">Learning with noisy labels is a common challenge in many real-world applications <cite class="ltx_cite ltx_citemacro_cite">Bloomfield <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib4" title="">2009</a>); Zhang <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib37" title="">2020</a>); Song <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib25" title="">2022</a>)</cite>.
<cite class="ltx_cite ltx_citemacro_cite">Chen <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib6" title="">2021</a>)</cite> proposed a new labeling method called SEAL (Self-Evolution Average Label) to tackle instance-dependent label noise.
Apart from the labeling method, <cite class="ltx_cite ltx_citemacro_cite">Kim <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib16" title="">2021</a>)</cite> proposed filtering noisy instances via their eigenvectors (FINE), while <cite class="ltx_cite ltx_citemacro_cite">Xia <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib32" title="">2021</a>)</cite> dynamically selected training samples by analyzing the prediction values between two models.
However, in the context of quantitative investment, these methods may not suffice due to extreme noise levels, as illustrated in Fig. <a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#S1.F1" title="Figure 1 ‣ 1 Introduction ‣ Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling"><span class="ltx_text ltx_ref_tag">1</span></a>.
Simple label modifications or noisy sample removal can inadvertently increase uncertainty or eliminate the potentially profitable opportunities.
In contrast, our RA-Labeling method is designed to adaptively refine the noisy labels of potentially profitable samples to build a noise-robsut model.

</p>
</div>
</section>
</section>
<section class="ltx_section" id="S3">
<h2 class="ltx_title ltx_title_section">
<span class="ltx_tag ltx_tag_section">3 </span>Preliminary</h2>
<section class="ltx_paragraph" id="S3.SS0.SSS0.Px1">
<h4 class="ltx_title ltx_title_paragraph">Problem Setup.</h4>
<div class="ltx_para" id="S3.SS0.SSS0.Px1.p1">
<p class="ltx_p" id="S3.SS0.SSS0.Px1.p1.5">Consider a time series of the asset trading price over <math alttext="T" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px1.p1.1.m1.1"><semantics id="S3.SS0.SSS0.Px1.p1.1.m1.1a"><mi id="S3.SS0.SSS0.Px1.p1.1.m1.1.1" xref="S3.SS0.SSS0.Px1.p1.1.m1.1.1.cmml">T</mi><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px1.p1.1.m1.1b"><ci id="S3.SS0.SSS0.Px1.p1.1.m1.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.1.m1.1.1">𝑇</ci></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px1.p1.1.m1.1c">T</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px1.p1.1.m1.1d">italic_T</annotation></semantics></math> time-steps <math alttext="\{\text{Price}_{t}|t=1,2,\dots,T\}" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px1.p1.2.m2.6"><semantics id="S3.SS0.SSS0.Px1.p1.2.m2.6a"><mrow id="S3.SS0.SSS0.Px1.p1.2.m2.6.6.2" xref="S3.SS0.SSS0.Px1.p1.2.m2.6.6.3.cmml"><mo id="S3.SS0.SSS0.Px1.p1.2.m2.*******" stretchy="false" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******.cmml">{</mo><msub id="S3.SS0.SSS0.Px1.p1.2.m2.*******" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******.cmml"><mtext id="S3.SS0.SSS0.Px1.p1.2.m2.*******.2" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******.2a.cmml">Price</mtext><mi id="S3.SS0.SSS0.Px1.p1.2.m2.*******.3" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******.3.cmml">t</mi></msub><mo id="S3.SS0.SSS0.Px1.p1.2.m2.6.6.2.4" lspace="0em" rspace="0em" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******.cmml">|</mo><mrow id="S3.SS0.SSS0.Px1.p1.2.m2.*******" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******.cmml"><mi id="S3.SS0.SSS0.Px1.p1.2.m2.*******.2" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******.2.cmml">t</mi><mo id="S3.SS0.SSS0.Px1.p1.2.m2.*******.1" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******.1.cmml">=</mo><mrow id="S3.SS0.SSS0.Px1.p1.2.m2.*******.3.2" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******.3.1.cmml"><mn id="S3.SS0.SSS0.Px1.p1.2.m2.1.1" xref="S3.SS0.SSS0.Px1.p1.2.m2.1.1.cmml">1</mn><mo id="S3.SS0.SSS0.Px1.p1.2.m2.*******.3.2.1" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******.3.1.cmml">,</mo><mn id="S3.SS0.SSS0.Px1.p1.2.m2.2.2" xref="S3.SS0.SSS0.Px1.p1.2.m2.2.2.cmml">2</mn><mo id="S3.SS0.SSS0.Px1.p1.2.m2.*******.3.2.2" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******.3.1.cmml">,</mo><mi id="S3.SS0.SSS0.Px1.p1.2.m2.3.3" mathvariant="normal" xref="S3.SS0.SSS0.Px1.p1.2.m2.3.3.cmml">…</mi><mo id="S3.SS0.SSS0.Px1.p1.2.m2.*******.3.2.3" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******.3.1.cmml">,</mo><mi id="S3.SS0.SSS0.Px1.p1.2.m2.4.4" xref="S3.SS0.SSS0.Px1.p1.2.m2.4.4.cmml">T</mi></mrow></mrow><mo id="S3.SS0.SSS0.Px1.p1.2.m2.6.6.2.5" stretchy="false" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******.cmml">}</mo></mrow><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px1.p1.2.m2.6b"><apply id="S3.SS0.SSS0.Px1.p1.2.m2.6.6.3.cmml" xref="S3.SS0.SSS0.Px1.p1.2.m2.6.6.2"><csymbol cd="latexml" id="S3.SS0.SSS0.Px1.p1.2.m2.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******">conditional-set</csymbol><apply id="S3.SS0.SSS0.Px1.p1.2.m2.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.2.m2.*******.1.cmml" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******">subscript</csymbol><ci id="S3.SS0.SSS0.Px1.p1.2.m2.*******.2a.cmml" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******.2"><mtext id="S3.SS0.SSS0.Px1.p1.2.m2.*******.2.cmml" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******.2">Price</mtext></ci><ci id="S3.SS0.SSS0.Px1.p1.2.m2.*******.3.cmml" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******.3">𝑡</ci></apply><apply id="S3.SS0.SSS0.Px1.p1.2.m2.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******"><eq id="S3.SS0.SSS0.Px1.p1.2.m2.*******.1.cmml" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******.1"></eq><ci id="S3.SS0.SSS0.Px1.p1.2.m2.*******.2.cmml" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******.2">𝑡</ci><list id="S3.SS0.SSS0.Px1.p1.2.m2.*******.3.1.cmml" xref="S3.SS0.SSS0.Px1.p1.2.m2.*******.3.2"><cn id="S3.SS0.SSS0.Px1.p1.2.m2.1.1.cmml" type="integer" xref="S3.SS0.SSS0.Px1.p1.2.m2.1.1">1</cn><cn id="S3.SS0.SSS0.Px1.p1.2.m2.2.2.cmml" type="integer" xref="S3.SS0.SSS0.Px1.p1.2.m2.2.2">2</cn><ci id="S3.SS0.SSS0.Px1.p1.2.m2.3.3.cmml" xref="S3.SS0.SSS0.Px1.p1.2.m2.3.3">…</ci><ci id="S3.SS0.SSS0.Px1.p1.2.m2.4.4.cmml" xref="S3.SS0.SSS0.Px1.p1.2.m2.4.4">𝑇</ci></list></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px1.p1.2.m2.6c">\{\text{Price}_{t}|t=1,2,\dots,T\}</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px1.p1.2.m2.6d">{ Price start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT | italic_t = 1 , 2 , … , italic_T }</annotation></semantics></math>.
Typically, the price movement trend is defined as the future change (return) of the asset price <cite class="ltx_cite ltx_citemacro_cite">De Prado (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib7" title="">2018</a>)</cite>. In this paper, we adopt a fixed-time horizon method to define the asset price trend. Concretely, the label <math alttext="y_{t}" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px1.p1.3.m3.1"><semantics id="S3.SS0.SSS0.Px1.p1.3.m3.1a"><msub id="S3.SS0.SSS0.Px1.p1.3.m3.1.1" xref="S3.SS0.SSS0.Px1.p1.3.m3.1.1.cmml"><mi id="S3.SS0.SSS0.Px1.p1.3.m3.1.1.2" xref="S3.SS0.SSS0.Px1.p1.3.m3.1.1.2.cmml">y</mi><mi id="S3.SS0.SSS0.Px1.p1.3.m3.1.1.3" xref="S3.SS0.SSS0.Px1.p1.3.m3.1.1.3.cmml">t</mi></msub><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px1.p1.3.m3.1b"><apply id="S3.SS0.SSS0.Px1.p1.3.m3.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.3.m3.1.1"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.3.m3.1.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.3.m3.1.1">subscript</csymbol><ci id="S3.SS0.SSS0.Px1.p1.3.m3.1.1.2.cmml" xref="S3.SS0.SSS0.Px1.p1.3.m3.1.1.2">𝑦</ci><ci id="S3.SS0.SSS0.Px1.p1.3.m3.1.1.3.cmml" xref="S3.SS0.SSS0.Px1.p1.3.m3.1.1.3">𝑡</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px1.p1.3.m3.1c">y_{t}</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px1.p1.3.m3.1d">italic_y start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT</annotation></semantics></math> indicates whether the return of the asset price over a time horizon <math alttext="\Delta" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px1.p1.4.m4.1"><semantics id="S3.SS0.SSS0.Px1.p1.4.m4.1a"><mi id="S3.SS0.SSS0.Px1.p1.4.m4.1.1" mathvariant="normal" xref="S3.SS0.SSS0.Px1.p1.4.m4.1.1.cmml">Δ</mi><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px1.p1.4.m4.1b"><ci id="S3.SS0.SSS0.Px1.p1.4.m4.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.4.m4.1.1">Δ</ci></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px1.p1.4.m4.1c">\Delta</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px1.p1.4.m4.1d">roman_Δ</annotation></semantics></math> exceeds a certain pre-defined threshold <math alttext="\lambda" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px1.p1.5.m5.1"><semantics id="S3.SS0.SSS0.Px1.p1.5.m5.1a"><mi id="S3.SS0.SSS0.Px1.p1.5.m5.1.1" xref="S3.SS0.SSS0.Px1.p1.5.m5.1.1.cmml">λ</mi><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px1.p1.5.m5.1b"><ci id="S3.SS0.SSS0.Px1.p1.5.m5.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.5.m5.1.1">𝜆</ci></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px1.p1.5.m5.1c">\lambda</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px1.p1.5.m5.1d">italic_λ</annotation></semantics></math>. For long positions:</p>
<table class="ltx_equation ltx_eqn_table" id="S3.E1">
<tbody><tr class="ltx_equation ltx_eqn_row ltx_align_baseline">
<td class="ltx_eqn_cell ltx_eqn_center_padleft"></td>
<td class="ltx_eqn_cell ltx_align_center"><math alttext="y_{t}=\left\{\begin{array}[]{cl}1,&amp;\text{Price}_{t+\Delta}/\text{Price}_{t}-1&gt;%
\lambda\\
0,&amp;\text{otherwise}\end{array}\right.." class="ltx_Math" display="block" id="S3.E1.m1.3"><semantics id="S3.E1.m1.3a"><mrow id="S3.E1.m1.3.3.1" xref="S3.E1.m1.*******.cmml"><mrow id="S3.E1.m1.*******" xref="S3.E1.m1.*******.cmml"><msub id="S3.E1.m1.*******.2" xref="S3.E1.m1.*******.2.cmml"><mi id="S3.E1.m1.3.3.*******" xref="S3.E1.m1.3.3.*******.cmml">y</mi><mi id="S3.E1.m1.3.3.*******" xref="S3.E1.m1.3.3.*******.cmml">t</mi></msub><mo id="S3.E1.m1.*******.1" xref="S3.E1.m1.*******.1.cmml">=</mo><mrow id="S3.E1.m1.3.3.*******" xref="S3.E1.m1.3.3.*******.cmml"><mo id="S3.E1.m1.3.3.*******.1" xref="S3.E1.m1.3.3.*******.1.cmml">{</mo><mtable columnspacing="5pt" displaystyle="true" id="S3.E1.m1.2.2" rowspacing="0pt" xref="S3.E1.m1.2.2.cmml"><mtr id="S3.E1.m1.2.2a" xref="S3.E1.m1.2.2.cmml"><mtd id="S3.E1.m1.2.2b" xref="S3.E1.m1.2.2.cmml"><mrow id="S3.E1.m*******.1.1.3" xref="S3.E1.m1.2.2.cmml"><mn id="S3.E1.m*******.1.1.1" xref="S3.E1.m*******.1.1.1.cmml">1</mn><mo id="S3.E1.m*******.*******" xref="S3.E1.m1.2.2.cmml">,</mo></mrow></mtd><mtd class="ltx_align_left" columnalign="left" id="S3.E1.m1.2.2c" xref="S3.E1.m1.2.2.cmml"><mrow id="S3.E1.m1.1.*******" xref="S3.E1.m1.1.*******.cmml"><mrow id="S3.E1.m1.1.*******.2" xref="S3.E1.m1.1.*******.2.cmml"><mrow id="S3.E1.m1.1.*******.2.2" xref="S3.E1.m1.1.*******.2.2.cmml"><msub id="S3.E1.m1.1.*******.2.2.2" xref="S3.E1.m1.1.*******.2.2.2.cmml"><mtext id="S3.E1.m1.1.*******.*******" xref="S3.E1.m1.1.*******.*******a.cmml">Price</mtext><mrow id="S3.E1.m1.1.*******.*******" xref="S3.E1.m1.1.*******.*******.cmml"><mi id="S3.E1.m1.1.*******.2.*******" xref="S3.E1.m1.1.*******.2.*******.cmml">t</mi><mo id="S3.E1.m1.1.*******.2.*******" xref="S3.E1.m1.1.*******.2.*******.cmml">+</mo><mi id="S3.E1.m1.1.*******.2.*******" mathvariant="normal" xref="S3.E1.m1.1.*******.2.*******.cmml">Δ</mi></mrow></msub><mo id="S3.E1.m1.1.*******.2.2.1" xref="S3.E1.m1.1.*******.2.2.1.cmml">/</mo><msub id="S3.E1.m1.1.*******.2.2.3" xref="S3.E1.m1.1.*******.2.2.3.cmml"><mtext id="S3.E1.m1.1.*******.*******" xref="S3.E1.m1.1.*******.*******a.cmml">Price</mtext><mi id="S3.E1.m1.1.*******.*******" xref="S3.E1.m1.1.*******.*******.cmml">t</mi></msub></mrow><mo id="S3.E1.m1.1.*******.2.1" xref="S3.E1.m1.1.*******.2.1.cmml">−</mo><mn id="S3.E1.m1.1.*******.2.3" xref="S3.E1.m1.1.*******.2.3.cmml">1</mn></mrow><mo id="S3.E1.m1.1.*******.1" xref="S3.E1.m1.1.*******.1.cmml">&gt;</mo><mi id="S3.E1.m1.1.*******.3" xref="S3.E1.m1.1.*******.3.cmml">λ</mi></mrow></mtd></mtr><mtr id="S3.E1.m1.2.2d" xref="S3.E1.m1.2.2.cmml"><mtd id="S3.E1.m1.2.2e" xref="S3.E1.m1.2.2.cmml"><mrow id="S3.E1.m1.2.*******.3" xref="S3.E1.m1.2.2.cmml"><mn id="S3.E1.m1.2.*******.1" xref="S3.E1.m1.2.*******.1.cmml">0</mn><mo id="S3.E1.m1.2.*******.3.1" xref="S3.E1.m1.2.2.cmml">,</mo></mrow></mtd><mtd class="ltx_align_left" columnalign="left" id="S3.E1.m1.2.2f" xref="S3.E1.m1.2.2.cmml"><mtext id="S3.E1.m1.2.*******" xref="S3.E1.m1.2.*******a.cmml">otherwise</mtext></mtd></mtr></mtable><mi id="S3.E1.m1.3.3.*******.2" xref="S3.E1.m1.3.3.*******.1.cmml"></mi></mrow></mrow><mo id="S3.E1.m1.*******" lspace="0em" xref="S3.E1.m1.*******.cmml">.</mo></mrow><annotation-xml encoding="MathML-Content" id="S3.E1.m1.3b"><apply id="S3.E1.m1.*******.cmml" xref="S3.E1.m1.3.3.1"><eq id="S3.E1.m1.*******.1.cmml" xref="S3.E1.m1.*******.1"></eq><apply id="S3.E1.m1.*******.2.cmml" xref="S3.E1.m1.*******.2"><csymbol cd="ambiguous" id="S3.E1.m1.3.3.*******.cmml" xref="S3.E1.m1.*******.2">subscript</csymbol><ci id="S3.E1.m1.3.3.*******.cmml" xref="S3.E1.m1.3.3.*******">𝑦</ci><ci id="S3.E1.m1.3.3.*******.cmml" xref="S3.E1.m1.3.3.*******">𝑡</ci></apply><apply id="S3.E1.m1.3.3.*******.cmml" xref="S3.E1.m1.3.3.*******"><csymbol cd="latexml" id="S3.E1.m1.3.3.*******.1.cmml" xref="S3.E1.m1.3.3.*******.1">cases</csymbol><matrix id="S3.E1.m1.2.2.cmml" xref="S3.E1.m1.2.2"><matrixrow id="S3.E1.m1.2.2a.cmml" xref="S3.E1.m1.2.2"><cn id="S3.E1.m*******.1.1.1.cmml" type="integer" xref="S3.E1.m*******.1.1.1">1</cn><apply id="S3.E1.m1.1.*******.cmml" xref="S3.E1.m1.1.*******"><gt id="S3.E1.m1.1.*******.1.cmml" xref="S3.E1.m1.1.*******.1"></gt><apply id="S3.E1.m1.1.*******.2.cmml" xref="S3.E1.m1.1.*******.2"><minus id="S3.E1.m1.1.*******.2.1.cmml" xref="S3.E1.m1.1.*******.2.1"></minus><apply id="S3.E1.m1.1.*******.2.2.cmml" xref="S3.E1.m1.1.*******.2.2"><divide id="S3.E1.m1.1.*******.2.2.1.cmml" xref="S3.E1.m1.1.*******.2.2.1"></divide><apply id="S3.E1.m1.1.*******.2.2.2.cmml" xref="S3.E1.m1.1.*******.2.2.2"><csymbol cd="ambiguous" id="S3.E1.m1.1.*******.*******.cmml" xref="S3.E1.m1.1.*******.2.2.2">subscript</csymbol><ci id="S3.E1.m1.1.*******.*******a.cmml" xref="S3.E1.m1.1.*******.*******"><mtext id="S3.E1.m1.1.*******.*******.cmml" xref="S3.E1.m1.1.*******.*******">Price</mtext></ci><apply id="S3.E1.m1.1.*******.*******.cmml" xref="S3.E1.m1.1.*******.*******"><plus id="S3.E1.m1.1.*******.2.*******.cmml" xref="S3.E1.m1.1.*******.2.*******"></plus><ci id="S3.E1.m1.1.*******.2.*******.cmml" xref="S3.E1.m1.1.*******.2.*******">𝑡</ci><ci id="S3.E1.m1.1.*******.2.*******.cmml" xref="S3.E1.m1.1.*******.2.*******">Δ</ci></apply></apply><apply id="S3.E1.m1.1.*******.2.2.3.cmml" xref="S3.E1.m1.1.*******.2.2.3"><csymbol cd="ambiguous" id="S3.E1.m1.1.*******.*******.cmml" xref="S3.E1.m1.1.*******.2.2.3">subscript</csymbol><ci id="S3.E1.m1.1.*******.*******a.cmml" xref="S3.E1.m1.1.*******.*******"><mtext id="S3.E1.m1.1.*******.*******.cmml" xref="S3.E1.m1.1.*******.*******">Price</mtext></ci><ci id="S3.E1.m1.1.*******.*******.cmml" xref="S3.E1.m1.1.*******.*******">𝑡</ci></apply></apply><cn id="S3.E1.m1.1.*******.2.3.cmml" type="integer" xref="S3.E1.m1.1.*******.2.3">1</cn></apply><ci id="S3.E1.m1.1.*******.3.cmml" xref="S3.E1.m1.1.*******.3">𝜆</ci></apply></matrixrow><matrixrow id="S3.E1.m1.2.2b.cmml" xref="S3.E1.m1.2.2"><cn id="S3.E1.m1.2.*******.1.cmml" type="integer" xref="S3.E1.m1.2.*******.1">0</cn><ci id="S3.E1.m1.2.*******a.cmml" xref="S3.E1.m1.2.*******"><mtext id="S3.E1.m1.2.*******.cmml" xref="S3.E1.m1.2.*******">otherwise</mtext></ci></matrixrow></matrix></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.E1.m1.3c">y_{t}=\left\{\begin{array}[]{cl}1,&amp;\text{Price}_{t+\Delta}/\text{Price}_{t}-1&gt;%
\lambda\\
0,&amp;\text{otherwise}\end{array}\right..</annotation><annotation encoding="application/x-llamapun" id="S3.E1.m1.3d">italic_y start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT = { start_ARRAY start_ROW start_CELL 1 , end_CELL start_CELL Price start_POSTSUBSCRIPT italic_t + roman_Δ end_POSTSUBSCRIPT / Price start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT - 1 &gt; italic_λ end_CELL end_ROW start_ROW start_CELL 0 , end_CELL start_CELL otherwise end_CELL end_ROW end_ARRAY .</annotation></semantics></math></td>
<td class="ltx_eqn_cell ltx_eqn_center_padright"></td>
<td class="ltx_eqn_cell ltx_eqn_eqno ltx_align_middle ltx_align_right" rowspan="1"><span class="ltx_tag ltx_tag_equation ltx_align_right">(1)</span></td>
</tr></tbody>
</table>
<p class="ltx_p" id="S3.SS0.SSS0.Px1.p1.15">Similarly, for short positions, we set the label 1 if <math alttext="\text{Price}_{t+\Delta}/\text{Price}_{t}-1&lt;-\lambda" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px1.p1.6.m1.1"><semantics id="S3.SS0.SSS0.Px1.p1.6.m1.1a"><mrow id="S3.SS0.SSS0.Px1.p1.6.m1.1.1" xref="S3.SS0.SSS0.Px1.p1.6.m1.1.1.cmml"><mrow id="S3.SS0.SSS0.Px1.p1.6.m*******" xref="S3.SS0.SSS0.Px1.p1.6.m*******.cmml"><mrow id="S3.SS0.SSS0.Px1.p1.6.m1.*******" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.cmml"><msub id="S3.SS0.SSS0.Px1.p1.6.m1.*******.2" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.cmml"><mtext id="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.2" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.2a.cmml">Price</mtext><mrow id="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.3" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.3.cmml"><mi id="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.3.2" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.3.2.cmml">t</mi><mo id="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.3.1" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.3.1.cmml">+</mo><mi id="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.3.3" mathvariant="normal" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.3.3.cmml">Δ</mi></mrow></msub><mo id="S3.SS0.SSS0.Px1.p1.6.m1.*******.1" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.1.cmml">/</mo><msub id="S3.SS0.SSS0.Px1.p1.6.m1.*******.3" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.3.cmml"><mtext id="S3.SS0.SSS0.Px1.p1.6.m1.*******.3.2" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.3.2a.cmml">Price</mtext><mi id="S3.SS0.SSS0.Px1.p1.6.m1.*******.3.3" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.3.3.cmml">t</mi></msub></mrow><mo id="S3.SS0.SSS0.Px1.p1.6.m1.*******" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.cmml">−</mo><mn id="S3.SS0.SSS0.Px1.p1.6.m1.*******" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.cmml">1</mn></mrow><mo id="S3.SS0.SSS0.Px1.p1.6.m*******" xref="S3.SS0.SSS0.Px1.p1.6.m*******.cmml">&lt;</mo><mrow id="S3.SS0.SSS0.Px1.p1.6.m*******" xref="S3.SS0.SSS0.Px1.p1.6.m*******.cmml"><mo id="S3.SS0.SSS0.Px1.p1.6.m*******a" xref="S3.SS0.SSS0.Px1.p1.6.m*******.cmml">−</mo><mi id="S3.SS0.SSS0.Px1.p1.6.m1.*******" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.cmml">λ</mi></mrow></mrow><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px1.p1.6.m1.1b"><apply id="S3.SS0.SSS0.Px1.p1.6.m1.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m1.1.1"><lt id="S3.SS0.SSS0.Px1.p1.6.m*******.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m*******"></lt><apply id="S3.SS0.SSS0.Px1.p1.6.m*******.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m*******"><minus id="S3.SS0.SSS0.Px1.p1.6.m1.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******"></minus><apply id="S3.SS0.SSS0.Px1.p1.6.m1.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******"><divide id="S3.SS0.SSS0.Px1.p1.6.m1.*******.1.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.1"></divide><apply id="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.2"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.1.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.2">subscript</csymbol><ci id="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.2a.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.2"><mtext id="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.2.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.2">Price</mtext></ci><apply id="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.3.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.3"><plus id="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.3.1.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.3.1"></plus><ci id="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.3.2.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.3.2">𝑡</ci><ci id="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.3.3.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.2.3.3">Δ</ci></apply></apply><apply id="S3.SS0.SSS0.Px1.p1.6.m1.*******.3.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.3"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.6.m1.*******.3.1.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.3">subscript</csymbol><ci id="S3.SS0.SSS0.Px1.p1.6.m1.*******.3.2a.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.3.2"><mtext id="S3.SS0.SSS0.Px1.p1.6.m1.*******.3.2.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.3.2">Price</mtext></ci><ci id="S3.SS0.SSS0.Px1.p1.6.m1.*******.3.3.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******.3.3">𝑡</ci></apply></apply><cn id="S3.SS0.SSS0.Px1.p1.6.m1.*******.cmml" type="integer" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******">1</cn></apply><apply id="S3.SS0.SSS0.Px1.p1.6.m*******.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m*******"><minus id="S3.SS0.SSS0.Px1.p1.6.m1.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m*******"></minus><ci id="S3.SS0.SSS0.Px1.p1.6.m1.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.6.m1.*******">𝜆</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px1.p1.6.m1.1c">\text{Price}_{t+\Delta}/\text{Price}_{t}-1&lt;-\lambda</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px1.p1.6.m1.1d">Price start_POSTSUBSCRIPT italic_t + roman_Δ end_POSTSUBSCRIPT / Price start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT - 1 &lt; - italic_λ</annotation></semantics></math> and 0 otherwise.
At the training step <math alttext="t" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px1.p1.7.m2.1"><semantics id="S3.SS0.SSS0.Px1.p1.7.m2.1a"><mi id="S3.SS0.SSS0.Px1.p1.7.m2.1.1" xref="S3.SS0.SSS0.Px1.p1.7.m2.1.1.cmml">t</mi><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px1.p1.7.m2.1b"><ci id="S3.SS0.SSS0.Px1.p1.7.m2.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.7.m2.1.1">𝑡</ci></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px1.p1.7.m2.1c">t</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px1.p1.7.m2.1d">italic_t</annotation></semantics></math>, we calculate the feature vector <math alttext="{\bm{x}}_{t}\in\mathcal{X}\subseteq\mathbb{R}^{d}" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px1.p1.8.m3.1"><semantics id="S3.SS0.SSS0.Px1.p1.8.m3.1a"><mrow id="S3.SS0.SSS0.Px1.p1.8.m3.1.1" xref="S3.SS0.SSS0.Px1.p1.8.m3.1.1.cmml"><msub id="S3.SS0.SSS0.Px1.p1.8.m3.1.1.2" xref="S3.SS0.SSS0.Px1.p1.8.m3.1.1.2.cmml"><mi id="S3.SS0.SSS0.Px1.p1.8.m3.*******" xref="S3.SS0.SSS0.Px1.p1.8.m3.*******.cmml">𝒙</mi><mi id="S3.SS0.SSS0.Px1.p1.8.m3.*******" xref="S3.SS0.SSS0.Px1.p1.8.m3.*******.cmml">t</mi></msub><mo id="S3.SS0.SSS0.Px1.p1.8.m3.1.1.3" xref="S3.SS0.SSS0.Px1.p1.8.m3.1.1.3.cmml">∈</mo><mi class="ltx_font_mathcaligraphic" id="S3.SS0.SSS0.Px1.p1.8.m3.1.1.4" xref="S3.SS0.SSS0.Px1.p1.8.m3.1.1.4.cmml">𝒳</mi><mo id="S3.SS0.SSS0.Px1.p1.8.m3.1.1.5" xref="S3.SS0.SSS0.Px1.p1.8.m3.1.1.5.cmml">⊆</mo><msup id="S3.SS0.SSS0.Px1.p1.8.m3.1.1.6" xref="S3.SS0.SSS0.Px1.p1.8.m3.1.1.6.cmml"><mi id="S3.SS0.SSS0.Px1.p1.8.m3.*******" xref="S3.SS0.SSS0.Px1.p1.8.m3.*******.cmml">ℝ</mi><mi id="S3.SS0.SSS0.Px1.p1.8.m3.*******" xref="S3.SS0.SSS0.Px1.p1.8.m3.*******.cmml">d</mi></msup></mrow><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px1.p1.8.m3.1b"><apply id="S3.SS0.SSS0.Px1.p1.8.m3.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.8.m3.1.1"><and id="S3.SS0.SSS0.Px1.p1.8.m3.1.1a.cmml" xref="S3.SS0.SSS0.Px1.p1.8.m3.1.1"></and><apply id="S3.SS0.SSS0.Px1.p1.8.m3.1.1b.cmml" xref="S3.SS0.SSS0.Px1.p1.8.m3.1.1"><in id="S3.SS0.SSS0.Px1.p1.8.m3.1.1.3.cmml" xref="S3.SS0.SSS0.Px1.p1.8.m3.1.1.3"></in><apply id="S3.SS0.SSS0.Px1.p1.8.m3.1.1.2.cmml" xref="S3.SS0.SSS0.Px1.p1.8.m3.1.1.2"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.8.m3.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.8.m3.1.1.2">subscript</csymbol><ci id="S3.SS0.SSS0.Px1.p1.8.m3.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.8.m3.*******">𝒙</ci><ci id="S3.SS0.SSS0.Px1.p1.8.m3.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.8.m3.*******">𝑡</ci></apply><ci id="S3.SS0.SSS0.Px1.p1.8.m3.1.1.4.cmml" xref="S3.SS0.SSS0.Px1.p1.8.m3.1.1.4">𝒳</ci></apply><apply id="S3.SS0.SSS0.Px1.p1.8.m3.1.1c.cmml" xref="S3.SS0.SSS0.Px1.p1.8.m3.1.1"><subset id="S3.SS0.SSS0.Px1.p1.8.m3.1.1.5.cmml" xref="S3.SS0.SSS0.Px1.p1.8.m3.1.1.5"></subset><share href="https://arxiv.org/html/2107.11972v4#S3.SS0.SSS0.Px1.p1.8.m3.1.1.4.cmml" id="S3.SS0.SSS0.Px1.p1.8.m3.1.1d.cmml" xref="S3.SS0.SSS0.Px1.p1.8.m3.1.1"></share><apply id="S3.SS0.SSS0.Px1.p1.8.m3.1.1.6.cmml" xref="S3.SS0.SSS0.Px1.p1.8.m3.1.1.6"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.8.m3.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.8.m3.1.1.6">superscript</csymbol><ci id="S3.SS0.SSS0.Px1.p1.8.m3.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.8.m3.*******">ℝ</ci><ci id="S3.SS0.SSS0.Px1.p1.8.m3.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.8.m3.*******">𝑑</ci></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px1.p1.8.m3.1c">{\bm{x}}_{t}\in\mathcal{X}\subseteq\mathbb{R}^{d}</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px1.p1.8.m3.1d">bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT ∈ caligraphic_X ⊆ blackboard_R start_POSTSUPERSCRIPT italic_d end_POSTSUPERSCRIPT</annotation></semantics></math> based on price and volume information up to time <math alttext="t" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px1.p1.9.m4.1"><semantics id="S3.SS0.SSS0.Px1.p1.9.m4.1a"><mi id="S3.SS0.SSS0.Px1.p1.9.m4.1.1" xref="S3.SS0.SSS0.Px1.p1.9.m4.1.1.cmml">t</mi><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px1.p1.9.m4.1b"><ci id="S3.SS0.SSS0.Px1.p1.9.m4.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.9.m4.1.1">𝑡</ci></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px1.p1.9.m4.1c">t</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px1.p1.9.m4.1d">italic_t</annotation></semantics></math>, and its label <math alttext="y_{t}\in\mathcal{Y}=\{0,1\}" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px1.p1.10.m5.2"><semantics id="S3.SS0.SSS0.Px1.p1.10.m5.2a"><mrow id="S3.SS0.SSS0.Px1.p1.10.m5.2.3" xref="S3.SS0.SSS0.Px1.p1.10.m5.2.3.cmml"><msub id="S3.SS0.SSS0.Px1.p1.10.m5.2.3.2" xref="S3.SS0.SSS0.Px1.p1.10.m5.2.3.2.cmml"><mi id="S3.SS0.SSS0.Px1.p1.10.m5.*******" xref="S3.SS0.SSS0.Px1.p1.10.m5.*******.cmml">y</mi><mi id="S3.SS0.SSS0.Px1.p1.10.m5.*******" xref="S3.SS0.SSS0.Px1.p1.10.m5.*******.cmml">t</mi></msub><mo id="S3.SS0.SSS0.Px1.p1.10.m5.2.3.3" xref="S3.SS0.SSS0.Px1.p1.10.m5.2.3.3.cmml">∈</mo><mi class="ltx_font_mathcaligraphic" id="S3.SS0.SSS0.Px1.p1.10.m5.2.3.4" xref="S3.SS0.SSS0.Px1.p1.10.m5.2.3.4.cmml">𝒴</mi><mo id="S3.SS0.SSS0.Px1.p1.10.m5.2.3.5" xref="S3.SS0.SSS0.Px1.p1.10.m5.2.3.5.cmml">=</mo><mrow id="S3.SS0.SSS0.Px1.p1.10.m5.*******" xref="S3.SS0.SSS0.Px1.p1.10.m5.*******.cmml"><mo id="S3.SS0.SSS0.Px1.p1.10.m5.*******.1" stretchy="false" xref="S3.SS0.SSS0.Px1.p1.10.m5.*******.cmml">{</mo><mn id="S3.SS0.SSS0.Px1.p1.10.m5.1.1" xref="S3.SS0.SSS0.Px1.p1.10.m5.1.1.cmml">0</mn><mo id="S3.SS0.SSS0.Px1.p1.10.m5.*******.2" xref="S3.SS0.SSS0.Px1.p1.10.m5.*******.cmml">,</mo><mn id="S3.SS0.SSS0.Px1.p1.10.m5.2.2" xref="S3.SS0.SSS0.Px1.p1.10.m5.2.2.cmml">1</mn><mo id="S3.SS0.SSS0.Px1.p1.10.m5.*******.3" stretchy="false" xref="S3.SS0.SSS0.Px1.p1.10.m5.*******.cmml">}</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px1.p1.10.m5.2b"><apply id="S3.SS0.SSS0.Px1.p1.10.m5.2.3.cmml" xref="S3.SS0.SSS0.Px1.p1.10.m5.2.3"><and id="S3.SS0.SSS0.Px1.p1.10.m5.2.3a.cmml" xref="S3.SS0.SSS0.Px1.p1.10.m5.2.3"></and><apply id="S3.SS0.SSS0.Px1.p1.10.m5.2.3b.cmml" xref="S3.SS0.SSS0.Px1.p1.10.m5.2.3"><in id="S3.SS0.SSS0.Px1.p1.10.m5.2.3.3.cmml" xref="S3.SS0.SSS0.Px1.p1.10.m5.2.3.3"></in><apply id="S3.SS0.SSS0.Px1.p1.10.m5.2.3.2.cmml" xref="S3.SS0.SSS0.Px1.p1.10.m5.2.3.2"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.10.m5.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.10.m5.2.3.2">subscript</csymbol><ci id="S3.SS0.SSS0.Px1.p1.10.m5.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.10.m5.*******">𝑦</ci><ci id="S3.SS0.SSS0.Px1.p1.10.m5.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.10.m5.*******">𝑡</ci></apply><ci id="S3.SS0.SSS0.Px1.p1.10.m5.2.3.4.cmml" xref="S3.SS0.SSS0.Px1.p1.10.m5.2.3.4">𝒴</ci></apply><apply id="S3.SS0.SSS0.Px1.p1.10.m5.2.3c.cmml" xref="S3.SS0.SSS0.Px1.p1.10.m5.2.3"><eq id="S3.SS0.SSS0.Px1.p1.10.m5.2.3.5.cmml" xref="S3.SS0.SSS0.Px1.p1.10.m5.2.3.5"></eq><share href="https://arxiv.org/html/2107.11972v4#S3.SS0.SSS0.Px1.p1.10.m5.2.3.4.cmml" id="S3.SS0.SSS0.Px1.p1.10.m5.2.3d.cmml" xref="S3.SS0.SSS0.Px1.p1.10.m5.2.3"></share><set id="S3.SS0.SSS0.Px1.p1.10.m5.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.10.m5.*******"><cn id="S3.SS0.SSS0.Px1.p1.10.m5.1.1.cmml" type="integer" xref="S3.SS0.SSS0.Px1.p1.10.m5.1.1">0</cn><cn id="S3.SS0.SSS0.Px1.p1.10.m5.2.2.cmml" type="integer" xref="S3.SS0.SSS0.Px1.p1.10.m5.2.2">1</cn></set></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px1.p1.10.m5.2c">y_{t}\in\mathcal{Y}=\{0,1\}</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px1.p1.10.m5.2d">italic_y start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT ∈ caligraphic_Y = { 0 , 1 }</annotation></semantics></math>.
We denote the training set as <math alttext="{\bm{X}}=[{\bm{x}}_{1},\cdots,{\bm{x}}_{N}]^{T}\in\mathbb{R}^{N\times d}" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px1.p1.11.m6.3"><semantics id="S3.SS0.SSS0.Px1.p1.11.m6.3a"><mrow id="S3.SS0.SSS0.Px1.p1.11.m6.3.3" xref="S3.SS0.SSS0.Px1.p1.11.m6.3.3.cmml"><mi id="S3.SS0.SSS0.Px1.p1.11.m6.3.3.4" xref="S3.SS0.SSS0.Px1.p1.11.m6.3.3.4.cmml">𝑿</mi><mo id="S3.SS0.SSS0.Px1.p1.11.m6.3.3.5" xref="S3.SS0.SSS0.Px1.p1.11.m6.3.3.5.cmml">=</mo><msup id="S3.SS0.SSS0.Px1.p1.11.m6.3.3.2" xref="S3.SS0.SSS0.Px1.p1.11.m6.3.3.2.cmml"><mrow id="S3.SS0.SSS0.Px1.p1.11.m6.*******.2" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.3.cmml"><mo id="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.3" stretchy="false" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.3.cmml">[</mo><msub id="S3.SS0.SSS0.Px1.p1.11.m6.*******.1.1" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.1.1.cmml"><mi id="S3.SS0.SSS0.Px1.p1.11.m6.*******.1.1.2" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.1.1.2.cmml">𝒙</mi><mn id="S3.SS0.SSS0.Px1.p1.11.m6.*******.1.1.3" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.1.1.3.cmml">1</mn></msub><mo id="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.4" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.3.cmml">,</mo><mi id="S3.SS0.SSS0.Px1.p1.11.m6.1.1" mathvariant="normal" xref="S3.SS0.SSS0.Px1.p1.11.m6.1.1.cmml">⋯</mi><mo id="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.5" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.3.cmml">,</mo><msub id="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.2" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.2.cmml"><mi id="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.2.2" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.2.2.cmml">𝒙</mi><mi id="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.2.3" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.2.3.cmml">N</mi></msub><mo id="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.6" stretchy="false" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.3.cmml">]</mo></mrow><mi id="S3.SS0.SSS0.Px1.p1.11.m6.*******" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.cmml">T</mi></msup><mo id="S3.SS0.SSS0.Px1.p1.11.m6.3.3.6" xref="S3.SS0.SSS0.Px1.p1.11.m6.3.3.6.cmml">∈</mo><msup id="S3.SS0.SSS0.Px1.p1.11.m6.3.3.7" xref="S3.SS0.SSS0.Px1.p1.11.m6.3.3.7.cmml"><mi id="S3.SS0.SSS0.Px1.p1.11.m6.*******" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.cmml">ℝ</mi><mrow id="S3.SS0.SSS0.Px1.p1.11.m6.*******" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.cmml"><mi id="S3.SS0.SSS0.Px1.p1.11.m6.*******.2" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.cmml">N</mi><mo id="S3.SS0.SSS0.Px1.p1.11.m6.*******.1" lspace="0.222em" rspace="0.222em" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.1.cmml">×</mo><mi id="S3.SS0.SSS0.Px1.p1.11.m6.*******.3" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.3.cmml">d</mi></mrow></msup></mrow><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px1.p1.11.m6.3b"><apply id="S3.SS0.SSS0.Px1.p1.11.m6.3.3.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.3.3"><and id="S3.SS0.SSS0.Px1.p1.11.m6.3.3a.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.3.3"></and><apply id="S3.SS0.SSS0.Px1.p1.11.m6.3.3b.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.3.3"><eq id="S3.SS0.SSS0.Px1.p1.11.m6.3.3.5.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.3.3.5"></eq><ci id="S3.SS0.SSS0.Px1.p1.11.m6.3.3.4.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.3.3.4">𝑿</ci><apply id="S3.SS0.SSS0.Px1.p1.11.m6.3.3.2.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.3.3.2"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.11.m6.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.3.3.2">superscript</csymbol><list id="S3.SS0.SSS0.Px1.p1.11.m6.*******.3.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.2"><apply id="S3.SS0.SSS0.Px1.p1.11.m6.*******.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.1.1"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.11.m6.*******.1.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.1.1">subscript</csymbol><ci id="S3.SS0.SSS0.Px1.p1.11.m6.*******.1.1.2.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.1.1.2">𝒙</ci><cn id="S3.SS0.SSS0.Px1.p1.11.m6.*******.1.1.3.cmml" type="integer" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.1.1.3">1</cn></apply><ci id="S3.SS0.SSS0.Px1.p1.11.m6.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.1.1">⋯</ci><apply id="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.2.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.2"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.2.1.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.2">subscript</csymbol><ci id="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.2.2.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.2.2">𝒙</ci><ci id="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.2.3.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.2.3">𝑁</ci></apply></list><ci id="S3.SS0.SSS0.Px1.p1.11.m6.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******">𝑇</ci></apply></apply><apply id="S3.SS0.SSS0.Px1.p1.11.m6.3.3c.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.3.3"><in id="S3.SS0.SSS0.Px1.p1.11.m6.3.3.6.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.3.3.6"></in><share href="https://arxiv.org/html/2107.11972v4#S3.SS0.SSS0.Px1.p1.11.m6.3.3.2.cmml" id="S3.SS0.SSS0.Px1.p1.11.m6.3.3d.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.3.3"></share><apply id="S3.SS0.SSS0.Px1.p1.11.m6.3.3.7.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.3.3.7"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.11.m6.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.3.3.7">superscript</csymbol><ci id="S3.SS0.SSS0.Px1.p1.11.m6.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******">ℝ</ci><apply id="S3.SS0.SSS0.Px1.p1.11.m6.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******"><times id="S3.SS0.SSS0.Px1.p1.11.m6.*******.1.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.1"></times><ci id="S3.SS0.SSS0.Px1.p1.11.m6.*******.2.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.2">𝑁</ci><ci id="S3.SS0.SSS0.Px1.p1.11.m6.*******.3.cmml" xref="S3.SS0.SSS0.Px1.p1.11.m6.*******.3">𝑑</ci></apply></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px1.p1.11.m6.3c">{\bm{X}}=[{\bm{x}}_{1},\cdots,{\bm{x}}_{N}]^{T}\in\mathbb{R}^{N\times d}</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px1.p1.11.m6.3d">bold_italic_X = [ bold_italic_x start_POSTSUBSCRIPT 1 end_POSTSUBSCRIPT , ⋯ , bold_italic_x start_POSTSUBSCRIPT italic_N end_POSTSUBSCRIPT ] start_POSTSUPERSCRIPT italic_T end_POSTSUPERSCRIPT ∈ blackboard_R start_POSTSUPERSCRIPT italic_N × italic_d end_POSTSUPERSCRIPT</annotation></semantics></math> associated with its label <math alttext="{\bm{y}}=[y_{1},\cdots,y_{N}]^{T}\in\mathbb{R}^{N}" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px1.p1.12.m7.3"><semantics id="S3.SS0.SSS0.Px1.p1.12.m7.3a"><mrow id="S3.SS0.SSS0.Px1.p1.12.m7.3.3" xref="S3.SS0.SSS0.Px1.p1.12.m7.3.3.cmml"><mi id="S3.SS0.SSS0.Px1.p1.12.m7.3.3.4" xref="S3.SS0.SSS0.Px1.p1.12.m7.3.3.4.cmml">𝒚</mi><mo id="S3.SS0.SSS0.Px1.p1.12.m7.3.3.5" xref="S3.SS0.SSS0.Px1.p1.12.m7.3.3.5.cmml">=</mo><msup id="S3.SS0.SSS0.Px1.p1.12.m7.3.3.2" xref="S3.SS0.SSS0.Px1.p1.12.m7.3.3.2.cmml"><mrow id="S3.SS0.SSS0.Px1.p1.12.m7.*******.2" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.3.cmml"><mo id="S3.SS0.SSS0.Px1.p1.12.m7.*******.2.3" stretchy="false" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.3.cmml">[</mo><msub id="S3.SS0.SSS0.Px1.p1.12.m7.*******.1.1" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.1.1.cmml"><mi id="S3.SS0.SSS0.Px1.p1.12.m7.*******.1.1.2" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.1.1.2.cmml">y</mi><mn id="S3.SS0.SSS0.Px1.p1.12.m7.*******.1.1.3" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.1.1.3.cmml">1</mn></msub><mo id="S3.SS0.SSS0.Px1.p1.12.m7.*******.2.4" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.3.cmml">,</mo><mi id="S3.SS0.SSS0.Px1.p1.12.m7.1.1" mathvariant="normal" xref="S3.SS0.SSS0.Px1.p1.12.m7.1.1.cmml">⋯</mi><mo id="S3.SS0.SSS0.Px1.p1.12.m7.*******.2.5" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.3.cmml">,</mo><msub id="S3.SS0.SSS0.Px1.p1.12.m7.*******.2.2" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.2.2.cmml"><mi id="S3.SS0.SSS0.Px1.p1.12.m7.*******.2.2.2" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.2.2.2.cmml">y</mi><mi id="S3.SS0.SSS0.Px1.p1.12.m7.*******.2.2.3" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.2.2.3.cmml">N</mi></msub><mo id="S3.SS0.SSS0.Px1.p1.12.m7.*******.2.6" stretchy="false" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.3.cmml">]</mo></mrow><mi id="S3.SS0.SSS0.Px1.p1.12.m7.*******" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.cmml">T</mi></msup><mo id="S3.SS0.SSS0.Px1.p1.12.m7.3.3.6" xref="S3.SS0.SSS0.Px1.p1.12.m7.3.3.6.cmml">∈</mo><msup id="S3.SS0.SSS0.Px1.p1.12.m7.3.3.7" xref="S3.SS0.SSS0.Px1.p1.12.m7.3.3.7.cmml"><mi id="S3.SS0.SSS0.Px1.p1.12.m7.*******" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.cmml">ℝ</mi><mi id="S3.SS0.SSS0.Px1.p1.12.m7.*******" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.cmml">N</mi></msup></mrow><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px1.p1.12.m7.3b"><apply id="S3.SS0.SSS0.Px1.p1.12.m7.3.3.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.3.3"><and id="S3.SS0.SSS0.Px1.p1.12.m7.3.3a.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.3.3"></and><apply id="S3.SS0.SSS0.Px1.p1.12.m7.3.3b.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.3.3"><eq id="S3.SS0.SSS0.Px1.p1.12.m7.3.3.5.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.3.3.5"></eq><ci id="S3.SS0.SSS0.Px1.p1.12.m7.3.3.4.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.3.3.4">𝒚</ci><apply id="S3.SS0.SSS0.Px1.p1.12.m7.3.3.2.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.3.3.2"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.12.m7.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.3.3.2">superscript</csymbol><list id="S3.SS0.SSS0.Px1.p1.12.m7.*******.3.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.2"><apply id="S3.SS0.SSS0.Px1.p1.12.m7.*******.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.1.1"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.12.m7.*******.1.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.1.1">subscript</csymbol><ci id="S3.SS0.SSS0.Px1.p1.12.m7.*******.1.1.2.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.1.1.2">𝑦</ci><cn id="S3.SS0.SSS0.Px1.p1.12.m7.*******.1.1.3.cmml" type="integer" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.1.1.3">1</cn></apply><ci id="S3.SS0.SSS0.Px1.p1.12.m7.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.1.1">⋯</ci><apply id="S3.SS0.SSS0.Px1.p1.12.m7.*******.2.2.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.2.2"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.12.m7.*******.2.2.1.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.2.2">subscript</csymbol><ci id="S3.SS0.SSS0.Px1.p1.12.m7.*******.2.2.2.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.2.2.2">𝑦</ci><ci id="S3.SS0.SSS0.Px1.p1.12.m7.*******.2.2.3.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******.2.2.3">𝑁</ci></apply></list><ci id="S3.SS0.SSS0.Px1.p1.12.m7.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******">𝑇</ci></apply></apply><apply id="S3.SS0.SSS0.Px1.p1.12.m7.3.3c.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.3.3"><in id="S3.SS0.SSS0.Px1.p1.12.m7.3.3.6.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.3.3.6"></in><share href="https://arxiv.org/html/2107.11972v4#S3.SS0.SSS0.Px1.p1.12.m7.3.3.2.cmml" id="S3.SS0.SSS0.Px1.p1.12.m7.3.3d.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.3.3"></share><apply id="S3.SS0.SSS0.Px1.p1.12.m7.3.3.7.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.3.3.7"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.12.m7.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.3.3.7">superscript</csymbol><ci id="S3.SS0.SSS0.Px1.p1.12.m7.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******">ℝ</ci><ci id="S3.SS0.SSS0.Px1.p1.12.m7.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.12.m7.*******">𝑁</ci></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px1.p1.12.m7.3c">{\bm{y}}=[y_{1},\cdots,y_{N}]^{T}\in\mathbb{R}^{N}</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px1.p1.12.m7.3d">bold_italic_y = [ italic_y start_POSTSUBSCRIPT 1 end_POSTSUBSCRIPT , ⋯ , italic_y start_POSTSUBSCRIPT italic_N end_POSTSUBSCRIPT ] start_POSTSUPERSCRIPT italic_T end_POSTSUPERSCRIPT ∈ blackboard_R start_POSTSUPERSCRIPT italic_N end_POSTSUPERSCRIPT</annotation></semantics></math>, where <math alttext="N" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px1.p1.13.m8.1"><semantics id="S3.SS0.SSS0.Px1.p1.13.m8.1a"><mi id="S3.SS0.SSS0.Px1.p1.13.m8.1.1" xref="S3.SS0.SSS0.Px1.p1.13.m8.1.1.cmml">N</mi><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px1.p1.13.m8.1b"><ci id="S3.SS0.SSS0.Px1.p1.13.m8.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.13.m8.1.1">𝑁</ci></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px1.p1.13.m8.1c">N</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px1.p1.13.m8.1d">italic_N</annotation></semantics></math> is the number of time steps and <math alttext="d" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px1.p1.14.m9.1"><semantics id="S3.SS0.SSS0.Px1.p1.14.m9.1a"><mi id="S3.SS0.SSS0.Px1.p1.14.m9.1.1" xref="S3.SS0.SSS0.Px1.p1.14.m9.1.1.cmml">d</mi><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px1.p1.14.m9.1b"><ci id="S3.SS0.SSS0.Px1.p1.14.m9.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.14.m9.1.1">𝑑</ci></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px1.p1.14.m9.1c">d</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px1.p1.14.m9.1d">italic_d</annotation></semantics></math> is the feature dimension. Similarly, <math alttext="{\bm{X}}_{\text{test}}=[{\bm{x}}_{1},\cdots,{\bm{x}}_{N_{\text{test}}}]^{T}\in%
\mathbb{R}^{N_{\text{test}}\times d}" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px1.p1.15.m10.3"><semantics id="S3.SS0.SSS0.Px1.p1.15.m10.3a"><mrow id="S3.SS0.SSS0.Px1.p1.15.m10.3.3" xref="S3.SS0.SSS0.Px1.p1.15.m10.3.3.cmml"><msub id="S3.SS0.SSS0.Px1.p1.15.m10.3.3.4" xref="S3.SS0.SSS0.Px1.p1.15.m10.3.3.4.cmml"><mi id="S3.SS0.SSS0.Px1.p1.15.m10.*******" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.cmml">𝑿</mi><mtext id="S3.SS0.SSS0.Px1.p1.15.m10.*******" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******a.cmml">test</mtext></msub><mo id="S3.SS0.SSS0.Px1.p1.15.m10.3.3.5" xref="S3.SS0.SSS0.Px1.p1.15.m10.3.3.5.cmml">=</mo><msup id="S3.SS0.SSS0.Px1.p1.15.m10.3.3.2" xref="S3.SS0.SSS0.Px1.p1.15.m10.3.3.2.cmml"><mrow id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.3.cmml"><mo id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.3" stretchy="false" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.3.cmml">[</mo><msub id="S3.SS0.SSS0.Px1.p1.15.m10.*******.1.1" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.1.1.cmml"><mi id="S3.SS0.SSS0.Px1.p1.15.m10.*******.1.1.2" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.1.1.2.cmml">𝒙</mi><mn id="S3.SS0.SSS0.Px1.p1.15.m10.*******.1.1.3" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.1.1.3.cmml">1</mn></msub><mo id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.4" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.3.cmml">,</mo><mi id="S3.SS0.SSS0.Px1.p1.15.m10.1.1" mathvariant="normal" xref="S3.SS0.SSS0.Px1.p1.15.m10.1.1.cmml">⋯</mi><mo id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.5" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.3.cmml">,</mo><msub id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.2" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.2.cmml"><mi id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.2.2" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.2.2.cmml">𝒙</mi><msub id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.2.3" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.2.3.cmml"><mi id="S3.SS0.SSS0.Px1.p1.15.m10.*******.*******" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.*******.cmml">N</mi><mtext id="S3.SS0.SSS0.Px1.p1.15.m10.*******.*******" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.*******a.cmml">test</mtext></msub></msub><mo id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.6" stretchy="false" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.3.cmml">]</mo></mrow><mi id="S3.SS0.SSS0.Px1.p1.15.m10.*******" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.cmml">T</mi></msup><mo id="S3.SS0.SSS0.Px1.p1.15.m10.3.3.6" xref="S3.SS0.SSS0.Px1.p1.15.m10.3.3.6.cmml">∈</mo><msup id="S3.SS0.SSS0.Px1.p1.15.m10.3.3.7" xref="S3.SS0.SSS0.Px1.p1.15.m10.3.3.7.cmml"><mi id="S3.SS0.SSS0.Px1.p1.15.m10.*******" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.cmml">ℝ</mi><mrow id="S3.SS0.SSS0.Px1.p1.15.m10.*******" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.cmml"><msub id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.cmml"><mi id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.2" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.2.cmml">N</mi><mtext id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.3" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.3a.cmml">test</mtext></msub><mo id="S3.SS0.SSS0.Px1.p1.15.m10.*******.1" lspace="0.222em" rspace="0.222em" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.1.cmml">×</mo><mi id="S3.SS0.SSS0.Px1.p1.15.m10.*******.3" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.3.cmml">d</mi></mrow></msup></mrow><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px1.p1.15.m10.3b"><apply id="S3.SS0.SSS0.Px1.p1.15.m10.3.3.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.3.3"><and id="S3.SS0.SSS0.Px1.p1.15.m10.3.3a.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.3.3"></and><apply id="S3.SS0.SSS0.Px1.p1.15.m10.3.3b.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.3.3"><eq id="S3.SS0.SSS0.Px1.p1.15.m10.3.3.5.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.3.3.5"></eq><apply id="S3.SS0.SSS0.Px1.p1.15.m10.3.3.4.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.3.3.4"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.15.m10.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.3.3.4">subscript</csymbol><ci id="S3.SS0.SSS0.Px1.p1.15.m10.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******">𝑿</ci><ci id="S3.SS0.SSS0.Px1.p1.15.m10.*******a.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******"><mtext id="S3.SS0.SSS0.Px1.p1.15.m10.*******.cmml" mathsize="70%" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******">test</mtext></ci></apply><apply id="S3.SS0.SSS0.Px1.p1.15.m10.3.3.2.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.3.3.2"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.15.m10.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.3.3.2">superscript</csymbol><list id="S3.SS0.SSS0.Px1.p1.15.m10.*******.3.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.2"><apply id="S3.SS0.SSS0.Px1.p1.15.m10.*******.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.1.1"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.15.m10.*******.1.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.1.1">subscript</csymbol><ci id="S3.SS0.SSS0.Px1.p1.15.m10.*******.1.1.2.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.1.1.2">𝒙</ci><cn id="S3.SS0.SSS0.Px1.p1.15.m10.*******.1.1.3.cmml" type="integer" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.1.1.3">1</cn></apply><ci id="S3.SS0.SSS0.Px1.p1.15.m10.1.1.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.1.1">⋯</ci><apply id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.2.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.2"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.2.1.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.2">subscript</csymbol><ci id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.2.2.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.2.2">𝒙</ci><apply id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.2.3.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.2.3"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.15.m10.*******.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.2.3">subscript</csymbol><ci id="S3.SS0.SSS0.Px1.p1.15.m10.*******.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.*******">𝑁</ci><ci id="S3.SS0.SSS0.Px1.p1.15.m10.*******.*******a.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.*******"><mtext id="S3.SS0.SSS0.Px1.p1.15.m10.*******.*******.cmml" mathsize="50%" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.*******">test</mtext></ci></apply></apply></list><ci id="S3.SS0.SSS0.Px1.p1.15.m10.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******">𝑇</ci></apply></apply><apply id="S3.SS0.SSS0.Px1.p1.15.m10.3.3c.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.3.3"><in id="S3.SS0.SSS0.Px1.p1.15.m10.3.3.6.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.3.3.6"></in><share href="https://arxiv.org/html/2107.11972v4#S3.SS0.SSS0.Px1.p1.15.m10.3.3.2.cmml" id="S3.SS0.SSS0.Px1.p1.15.m10.3.3d.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.3.3"></share><apply id="S3.SS0.SSS0.Px1.p1.15.m10.3.3.7.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.3.3.7"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.15.m10.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.3.3.7">superscript</csymbol><ci id="S3.SS0.SSS0.Px1.p1.15.m10.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******">ℝ</ci><apply id="S3.SS0.SSS0.Px1.p1.15.m10.*******.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******"><times id="S3.SS0.SSS0.Px1.p1.15.m10.*******.1.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.1"></times><apply id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.2"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.1.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.2">subscript</csymbol><ci id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.2.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.2">𝑁</ci><ci id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.3a.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.3"><mtext id="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.3.cmml" mathsize="50%" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.2.3">test</mtext></ci></apply><ci id="S3.SS0.SSS0.Px1.p1.15.m10.*******.3.cmml" xref="S3.SS0.SSS0.Px1.p1.15.m10.*******.3">𝑑</ci></apply></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px1.p1.15.m10.3c">{\bm{X}}_{\text{test}}=[{\bm{x}}_{1},\cdots,{\bm{x}}_{N_{\text{test}}}]^{T}\in%
\mathbb{R}^{N_{\text{test}}\times d}</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px1.p1.15.m10.3d">bold_italic_X start_POSTSUBSCRIPT test end_POSTSUBSCRIPT = [ bold_italic_x start_POSTSUBSCRIPT 1 end_POSTSUBSCRIPT , ⋯ , bold_italic_x start_POSTSUBSCRIPT italic_N start_POSTSUBSCRIPT test end_POSTSUBSCRIPT end_POSTSUBSCRIPT ] start_POSTSUPERSCRIPT italic_T end_POSTSUPERSCRIPT ∈ blackboard_R start_POSTSUPERSCRIPT italic_N start_POSTSUBSCRIPT test end_POSTSUBSCRIPT × italic_d end_POSTSUPERSCRIPT</annotation></semantics></math> denotes the testing set.</p>
</div>
</section>
<section class="ltx_paragraph" id="S3.SS0.SSS0.Px2">
<h4 class="ltx_title ltx_title_paragraph">Goal.</h4>
<div class="ltx_para" id="S3.SS0.SSS0.Px2.p1">
<p class="ltx_p" id="S3.SS0.SSS0.Px2.p1.9"><em class="ltx_emph ltx_font_italic" id="S3.SS0.SSS0.Px2.p1.9.1">Price movement forecasting</em> aims to build a (binary) parameterized classifier <math alttext="f_{\theta}:\mathcal{X}\rightarrow\mathcal{Y}" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px2.p1.1.m1.1"><semantics id="S3.SS0.SSS0.Px2.p1.1.m1.1a"><mrow id="S3.SS0.SSS0.Px2.p1.1.m1.1.1" xref="S3.SS0.SSS0.Px2.p1.1.m1.1.1.cmml"><msub id="S3.SS0.SSS0.Px2.p1.1.m*******" xref="S3.SS0.SSS0.Px2.p1.1.m*******.cmml"><mi id="S3.SS0.SSS0.Px2.p1.1.m1.*******" xref="S3.SS0.SSS0.Px2.p1.1.m1.*******.cmml">f</mi><mi id="S3.SS0.SSS0.Px2.p1.1.m1.*******" xref="S3.SS0.SSS0.Px2.p1.1.m1.*******.cmml">θ</mi></msub><mo id="S3.SS0.SSS0.Px2.p1.1.m*******" lspace="0.278em" rspace="0.278em" xref="S3.SS0.SSS0.Px2.p1.1.m*******.cmml">:</mo><mrow id="S3.SS0.SSS0.Px2.p1.1.m*******" xref="S3.SS0.SSS0.Px2.p1.1.m*******.cmml"><mi class="ltx_font_mathcaligraphic" id="S3.SS0.SSS0.Px2.p1.1.m1.*******" xref="S3.SS0.SSS0.Px2.p1.1.m1.*******.cmml">𝒳</mi><mo id="S3.SS0.SSS0.Px2.p1.1.m1.*******" stretchy="false" xref="S3.SS0.SSS0.Px2.p1.1.m1.*******.cmml">→</mo><mi class="ltx_font_mathcaligraphic" id="S3.SS0.SSS0.Px2.p1.1.m1.*******" xref="S3.SS0.SSS0.Px2.p1.1.m1.*******.cmml">𝒴</mi></mrow></mrow><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px2.p1.1.m1.1b"><apply id="S3.SS0.SSS0.Px2.p1.1.m1.1.1.cmml" xref="S3.SS0.SSS0.Px2.p1.1.m1.1.1"><ci id="S3.SS0.SSS0.Px2.p1.1.m*******.cmml" xref="S3.SS0.SSS0.Px2.p1.1.m*******">:</ci><apply id="S3.SS0.SSS0.Px2.p1.1.m*******.cmml" xref="S3.SS0.SSS0.Px2.p1.1.m*******"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px2.p1.1.m1.*******.cmml" xref="S3.SS0.SSS0.Px2.p1.1.m*******">subscript</csymbol><ci id="S3.SS0.SSS0.Px2.p1.1.m1.*******.cmml" xref="S3.SS0.SSS0.Px2.p1.1.m1.*******">𝑓</ci><ci id="S3.SS0.SSS0.Px2.p1.1.m1.*******.cmml" xref="S3.SS0.SSS0.Px2.p1.1.m1.*******">𝜃</ci></apply><apply id="S3.SS0.SSS0.Px2.p1.1.m*******.cmml" xref="S3.SS0.SSS0.Px2.p1.1.m*******"><ci id="S3.SS0.SSS0.Px2.p1.1.m1.*******.cmml" xref="S3.SS0.SSS0.Px2.p1.1.m1.*******">→</ci><ci id="S3.SS0.SSS0.Px2.p1.1.m1.*******.cmml" xref="S3.SS0.SSS0.Px2.p1.1.m1.*******">𝒳</ci><ci id="S3.SS0.SSS0.Px2.p1.1.m1.*******.cmml" xref="S3.SS0.SSS0.Px2.p1.1.m1.*******">𝒴</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px2.p1.1.m1.1c">f_{\theta}:\mathcal{X}\rightarrow\mathcal{Y}</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px2.p1.1.m1.1d">italic_f start_POSTSUBSCRIPT italic_θ end_POSTSUBSCRIPT : caligraphic_X → caligraphic_Y</annotation></semantics></math>, where <math alttext="f_{\theta}({\bm{x}}_{t})=Pr(y_{t}=1)" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px2.p1.2.m2.2"><semantics id="S3.SS0.SSS0.Px2.p1.2.m2.2a"><mrow id="S3.SS0.SSS0.Px2.p1.2.m2.2.2" xref="S3.SS0.SSS0.Px2.p1.2.m2.2.2.cmml"><mrow id="S3.SS0.SSS0.Px2.p1.2.m2.1.1.1" xref="S3.SS0.SSS0.Px2.p1.2.m2.1.1.1.cmml"><msub id="S3.SS0.SSS0.Px2.p1.2.m2.*******" xref="S3.SS0.SSS0.Px2.p1.2.m2.*******.cmml"><mi id="S3.SS0.SSS0.Px2.p1.2.m2.1.*******" xref="S3.SS0.SSS0.Px2.p1.2.m2.1.*******.cmml">f</mi><mi id="S3.SS0.SSS0.Px2.p1.2.m2.1.*******" xref="S3.SS0.SSS0.Px2.p1.2.m2.1.*******.cmml">θ</mi></msub><mo id="S3.SS0.SSS0.Px2.p1.2.m2.*******" xref="S3.SS0.SSS0.Px2.p1.2.m2.*******.cmml">⁢</mo><mrow id="S3.SS0.SSS0.Px2.p1.2.m2.*******.1" xref="S3.SS0.SSS0.Px2.p1.2.m2.*******.1.1.cmml"><mo id="S3.SS0.SSS0.Px2.p1.2.m2.*******.1.2" stretchy="false" xref="S3.SS0.SSS0.Px2.p1.2.m2.*******.1.1.cmml">(</mo><msub id="S3.SS0.SSS0.Px2.p1.2.m2.*******.1.1" xref="S3.SS0.SSS0.Px2.p1.2.m2.*******.1.1.cmml"><mi id="S3.SS0.SSS0.Px2.p1.2.m2.*******.1.1.2" xref="S3.SS0.SSS0.Px2.p1.2.m2.*******.1.1.2.cmml">𝒙</mi><mi id="S3.SS0.SSS0.Px2.p1.2.m2.*******.1.1.3" xref="S3.SS0.SSS0.Px2.p1.2.m2.*******.1.1.3.cmml">t</mi></msub><mo id="S3.SS0.SSS0.Px2.p1.2.m2.*******.1.3" stretchy="false" xref="S3.SS0.SSS0.Px2.p1.2.m2.*******.1.1.cmml">)</mo></mrow></mrow><mo id="S3.SS0.SSS0.Px2.p1.2.m*******" xref="S3.SS0.SSS0.Px2.p1.2.m*******.cmml">=</mo><mrow id="S3.SS0.SSS0.Px2.p1.2.m*******" xref="S3.SS0.SSS0.Px2.p1.2.m*******.cmml"><mi id="S3.SS0.SSS0.Px2.p1.2.m*******.3" xref="S3.SS0.SSS0.Px2.p1.2.m*******.3.cmml">P</mi><mo id="S3.SS0.SSS0.Px2.p1.2.m*******.2" xref="S3.SS0.SSS0.Px2.p1.2.m*******.2.cmml">⁢</mo><mi id="S3.SS0.SSS0.Px2.p1.2.m*******.4" xref="S3.SS0.SSS0.Px2.p1.2.m*******.4.cmml">r</mi><mo id="S3.SS0.SSS0.Px2.p1.2.m*******.2a" xref="S3.SS0.SSS0.Px2.p1.2.m*******.2.cmml">⁢</mo><mrow id="S3.SS0.SSS0.Px2.p1.2.m2.2.*******" xref="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.cmml"><mo id="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.2" stretchy="false" xref="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.cmml">(</mo><mrow id="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1" xref="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.cmml"><msub id="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.2" xref="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.2.cmml"><mi id="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.2.2" xref="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.2.2.cmml">y</mi><mi id="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.2.3" xref="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.2.3.cmml">t</mi></msub><mo id="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.1" xref="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.1.cmml">=</mo><mn id="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.3" xref="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.3.cmml">1</mn></mrow><mo id="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.3" stretchy="false" xref="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.cmml">)</mo></mrow></mrow></mrow><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px2.p1.2.m2.2b"><apply id="S3.SS0.SSS0.Px2.p1.2.m2.2.2.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m2.2.2"><eq id="S3.SS0.SSS0.Px2.p1.2.m*******.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m*******"></eq><apply id="S3.SS0.SSS0.Px2.p1.2.m2.1.1.1.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m2.1.1.1"><times id="S3.SS0.SSS0.Px2.p1.2.m2.*******.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m2.*******"></times><apply id="S3.SS0.SSS0.Px2.p1.2.m2.*******.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m2.*******"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px2.p1.2.m2.1.*******.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m2.*******">subscript</csymbol><ci id="S3.SS0.SSS0.Px2.p1.2.m2.1.*******.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m2.1.*******">𝑓</ci><ci id="S3.SS0.SSS0.Px2.p1.2.m2.1.*******.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m2.1.*******">𝜃</ci></apply><apply id="S3.SS0.SSS0.Px2.p1.2.m2.*******.1.1.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m2.*******.1"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px2.p1.2.m2.*******.1.1.1.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m2.*******.1">subscript</csymbol><ci id="S3.SS0.SSS0.Px2.p1.2.m2.*******.1.1.2.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m2.*******.1.1.2">𝒙</ci><ci id="S3.SS0.SSS0.Px2.p1.2.m2.*******.1.1.3.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m2.*******.1.1.3">𝑡</ci></apply></apply><apply id="S3.SS0.SSS0.Px2.p1.2.m*******.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m*******"><times id="S3.SS0.SSS0.Px2.p1.2.m*******.2.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m*******.2"></times><ci id="S3.SS0.SSS0.Px2.p1.2.m*******.3.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m*******.3">𝑃</ci><ci id="S3.SS0.SSS0.Px2.p1.2.m*******.4.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m*******.4">𝑟</ci><apply id="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m2.2.*******"><eq id="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.1.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.1"></eq><apply id="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.2.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.2"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.2.1.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.2">subscript</csymbol><ci id="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.2.2.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.2.2">𝑦</ci><ci id="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.2.3.cmml" xref="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.2.3">𝑡</ci></apply><cn id="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.3.cmml" type="integer" xref="S3.SS0.SSS0.Px2.p1.2.m2.2.*******.1.3">1</cn></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px2.p1.2.m2.2c">f_{\theta}({\bm{x}}_{t})=Pr(y_{t}=1)</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px2.p1.2.m2.2d">italic_f start_POSTSUBSCRIPT italic_θ end_POSTSUBSCRIPT ( bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT ) = italic_P italic_r ( italic_y start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT = 1 )</annotation></semantics></math> represents the probability of whether the price trend <math alttext="y_{t}" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px2.p1.3.m3.1"><semantics id="S3.SS0.SSS0.Px2.p1.3.m3.1a"><msub id="S3.SS0.SSS0.Px2.p1.3.m3.1.1" xref="S3.SS0.SSS0.Px2.p1.3.m3.1.1.cmml"><mi id="S3.SS0.SSS0.Px2.p1.3.m3.1.1.2" xref="S3.SS0.SSS0.Px2.p1.3.m3.1.1.2.cmml">y</mi><mi id="S3.SS0.SSS0.Px2.p1.3.m3.1.1.3" xref="S3.SS0.SSS0.Px2.p1.3.m3.1.1.3.cmml">t</mi></msub><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px2.p1.3.m3.1b"><apply id="S3.SS0.SSS0.Px2.p1.3.m3.1.1.cmml" xref="S3.SS0.SSS0.Px2.p1.3.m3.1.1"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px2.p1.3.m3.1.1.1.cmml" xref="S3.SS0.SSS0.Px2.p1.3.m3.1.1">subscript</csymbol><ci id="S3.SS0.SSS0.Px2.p1.3.m3.1.1.2.cmml" xref="S3.SS0.SSS0.Px2.p1.3.m3.1.1.2">𝑦</ci><ci id="S3.SS0.SSS0.Px2.p1.3.m3.1.1.3.cmml" xref="S3.SS0.SSS0.Px2.p1.3.m3.1.1.3">𝑡</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px2.p1.3.m3.1c">y_{t}</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px2.p1.3.m3.1d">italic_y start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT</annotation></semantics></math> exceeds the pre-defined threshold <math alttext="\lambda" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px2.p1.4.m4.1"><semantics id="S3.SS0.SSS0.Px2.p1.4.m4.1a"><mi id="S3.SS0.SSS0.Px2.p1.4.m4.1.1" xref="S3.SS0.SSS0.Px2.p1.4.m4.1.1.cmml">λ</mi><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px2.p1.4.m4.1b"><ci id="S3.SS0.SSS0.Px2.p1.4.m4.1.1.cmml" xref="S3.SS0.SSS0.Px2.p1.4.m4.1.1">𝜆</ci></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px2.p1.4.m4.1c">\lambda</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px2.p1.4.m4.1d">italic_λ</annotation></semantics></math>. In order to prevent the data leakage issue of financial data, <math alttext="f_{\theta}({\bm{x}}_{t})" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px2.p1.5.m5.1"><semantics id="S3.SS0.SSS0.Px2.p1.5.m5.1a"><mrow id="S3.SS0.SSS0.Px2.p1.5.m5.1.1" xref="S3.SS0.SSS0.Px2.p1.5.m5.1.1.cmml"><msub id="S3.SS0.SSS0.Px2.p1.5.m5.1.1.3" xref="S3.SS0.SSS0.Px2.p1.5.m5.1.1.3.cmml"><mi id="S3.SS0.SSS0.Px2.p1.5.m5.*******" xref="S3.SS0.SSS0.Px2.p1.5.m5.*******.cmml">f</mi><mi id="S3.SS0.SSS0.Px2.p1.5.m5.*******" xref="S3.SS0.SSS0.Px2.p1.5.m5.*******.cmml">θ</mi></msub><mo id="S3.SS0.SSS0.Px2.p1.5.m5.1.1.2" xref="S3.SS0.SSS0.Px2.p1.5.m5.1.1.2.cmml">⁢</mo><mrow id="S3.SS0.SSS0.Px2.p1.5.m5.*******" xref="S3.SS0.SSS0.Px2.p1.5.m5.*******.1.cmml"><mo id="S3.SS0.SSS0.Px2.p1.5.m5.*******.2" stretchy="false" xref="S3.SS0.SSS0.Px2.p1.5.m5.*******.1.cmml">(</mo><msub id="S3.SS0.SSS0.Px2.p1.5.m5.*******.1" xref="S3.SS0.SSS0.Px2.p1.5.m5.*******.1.cmml"><mi id="S3.SS0.SSS0.Px2.p1.5.m5.*******.1.2" xref="S3.SS0.SSS0.Px2.p1.5.m5.*******.1.2.cmml">𝒙</mi><mi id="S3.SS0.SSS0.Px2.p1.5.m5.*******.1.3" xref="S3.SS0.SSS0.Px2.p1.5.m5.*******.1.3.cmml">t</mi></msub><mo id="S3.SS0.SSS0.Px2.p1.5.m5.*******.3" stretchy="false" xref="S3.SS0.SSS0.Px2.p1.5.m5.*******.1.cmml">)</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px2.p1.5.m5.1b"><apply id="S3.SS0.SSS0.Px2.p1.5.m5.1.1.cmml" xref="S3.SS0.SSS0.Px2.p1.5.m5.1.1"><times id="S3.SS0.SSS0.Px2.p1.5.m5.1.1.2.cmml" xref="S3.SS0.SSS0.Px2.p1.5.m5.1.1.2"></times><apply id="S3.SS0.SSS0.Px2.p1.5.m5.1.1.3.cmml" xref="S3.SS0.SSS0.Px2.p1.5.m5.1.1.3"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px2.p1.5.m5.*******.cmml" xref="S3.SS0.SSS0.Px2.p1.5.m5.1.1.3">subscript</csymbol><ci id="S3.SS0.SSS0.Px2.p1.5.m5.*******.cmml" xref="S3.SS0.SSS0.Px2.p1.5.m5.*******">𝑓</ci><ci id="S3.SS0.SSS0.Px2.p1.5.m5.*******.cmml" xref="S3.SS0.SSS0.Px2.p1.5.m5.*******">𝜃</ci></apply><apply id="S3.SS0.SSS0.Px2.p1.5.m5.*******.1.cmml" xref="S3.SS0.SSS0.Px2.p1.5.m5.*******"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px2.p1.5.m5.*******.1.1.cmml" xref="S3.SS0.SSS0.Px2.p1.5.m5.*******">subscript</csymbol><ci id="S3.SS0.SSS0.Px2.p1.5.m5.*******.1.2.cmml" xref="S3.SS0.SSS0.Px2.p1.5.m5.*******.1.2">𝒙</ci><ci id="S3.SS0.SSS0.Px2.p1.5.m5.*******.1.3.cmml" xref="S3.SS0.SSS0.Px2.p1.5.m5.*******.1.3">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px2.p1.5.m5.1c">f_{\theta}({\bm{x}}_{t})</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px2.p1.5.m5.1d">italic_f start_POSTSUBSCRIPT italic_θ end_POSTSUBSCRIPT ( bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT )</annotation></semantics></math> makes a prediction on the asset price trend <math alttext="y_{t}" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px2.p1.6.m6.1"><semantics id="S3.SS0.SSS0.Px2.p1.6.m6.1a"><msub id="S3.SS0.SSS0.Px2.p1.6.m6.1.1" xref="S3.SS0.SSS0.Px2.p1.6.m6.1.1.cmml"><mi id="S3.SS0.SSS0.Px2.p1.6.m6.1.1.2" xref="S3.SS0.SSS0.Px2.p1.6.m6.1.1.2.cmml">y</mi><mi id="S3.SS0.SSS0.Px2.p1.6.m6.1.1.3" xref="S3.SS0.SSS0.Px2.p1.6.m6.1.1.3.cmml">t</mi></msub><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px2.p1.6.m6.1b"><apply id="S3.SS0.SSS0.Px2.p1.6.m6.1.1.cmml" xref="S3.SS0.SSS0.Px2.p1.6.m6.1.1"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px2.p1.6.m6.1.1.1.cmml" xref="S3.SS0.SSS0.Px2.p1.6.m6.1.1">subscript</csymbol><ci id="S3.SS0.SSS0.Px2.p1.6.m6.1.1.2.cmml" xref="S3.SS0.SSS0.Px2.p1.6.m6.1.1.2">𝑦</ci><ci id="S3.SS0.SSS0.Px2.p1.6.m6.1.1.3.cmml" xref="S3.SS0.SSS0.Px2.p1.6.m6.1.1.3">𝑡</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px2.p1.6.m6.1c">y_{t}</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px2.p1.6.m6.1d">italic_y start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT</annotation></semantics></math> of <math alttext="\Delta" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px2.p1.7.m7.1"><semantics id="S3.SS0.SSS0.Px2.p1.7.m7.1a"><mi id="S3.SS0.SSS0.Px2.p1.7.m7.1.1" mathvariant="normal" xref="S3.SS0.SSS0.Px2.p1.7.m7.1.1.cmml">Δ</mi><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px2.p1.7.m7.1b"><ci id="S3.SS0.SSS0.Px2.p1.7.m7.1.1.cmml" xref="S3.SS0.SSS0.Px2.p1.7.m7.1.1">Δ</ci></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px2.p1.7.m7.1c">\Delta</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px2.p1.7.m7.1d">roman_Δ</annotation></semantics></math>-step ahead (<math alttext="t+\Delta" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px2.p1.8.m8.1"><semantics id="S3.SS0.SSS0.Px2.p1.8.m8.1a"><mrow id="S3.SS0.SSS0.Px2.p1.8.m8.1.1" xref="S3.SS0.SSS0.Px2.p1.8.m8.1.1.cmml"><mi id="S3.SS0.SSS0.Px2.p1.8.m8.1.1.2" xref="S3.SS0.SSS0.Px2.p1.8.m8.1.1.2.cmml">t</mi><mo id="S3.SS0.SSS0.Px2.p1.8.m8.1.1.1" xref="S3.SS0.SSS0.Px2.p1.8.m8.1.1.1.cmml">+</mo><mi id="S3.SS0.SSS0.Px2.p1.8.m8.1.1.3" mathvariant="normal" xref="S3.SS0.SSS0.Px2.p1.8.m8.1.1.3.cmml">Δ</mi></mrow><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px2.p1.8.m8.1b"><apply id="S3.SS0.SSS0.Px2.p1.8.m8.1.1.cmml" xref="S3.SS0.SSS0.Px2.p1.8.m8.1.1"><plus id="S3.SS0.SSS0.Px2.p1.8.m8.1.1.1.cmml" xref="S3.SS0.SSS0.Px2.p1.8.m8.1.1.1"></plus><ci id="S3.SS0.SSS0.Px2.p1.8.m8.1.1.2.cmml" xref="S3.SS0.SSS0.Px2.p1.8.m8.1.1.2">𝑡</ci><ci id="S3.SS0.SSS0.Px2.p1.8.m8.1.1.3.cmml" xref="S3.SS0.SSS0.Px2.p1.8.m8.1.1.3">Δ</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px2.p1.8.m8.1c">t+\Delta</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px2.p1.8.m8.1d">italic_t + roman_Δ</annotation></semantics></math>) only based on price information up to <math alttext="t" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px2.p1.9.m9.1"><semantics id="S3.SS0.SSS0.Px2.p1.9.m9.1a"><mi id="S3.SS0.SSS0.Px2.p1.9.m9.1.1" xref="S3.SS0.SSS0.Px2.p1.9.m9.1.1.cmml">t</mi><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px2.p1.9.m9.1b"><ci id="S3.SS0.SSS0.Px2.p1.9.m9.1.1.cmml" xref="S3.SS0.SSS0.Px2.p1.9.m9.1.1">𝑡</ci></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px2.p1.9.m9.1c">t</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px2.p1.9.m9.1d">italic_t</annotation></semantics></math>.</p>
</div>
</section>
<section class="ltx_paragraph" id="S3.SS0.SSS0.Px3">
<h4 class="ltx_title ltx_title_paragraph">Samples With High <math alttext="p_{x}" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px3.1.m1.1"><semantics id="S3.SS0.SSS0.Px3.1.m1.1b"><msub id="S3.SS0.SSS0.Px3.1.m1.1.1" xref="S3.SS0.SSS0.Px3.1.m1.1.1.cmml"><mi id="S3.SS0.SSS0.Px3.1.m*******" xref="S3.SS0.SSS0.Px3.1.m*******.cmml">p</mi><mi id="S3.SS0.SSS0.Px3.1.m*******" xref="S3.SS0.SSS0.Px3.1.m*******.cmml">x</mi></msub><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px3.1.m1.1c"><apply id="S3.SS0.SSS0.Px3.1.m1.1.1.cmml" xref="S3.SS0.SSS0.Px3.1.m1.1.1"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.1.m*******.cmml" xref="S3.SS0.SSS0.Px3.1.m1.1.1">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.1.m*******.cmml" xref="S3.SS0.SSS0.Px3.1.m*******">𝑝</ci><ci id="S3.SS0.SSS0.Px3.1.m*******.cmml" xref="S3.SS0.SSS0.Px3.1.m*******">𝑥</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px3.1.m1.1d">p_{x}</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px3.1.m1.1e">italic_p start_POSTSUBSCRIPT italic_x end_POSTSUBSCRIPT</annotation></semantics></math>.</h4>
<div class="ltx_para" id="S3.SS0.SSS0.Px3.p1">
<p class="ltx_p" id="S3.SS0.SSS0.Px3.p1.13">Because we consider the binary classification, we assume the predicted label <math alttext="y_{t}\sim\text{Bernoulli}(p_{{\bm{x}}_{t}})" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px3.p1.1.m1.1"><semantics id="S3.SS0.SSS0.Px3.p1.1.m1.1a"><mrow id="S3.SS0.SSS0.Px3.p1.1.m1.1.1" xref="S3.SS0.SSS0.Px3.p1.1.m1.1.1.cmml"><msub id="S3.SS0.SSS0.Px3.p1.1.m*******" xref="S3.SS0.SSS0.Px3.p1.1.m*******.cmml"><mi id="S3.SS0.SSS0.Px3.p1.1.m1.*******" xref="S3.SS0.SSS0.Px3.p1.1.m1.*******.cmml">y</mi><mi id="S3.SS0.SSS0.Px3.p1.1.m1.*******" xref="S3.SS0.SSS0.Px3.p1.1.m1.*******.cmml">t</mi></msub><mo id="S3.SS0.SSS0.Px3.p1.1.m*******" xref="S3.SS0.SSS0.Px3.p1.1.m*******.cmml">∼</mo><mrow id="S3.SS0.SSS0.Px3.p1.1.m*******" xref="S3.SS0.SSS0.Px3.p1.1.m*******.cmml"><mtext id="S3.SS0.SSS0.Px3.p1.1.m*******.3" xref="S3.SS0.SSS0.Px3.p1.1.m*******.3a.cmml">Bernoulli</mtext><mo id="S3.SS0.SSS0.Px3.p1.1.m*******.2" xref="S3.SS0.SSS0.Px3.p1.1.m*******.2.cmml">⁢</mo><mrow id="S3.SS0.SSS0.Px3.p1.1.m*******.1.1" xref="S3.SS0.SSS0.Px3.p1.1.m*******.1.1.1.cmml"><mo id="S3.SS0.SSS0.Px3.p1.1.m*******.1.1.2" stretchy="false" xref="S3.SS0.SSS0.Px3.p1.1.m*******.1.1.1.cmml">(</mo><msub id="S3.SS0.SSS0.Px3.p1.1.m*******.1.1.1" xref="S3.SS0.SSS0.Px3.p1.1.m*******.1.1.1.cmml"><mi id="S3.SS0.SSS0.Px3.p1.1.m*******.*******" xref="S3.SS0.SSS0.Px3.p1.1.m*******.*******.cmml">p</mi><msub id="S3.SS0.SSS0.Px3.p1.1.m*******.*******" xref="S3.SS0.SSS0.Px3.p1.1.m*******.*******.cmml"><mi id="S3.SS0.SSS0.Px3.p1.1.m*******.1.*******" xref="S3.SS0.SSS0.Px3.p1.1.m*******.1.*******.cmml">𝒙</mi><mi id="S3.SS0.SSS0.Px3.p1.1.m*******.1.*******" xref="S3.SS0.SSS0.Px3.p1.1.m*******.1.*******.cmml">t</mi></msub></msub><mo id="S3.SS0.SSS0.Px3.p1.1.m*******.1.1.3" stretchy="false" xref="S3.SS0.SSS0.Px3.p1.1.m*******.1.1.1.cmml">)</mo></mrow></mrow></mrow><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px3.p1.1.m1.1b"><apply id="S3.SS0.SSS0.Px3.p1.1.m1.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.1.m1.1.1"><csymbol cd="latexml" id="S3.SS0.SSS0.Px3.p1.1.m*******.cmml" xref="S3.SS0.SSS0.Px3.p1.1.m*******">similar-to</csymbol><apply id="S3.SS0.SSS0.Px3.p1.1.m*******.cmml" xref="S3.SS0.SSS0.Px3.p1.1.m*******"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.1.m1.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.1.m*******">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.1.m1.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.1.m1.*******">𝑦</ci><ci id="S3.SS0.SSS0.Px3.p1.1.m1.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.1.m1.*******">𝑡</ci></apply><apply id="S3.SS0.SSS0.Px3.p1.1.m*******.cmml" xref="S3.SS0.SSS0.Px3.p1.1.m*******"><times id="S3.SS0.SSS0.Px3.p1.1.m*******.2.cmml" xref="S3.SS0.SSS0.Px3.p1.1.m*******.2"></times><ci id="S3.SS0.SSS0.Px3.p1.1.m*******.3a.cmml" xref="S3.SS0.SSS0.Px3.p1.1.m*******.3"><mtext id="S3.SS0.SSS0.Px3.p1.1.m*******.3.cmml" xref="S3.SS0.SSS0.Px3.p1.1.m*******.3">Bernoulli</mtext></ci><apply id="S3.SS0.SSS0.Px3.p1.1.m*******.1.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.1.m*******.1.1"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.1.m*******.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.1.m*******.1.1">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.1.m*******.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.1.m*******.*******">𝑝</ci><apply id="S3.SS0.SSS0.Px3.p1.1.m*******.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.1.m*******.*******"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.1.m*******.1.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.1.m*******.*******">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.1.m*******.1.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.1.m*******.1.*******">𝒙</ci><ci id="S3.SS0.SSS0.Px3.p1.1.m*******.1.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.1.m*******.1.*******">𝑡</ci></apply></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px3.p1.1.m1.1c">y_{t}\sim\text{Bernoulli}(p_{{\bm{x}}_{t}})</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px3.p1.1.m1.1d">italic_y start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT ∼ Bernoulli ( italic_p start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT )</annotation></semantics></math> conditional on known information <math alttext="{\bm{x}}_{t}" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px3.p1.2.m2.1"><semantics id="S3.SS0.SSS0.Px3.p1.2.m2.1a"><msub id="S3.SS0.SSS0.Px3.p1.2.m2.1.1" xref="S3.SS0.SSS0.Px3.p1.2.m2.1.1.cmml"><mi id="S3.SS0.SSS0.Px3.p1.2.m2.1.1.2" xref="S3.SS0.SSS0.Px3.p1.2.m2.1.1.2.cmml">𝒙</mi><mi id="S3.SS0.SSS0.Px3.p1.2.m2.1.1.3" xref="S3.SS0.SSS0.Px3.p1.2.m2.1.1.3.cmml">t</mi></msub><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px3.p1.2.m2.1b"><apply id="S3.SS0.SSS0.Px3.p1.2.m2.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.2.m2.1.1"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.2.m2.1.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.2.m2.1.1">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.2.m2.1.1.2.cmml" xref="S3.SS0.SSS0.Px3.p1.2.m2.1.1.2">𝒙</ci><ci id="S3.SS0.SSS0.Px3.p1.2.m2.1.1.3.cmml" xref="S3.SS0.SSS0.Px3.p1.2.m2.1.1.3">𝑡</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px3.p1.2.m2.1c">{\bm{x}}_{t}</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px3.p1.2.m2.1d">bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT</annotation></semantics></math> at the <math alttext="t" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px3.p1.3.m3.1"><semantics id="S3.SS0.SSS0.Px3.p1.3.m3.1a"><mi id="S3.SS0.SSS0.Px3.p1.3.m3.1.1" xref="S3.SS0.SSS0.Px3.p1.3.m3.1.1.cmml">t</mi><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px3.p1.3.m3.1b"><ci id="S3.SS0.SSS0.Px3.p1.3.m3.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.3.m3.1.1">𝑡</ci></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px3.p1.3.m3.1c">t</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px3.p1.3.m3.1d">italic_t</annotation></semantics></math> time step. It means that we observe the positive class with the probability <math alttext="p_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px3.p1.4.m4.1"><semantics id="S3.SS0.SSS0.Px3.p1.4.m4.1a"><msub id="S3.SS0.SSS0.Px3.p1.4.m4.1.1" xref="S3.SS0.SSS0.Px3.p1.4.m4.1.1.cmml"><mi id="S3.SS0.SSS0.Px3.p1.4.m4.1.1.2" xref="S3.SS0.SSS0.Px3.p1.4.m4.1.1.2.cmml">p</mi><msub id="S3.SS0.SSS0.Px3.p1.4.m4.1.1.3" xref="S3.SS0.SSS0.Px3.p1.4.m4.1.1.3.cmml"><mi id="S3.SS0.SSS0.Px3.p1.4.m4.*******" xref="S3.SS0.SSS0.Px3.p1.4.m4.*******.cmml">𝒙</mi><mi id="S3.SS0.SSS0.Px3.p1.4.m4.*******" xref="S3.SS0.SSS0.Px3.p1.4.m4.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px3.p1.4.m4.1b"><apply id="S3.SS0.SSS0.Px3.p1.4.m4.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.4.m4.1.1"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.4.m4.1.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.4.m4.1.1">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.4.m4.1.1.2.cmml" xref="S3.SS0.SSS0.Px3.p1.4.m4.1.1.2">𝑝</ci><apply id="S3.SS0.SSS0.Px3.p1.4.m4.1.1.3.cmml" xref="S3.SS0.SSS0.Px3.p1.4.m4.1.1.3"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.4.m4.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.4.m4.1.1.3">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.4.m4.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.4.m4.*******">𝒙</ci><ci id="S3.SS0.SSS0.Px3.p1.4.m4.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.4.m4.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px3.p1.4.m4.1c">p_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px3.p1.4.m4.1d">italic_p start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math> based on the current market condition.
A larger <math alttext="p_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px3.p1.5.m5.1"><semantics id="S3.SS0.SSS0.Px3.p1.5.m5.1a"><msub id="S3.SS0.SSS0.Px3.p1.5.m5.1.1" xref="S3.SS0.SSS0.Px3.p1.5.m5.1.1.cmml"><mi id="S3.SS0.SSS0.Px3.p1.5.m5.1.1.2" xref="S3.SS0.SSS0.Px3.p1.5.m5.1.1.2.cmml">p</mi><msub id="S3.SS0.SSS0.Px3.p1.5.m5.1.1.3" xref="S3.SS0.SSS0.Px3.p1.5.m5.1.1.3.cmml"><mi id="S3.SS0.SSS0.Px3.p1.5.m5.*******" xref="S3.SS0.SSS0.Px3.p1.5.m5.*******.cmml">𝒙</mi><mi id="S3.SS0.SSS0.Px3.p1.5.m5.*******" xref="S3.SS0.SSS0.Px3.p1.5.m5.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px3.p1.5.m5.1b"><apply id="S3.SS0.SSS0.Px3.p1.5.m5.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.5.m5.1.1"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.5.m5.1.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.5.m5.1.1">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.5.m5.1.1.2.cmml" xref="S3.SS0.SSS0.Px3.p1.5.m5.1.1.2">𝑝</ci><apply id="S3.SS0.SSS0.Px3.p1.5.m5.1.1.3.cmml" xref="S3.SS0.SSS0.Px3.p1.5.m5.1.1.3"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.5.m5.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.5.m5.1.1.3">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.5.m5.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.5.m5.*******">𝒙</ci><ci id="S3.SS0.SSS0.Px3.p1.5.m5.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.5.m5.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px3.p1.5.m5.1c">p_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px3.p1.5.m5.1d">italic_p start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math> indicates a stronger tendency and lower uncertainty of the asset price, and thus the prediction at this time step can be more reliable. In order to make a more accurate prediction, we gather the <em class="ltx_emph ltx_font_italic" id="S3.SS0.SSS0.Px3.p1.6.1">samples with high <math alttext="p_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px3.p1.6.1.m1.1"><semantics id="S3.SS0.SSS0.Px3.p1.6.1.m1.1a"><msub id="S3.SS0.SSS0.Px3.p1.6.1.m1.1.1" xref="S3.SS0.SSS0.Px3.p1.6.1.m1.1.1.cmml"><mi id="S3.SS0.SSS0.Px3.p1.6.1.m*******" xref="S3.SS0.SSS0.Px3.p1.6.1.m*******.cmml">p</mi><msub id="S3.SS0.SSS0.Px3.p1.6.1.m*******" xref="S3.SS0.SSS0.Px3.p1.6.1.m*******.cmml"><mi id="S3.SS0.SSS0.Px3.p1.6.1.m1.*******" xref="S3.SS0.SSS0.Px3.p1.6.1.m1.*******.cmml">𝐱</mi><mi id="S3.SS0.SSS0.Px3.p1.6.1.m1.*******" xref="S3.SS0.SSS0.Px3.p1.6.1.m1.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px3.p1.6.1.m1.1b"><apply id="S3.SS0.SSS0.Px3.p1.6.1.m1.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.6.1.m1.1.1"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.6.1.m*******.cmml" xref="S3.SS0.SSS0.Px3.p1.6.1.m1.1.1">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.6.1.m*******.cmml" xref="S3.SS0.SSS0.Px3.p1.6.1.m*******">𝑝</ci><apply id="S3.SS0.SSS0.Px3.p1.6.1.m*******.cmml" xref="S3.SS0.SSS0.Px3.p1.6.1.m*******"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.6.1.m1.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.6.1.m*******">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.6.1.m1.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.6.1.m1.*******">𝐱</ci><ci id="S3.SS0.SSS0.Px3.p1.6.1.m1.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.6.1.m1.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px3.p1.6.1.m1.1c">p_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px3.p1.6.1.m1.1d">italic_p start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math></em>, where <math alttext="p_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px3.p1.7.m6.1"><semantics id="S3.SS0.SSS0.Px3.p1.7.m6.1a"><msub id="S3.SS0.SSS0.Px3.p1.7.m6.1.1" xref="S3.SS0.SSS0.Px3.p1.7.m6.1.1.cmml"><mi id="S3.SS0.SSS0.Px3.p1.7.m6.1.1.2" xref="S3.SS0.SSS0.Px3.p1.7.m6.1.1.2.cmml">p</mi><msub id="S3.SS0.SSS0.Px3.p1.7.m6.1.1.3" xref="S3.SS0.SSS0.Px3.p1.7.m6.1.1.3.cmml"><mi id="S3.SS0.SSS0.Px3.p1.7.m6.*******" xref="S3.SS0.SSS0.Px3.p1.7.m6.*******.cmml">𝒙</mi><mi id="S3.SS0.SSS0.Px3.p1.7.m6.*******" xref="S3.SS0.SSS0.Px3.p1.7.m6.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px3.p1.7.m6.1b"><apply id="S3.SS0.SSS0.Px3.p1.7.m6.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.7.m6.1.1"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.7.m6.1.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.7.m6.1.1">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.7.m6.1.1.2.cmml" xref="S3.SS0.SSS0.Px3.p1.7.m6.1.1.2">𝑝</ci><apply id="S3.SS0.SSS0.Px3.p1.7.m6.1.1.3.cmml" xref="S3.SS0.SSS0.Px3.p1.7.m6.1.1.3"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.7.m6.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.7.m6.1.1.3">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.7.m6.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.7.m6.*******">𝒙</ci><ci id="S3.SS0.SSS0.Px3.p1.7.m6.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.7.m6.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px3.p1.7.m6.1c">p_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px3.p1.7.m6.1d">italic_p start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math> is greater than a given threshold <math alttext="\left(thres\right)" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px3.p1.8.m7.1"><semantics id="S3.SS0.SSS0.Px3.p1.8.m7.1a"><mrow id="S3.SS0.SSS0.Px3.p1.8.m7.1.1.1" xref="S3.SS0.SSS0.Px3.p1.8.m7.*******.cmml"><mo id="S3.SS0.SSS0.Px3.p1.8.m7.*******" xref="S3.SS0.SSS0.Px3.p1.8.m7.*******.cmml">(</mo><mrow id="S3.SS0.SSS0.Px3.p1.8.m7.*******" xref="S3.SS0.SSS0.Px3.p1.8.m7.*******.cmml"><mi id="S3.SS0.SSS0.Px3.p1.8.m7.*******.2" xref="S3.SS0.SSS0.Px3.p1.8.m7.*******.2.cmml">t</mi><mo id="S3.SS0.SSS0.Px3.p1.8.m7.*******.1" xref="S3.SS0.SSS0.Px3.p1.8.m7.*******.1.cmml">⁢</mo><mi id="S3.SS0.SSS0.Px3.p1.8.m7.*******.3" xref="S3.SS0.SSS0.Px3.p1.8.m7.*******.3.cmml">h</mi><mo id="S3.SS0.SSS0.Px3.p1.8.m7.*******.1a" xref="S3.SS0.SSS0.Px3.p1.8.m7.*******.1.cmml">⁢</mo><mi id="S3.SS0.SSS0.Px3.p1.8.m7.*******.4" xref="S3.SS0.SSS0.Px3.p1.8.m7.*******.4.cmml">r</mi><mo id="S3.SS0.SSS0.Px3.p1.8.m7.*******.1b" xref="S3.SS0.SSS0.Px3.p1.8.m7.*******.1.cmml">⁢</mo><mi id="S3.SS0.SSS0.Px3.p1.8.m7.*******.5" xref="S3.SS0.SSS0.Px3.p1.8.m7.*******.5.cmml">e</mi><mo id="S3.SS0.SSS0.Px3.p1.8.m7.*******.1c" xref="S3.SS0.SSS0.Px3.p1.8.m7.*******.1.cmml">⁢</mo><mi id="S3.SS0.SSS0.Px3.p1.8.m7.*******.6" xref="S3.SS0.SSS0.Px3.p1.8.m7.*******.6.cmml">s</mi></mrow><mo id="S3.SS0.SSS0.Px3.p1.8.m7.*******" xref="S3.SS0.SSS0.Px3.p1.8.m7.*******.cmml">)</mo></mrow><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px3.p1.8.m7.1b"><apply id="S3.SS0.SSS0.Px3.p1.8.m7.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.8.m7.1.1.1"><times id="S3.SS0.SSS0.Px3.p1.8.m7.*******.1.cmml" xref="S3.SS0.SSS0.Px3.p1.8.m7.*******.1"></times><ci id="S3.SS0.SSS0.Px3.p1.8.m7.*******.2.cmml" xref="S3.SS0.SSS0.Px3.p1.8.m7.*******.2">𝑡</ci><ci id="S3.SS0.SSS0.Px3.p1.8.m7.*******.3.cmml" xref="S3.SS0.SSS0.Px3.p1.8.m7.*******.3">ℎ</ci><ci id="S3.SS0.SSS0.Px3.p1.8.m7.*******.4.cmml" xref="S3.SS0.SSS0.Px3.p1.8.m7.*******.4">𝑟</ci><ci id="S3.SS0.SSS0.Px3.p1.8.m7.*******.5.cmml" xref="S3.SS0.SSS0.Px3.p1.8.m7.*******.5">𝑒</ci><ci id="S3.SS0.SSS0.Px3.p1.8.m7.*******.6.cmml" xref="S3.SS0.SSS0.Px3.p1.8.m7.*******.6">𝑠</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px3.p1.8.m7.1c">\left(thres\right)</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px3.p1.8.m7.1d">( italic_t italic_h italic_r italic_e italic_s )</annotation></semantics></math>, <em class="ltx_emph ltx_font_italic" id="S3.SS0.SSS0.Px3.p1.13.2">i.e.,</em> <math alttext="p_{{\bm{x}}_{t}}&gt;thres" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px3.p1.9.m8.1"><semantics id="S3.SS0.SSS0.Px3.p1.9.m8.1a"><mrow id="S3.SS0.SSS0.Px3.p1.9.m8.1.1" xref="S3.SS0.SSS0.Px3.p1.9.m8.1.1.cmml"><msub id="S3.SS0.SSS0.Px3.p1.9.m8.1.1.2" xref="S3.SS0.SSS0.Px3.p1.9.m8.1.1.2.cmml"><mi id="S3.SS0.SSS0.Px3.p1.9.m8.*******" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml">p</mi><msub id="S3.SS0.SSS0.Px3.p1.9.m8.*******" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml"><mi id="S3.SS0.SSS0.Px3.p1.9.m8.*******.2" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******.2.cmml">𝒙</mi><mi id="S3.SS0.SSS0.Px3.p1.9.m8.*******.3" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******.3.cmml">t</mi></msub></msub><mo id="S3.SS0.SSS0.Px3.p1.9.m8.1.1.1" xref="S3.SS0.SSS0.Px3.p1.9.m8.1.1.1.cmml">&gt;</mo><mrow id="S3.SS0.SSS0.Px3.p1.9.m8.1.1.3" xref="S3.SS0.SSS0.Px3.p1.9.m8.1.1.3.cmml"><mi id="S3.SS0.SSS0.Px3.p1.9.m8.*******" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml">t</mi><mo id="S3.SS0.SSS0.Px3.p1.9.m8.*******" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml">⁢</mo><mi id="S3.SS0.SSS0.Px3.p1.9.m8.*******" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml">h</mi><mo id="S3.SS0.SSS0.Px3.p1.9.m8.*******a" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml">⁢</mo><mi id="S3.SS0.SSS0.Px3.p1.9.m8.*******" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml">r</mi><mo id="S3.SS0.SSS0.Px3.p1.9.m8.*******b" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml">⁢</mo><mi id="S3.SS0.SSS0.Px3.p1.9.m8.*******" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml">e</mi><mo id="S3.SS0.SSS0.Px3.p1.9.m8.*******c" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml">⁢</mo><mi id="S3.SS0.SSS0.Px3.p1.9.m8.*******" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml">s</mi></mrow></mrow><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px3.p1.9.m8.1b"><apply id="S3.SS0.SSS0.Px3.p1.9.m8.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.9.m8.1.1"><gt id="S3.SS0.SSS0.Px3.p1.9.m8.1.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.9.m8.1.1.1"></gt><apply id="S3.SS0.SSS0.Px3.p1.9.m8.1.1.2.cmml" xref="S3.SS0.SSS0.Px3.p1.9.m8.1.1.2"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.9.m8.1.1.2">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******">𝑝</ci><apply id="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.9.m8.*******.1.cmml" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.9.m8.*******.2.cmml" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******.2">𝒙</ci><ci id="S3.SS0.SSS0.Px3.p1.9.m8.*******.3.cmml" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******.3">𝑡</ci></apply></apply><apply id="S3.SS0.SSS0.Px3.p1.9.m8.1.1.3.cmml" xref="S3.SS0.SSS0.Px3.p1.9.m8.1.1.3"><times id="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******"></times><ci id="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******">𝑡</ci><ci id="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******">ℎ</ci><ci id="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******">𝑟</ci><ci id="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******">𝑒</ci><ci id="S3.SS0.SSS0.Px3.p1.9.m8.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.9.m8.*******">𝑠</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px3.p1.9.m8.1c">p_{{\bm{x}}_{t}}&gt;thres</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px3.p1.9.m8.1d">italic_p start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT &gt; italic_t italic_h italic_r italic_e italic_s</annotation></semantics></math>.
However, it is impossible to obtain the exact underlying probability <math alttext="p_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px3.p1.10.m9.1"><semantics id="S3.SS0.SSS0.Px3.p1.10.m9.1a"><msub id="S3.SS0.SSS0.Px3.p1.10.m9.1.1" xref="S3.SS0.SSS0.Px3.p1.10.m9.1.1.cmml"><mi id="S3.SS0.SSS0.Px3.p1.10.m9.1.1.2" xref="S3.SS0.SSS0.Px3.p1.10.m9.1.1.2.cmml">p</mi><msub id="S3.SS0.SSS0.Px3.p1.10.m9.1.1.3" xref="S3.SS0.SSS0.Px3.p1.10.m9.1.1.3.cmml"><mi id="S3.SS0.SSS0.Px3.p1.10.m9.*******" xref="S3.SS0.SSS0.Px3.p1.10.m9.*******.cmml">𝒙</mi><mi id="S3.SS0.SSS0.Px3.p1.10.m9.*******" xref="S3.SS0.SSS0.Px3.p1.10.m9.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px3.p1.10.m9.1b"><apply id="S3.SS0.SSS0.Px3.p1.10.m9.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.10.m9.1.1"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.10.m9.1.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.10.m9.1.1">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.10.m9.1.1.2.cmml" xref="S3.SS0.SSS0.Px3.p1.10.m9.1.1.2">𝑝</ci><apply id="S3.SS0.SSS0.Px3.p1.10.m9.1.1.3.cmml" xref="S3.SS0.SSS0.Px3.p1.10.m9.1.1.3"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.10.m9.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.10.m9.1.1.3">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.10.m9.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.10.m9.*******">𝒙</ci><ci id="S3.SS0.SSS0.Px3.p1.10.m9.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.10.m9.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px3.p1.10.m9.1c">p_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px3.p1.10.m9.1d">italic_p start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math> from the input data since we can only observe one sample of the distribution <math alttext="y_{t}\sim\text{Bernoulli}(p_{{\bm{x}}_{t}})" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px3.p1.11.m10.1"><semantics id="S3.SS0.SSS0.Px3.p1.11.m10.1a"><mrow id="S3.SS0.SSS0.Px3.p1.11.m10.1.1" xref="S3.SS0.SSS0.Px3.p1.11.m10.1.1.cmml"><msub id="S3.SS0.SSS0.Px3.p1.11.m10.1.1.3" xref="S3.SS0.SSS0.Px3.p1.11.m10.1.1.3.cmml"><mi id="S3.SS0.SSS0.Px3.p1.11.m10.*******" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******.cmml">y</mi><mi id="S3.SS0.SSS0.Px3.p1.11.m10.*******" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******.cmml">t</mi></msub><mo id="S3.SS0.SSS0.Px3.p1.11.m10.1.1.2" xref="S3.SS0.SSS0.Px3.p1.11.m10.1.1.2.cmml">∼</mo><mrow id="S3.SS0.SSS0.Px3.p1.11.m10.1.1.1" xref="S3.SS0.SSS0.Px3.p1.11.m10.1.1.1.cmml"><mtext id="S3.SS0.SSS0.Px3.p1.11.m10.*******" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******a.cmml">Bernoulli</mtext><mo id="S3.SS0.SSS0.Px3.p1.11.m10.*******" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******.cmml">⁢</mo><mrow id="S3.SS0.SSS0.Px3.p1.11.m10.*******.1" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******.1.1.cmml"><mo id="S3.SS0.SSS0.Px3.p1.11.m10.*******.1.2" stretchy="false" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******.1.1.cmml">(</mo><msub id="S3.SS0.SSS0.Px3.p1.11.m10.*******.1.1" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******.1.1.cmml"><mi id="S3.SS0.SSS0.Px3.p1.11.m10.*******.1.1.2" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******.1.1.2.cmml">p</mi><msub id="S3.SS0.SSS0.Px3.p1.11.m10.*******.1.1.3" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******.1.1.3.cmml"><mi id="S3.SS0.SSS0.Px3.p1.11.m10.*******.*******" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******.*******.cmml">𝒙</mi><mi id="S3.SS0.SSS0.Px3.p1.11.m10.*******.*******" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******.*******.cmml">t</mi></msub></msub><mo id="S3.SS0.SSS0.Px3.p1.11.m10.*******.1.3" stretchy="false" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******.1.1.cmml">)</mo></mrow></mrow></mrow><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px3.p1.11.m10.1b"><apply id="S3.SS0.SSS0.Px3.p1.11.m10.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.11.m10.1.1"><csymbol cd="latexml" id="S3.SS0.SSS0.Px3.p1.11.m10.1.1.2.cmml" xref="S3.SS0.SSS0.Px3.p1.11.m10.1.1.2">similar-to</csymbol><apply id="S3.SS0.SSS0.Px3.p1.11.m10.1.1.3.cmml" xref="S3.SS0.SSS0.Px3.p1.11.m10.1.1.3"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.11.m10.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.11.m10.1.1.3">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.11.m10.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******">𝑦</ci><ci id="S3.SS0.SSS0.Px3.p1.11.m10.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******">𝑡</ci></apply><apply id="S3.SS0.SSS0.Px3.p1.11.m10.1.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.11.m10.1.1.1"><times id="S3.SS0.SSS0.Px3.p1.11.m10.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******"></times><ci id="S3.SS0.SSS0.Px3.p1.11.m10.*******a.cmml" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******"><mtext id="S3.SS0.SSS0.Px3.p1.11.m10.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******">Bernoulli</mtext></ci><apply id="S3.SS0.SSS0.Px3.p1.11.m10.*******.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******.1"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.11.m10.*******.1.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******.1">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.11.m10.*******.1.1.2.cmml" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******.1.1.2">𝑝</ci><apply id="S3.SS0.SSS0.Px3.p1.11.m10.*******.1.1.3.cmml" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******.1.1.3"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.11.m10.*******.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******.1.1.3">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.11.m10.*******.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******.*******">𝒙</ci><ci id="S3.SS0.SSS0.Px3.p1.11.m10.*******.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.11.m10.*******.*******">𝑡</ci></apply></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px3.p1.11.m10.1c">y_{t}\sim\text{Bernoulli}(p_{{\bm{x}}_{t}})</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px3.p1.11.m10.1d">italic_y start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT ∼ Bernoulli ( italic_p start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT )</annotation></semantics></math> at one certain time-step. Thus, we introduce <em class="ltx_emph ltx_font_italic" id="S3.SS0.SSS0.Px3.p1.13.3">locality-aware attention</em> (Sec. <a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#S4.SS1" title="4.1 Locality-Aware Attention (LA-Attention) ‣ 4 LARA: The Proposed Framework ‣ Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling"><span class="ltx_text ltx_ref_tag">4.1</span></a>) to give an approximate estimation <math alttext="\hat{p}_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px3.p1.12.m11.1"><semantics id="S3.SS0.SSS0.Px3.p1.12.m11.1a"><msub id="S3.SS0.SSS0.Px3.p1.12.m11.1.1" xref="S3.SS0.SSS0.Px3.p1.12.m11.1.1.cmml"><mover accent="true" id="S3.SS0.SSS0.Px3.p1.12.m1*******" xref="S3.SS0.SSS0.Px3.p1.12.m1*******.cmml"><mi id="S3.SS0.SSS0.Px3.p1.12.m11.*******" xref="S3.SS0.SSS0.Px3.p1.12.m11.*******.cmml">p</mi><mo id="S3.SS0.SSS0.Px3.p1.12.m11.*******" xref="S3.SS0.SSS0.Px3.p1.12.m11.*******.cmml">^</mo></mover><msub id="S3.SS0.SSS0.Px3.p1.12.m1*******" xref="S3.SS0.SSS0.Px3.p1.12.m1*******.cmml"><mi id="S3.SS0.SSS0.Px3.p1.12.m11.*******" xref="S3.SS0.SSS0.Px3.p1.12.m11.*******.cmml">𝒙</mi><mi id="S3.SS0.SSS0.Px3.p1.12.m11.*******" xref="S3.SS0.SSS0.Px3.p1.12.m11.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px3.p1.12.m11.1b"><apply id="S3.SS0.SSS0.Px3.p1.12.m11.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.12.m11.1.1"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.12.m1*******.cmml" xref="S3.SS0.SSS0.Px3.p1.12.m11.1.1">subscript</csymbol><apply id="S3.SS0.SSS0.Px3.p1.12.m1*******.cmml" xref="S3.SS0.SSS0.Px3.p1.12.m1*******"><ci id="S3.SS0.SSS0.Px3.p1.12.m11.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.12.m11.*******">^</ci><ci id="S3.SS0.SSS0.Px3.p1.12.m11.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.12.m11.*******">𝑝</ci></apply><apply id="S3.SS0.SSS0.Px3.p1.12.m1*******.cmml" xref="S3.SS0.SSS0.Px3.p1.12.m1*******"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.12.m11.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.12.m1*******">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.12.m11.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.12.m11.*******">𝒙</ci><ci id="S3.SS0.SSS0.Px3.p1.12.m11.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.12.m11.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px3.p1.12.m11.1c">\hat{p}_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px3.p1.12.m11.1d">over^ start_ARG italic_p end_ARG start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math> of <math alttext="p_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S3.SS0.SSS0.Px3.p1.13.m12.1"><semantics id="S3.SS0.SSS0.Px3.p1.13.m12.1a"><msub id="S3.SS0.SSS0.Px3.p1.13.m12.1.1" xref="S3.SS0.SSS0.Px3.p1.13.m12.1.1.cmml"><mi id="S3.SS0.SSS0.Px3.p1.13.m12.1.1.2" xref="S3.SS0.SSS0.Px3.p1.13.m12.1.1.2.cmml">p</mi><msub id="S3.SS0.SSS0.Px3.p1.13.m12.1.1.3" xref="S3.SS0.SSS0.Px3.p1.13.m12.1.1.3.cmml"><mi id="S3.SS0.SSS0.Px3.p1.13.m12.*******" xref="S3.SS0.SSS0.Px3.p1.13.m12.*******.cmml">𝒙</mi><mi id="S3.SS0.SSS0.Px3.p1.13.m12.*******" xref="S3.SS0.SSS0.Px3.p1.13.m12.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S3.SS0.SSS0.Px3.p1.13.m12.1b"><apply id="S3.SS0.SSS0.Px3.p1.13.m12.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.13.m12.1.1"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.13.m12.1.1.1.cmml" xref="S3.SS0.SSS0.Px3.p1.13.m12.1.1">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.13.m12.1.1.2.cmml" xref="S3.SS0.SSS0.Px3.p1.13.m12.1.1.2">𝑝</ci><apply id="S3.SS0.SSS0.Px3.p1.13.m12.1.1.3.cmml" xref="S3.SS0.SSS0.Px3.p1.13.m12.1.1.3"><csymbol cd="ambiguous" id="S3.SS0.SSS0.Px3.p1.13.m12.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.13.m12.1.1.3">subscript</csymbol><ci id="S3.SS0.SSS0.Px3.p1.13.m12.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.13.m12.*******">𝒙</ci><ci id="S3.SS0.SSS0.Px3.p1.13.m12.*******.cmml" xref="S3.SS0.SSS0.Px3.p1.13.m12.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S3.SS0.SSS0.Px3.p1.13.m12.1c">p_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S3.SS0.SSS0.Px3.p1.13.m12.1d">italic_p start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math>.</p>
</div>
</section>
</section>
<section class="ltx_section" id="S4">
<h2 class="ltx_title ltx_title_section">
<span class="ltx_tag ltx_tag_section">4 </span>LARA: The Proposed Framework</h2>
<div class="ltx_para" id="S4.p1">
<p class="ltx_p" id="S4.p1.1">In this section, we present our LARA framework, which consists of two main components—<em class="ltx_emph ltx_font_italic" id="S4.p1.1.2">locality-aware attention</em> (LA-Attention, Sec. <a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#S4.SS1" title="4.1 Locality-Aware Attention (LA-Attention) ‣ 4 LARA: The Proposed Framework ‣ Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling"><span class="ltx_text ltx_ref_tag">4.1</span></a>) and <em class="ltx_emph ltx_font_italic" id="S4.p1.1.3">iterative refinement labeling</em> (RA-Labeling, Sec. <a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#S4.SS2" title="4.2 Iterative Refinement Labeling (RA-Labeling) ‣ 4 LARA: The Proposed Framework ‣ Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling"><span class="ltx_text ltx_ref_tag">4.2</span></a>).
The key idea of LARA is to extract potentially profitable samples from the noisy market. LA-Attention extracts the <em class="ltx_emph ltx_font_italic" id="S4.p1.1.1">samples with high <math alttext="p_{\bm{x}}" class="ltx_Math" display="inline" id="S4.p1.1.1.m1.1"><semantics id="S4.p1.1.1.m1.1a"><msub id="S4.p1.1.1.m1.1.1" xref="S4.p1.1.1.m1.1.1.cmml"><mi id="S4.p1.1.1.m*******" xref="S4.p1.1.1.m*******.cmml">p</mi><mi id="S4.p1.1.1.m*******" xref="S4.p1.1.1.m*******.cmml">𝐱</mi></msub><annotation-xml encoding="MathML-Content" id="S4.p1.1.1.m1.1b"><apply id="S4.p1.1.1.m1.1.1.cmml" xref="S4.p1.1.1.m1.1.1"><csymbol cd="ambiguous" id="S4.p1.1.1.m*******.cmml" xref="S4.p1.1.1.m1.1.1">subscript</csymbol><ci id="S4.p1.1.1.m*******.cmml" xref="S4.p1.1.1.m*******">𝑝</ci><ci id="S4.p1.1.1.m*******.cmml" xref="S4.p1.1.1.m*******">𝐱</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.p1.1.1.m1.1c">p_{\bm{x}}</annotation><annotation encoding="application/x-llamapun" id="S4.p1.1.1.m1.1d">italic_p start_POSTSUBSCRIPT bold_italic_x end_POSTSUBSCRIPT</annotation></semantics></math></em> and RA-Labeling refines the labels of <em class="ltx_emph ltx_font_italic" id="S4.p1.1.4">noisy samples</em> to boost the performance of predictors.
The workflow of LARA is shown in Fig. <a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#S1.F2" title="Figure 2 ‣ 1 Introduction ‣ Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling"><span class="ltx_text ltx_ref_tag">2</span></a>.</p>
</div>
<section class="ltx_subsection" id="S4.SS1">
<h3 class="ltx_title ltx_title_subsection">
<span class="ltx_tag ltx_tag_subsection">4.1 </span><span class="ltx_text ltx_framed ltx_framed_underline" id="S4.SS1.1.1">L</span>ocality-Aware <span class="ltx_text ltx_framed ltx_framed_underline" id="S4.SS1.2.2">A</span>ttention (LA-Attention)</h3>
<div class="ltx_para" id="S4.SS1.p1">
<p class="ltx_p" id="S4.SS1.p1.6">LA-Attention is a two-step method for its training and testing phases.
In the training phase, we first extract the <em class="ltx_emph ltx_font_italic" id="S4.SS1.p1.1.1">samples with high <math alttext="p_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S4.SS1.p1.1.1.m1.1"><semantics id="S4.SS1.p1.1.1.m1.1a"><msub id="S4.SS1.p1.1.1.m1.1.1" xref="S4.SS1.p1.1.1.m1.1.1.cmml"><mi id="S4.SS1.p1.1.1.m*******" xref="S4.SS1.p1.1.1.m*******.cmml">p</mi><msub id="S4.SS1.p1.1.1.m*******" xref="S4.SS1.p1.1.1.m*******.cmml"><mi id="S4.SS1.p1.1.1.m1.*******" xref="S4.SS1.p1.1.1.m1.*******.cmml">𝐱</mi><mi id="S4.SS1.p1.1.1.m1.*******" xref="S4.SS1.p1.1.1.m1.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.p1.1.1.m1.1b"><apply id="S4.SS1.p1.1.1.m1.1.1.cmml" xref="S4.SS1.p1.1.1.m1.1.1"><csymbol cd="ambiguous" id="S4.SS1.p1.1.1.m*******.cmml" xref="S4.SS1.p1.1.1.m1.1.1">subscript</csymbol><ci id="S4.SS1.p1.1.1.m*******.cmml" xref="S4.SS1.p1.1.1.m*******">𝑝</ci><apply id="S4.SS1.p1.1.1.m*******.cmml" xref="S4.SS1.p1.1.1.m*******"><csymbol cd="ambiguous" id="S4.SS1.p1.1.1.m1.*******.cmml" xref="S4.SS1.p1.1.1.m*******">subscript</csymbol><ci id="S4.SS1.p1.1.1.m1.*******.cmml" xref="S4.SS1.p1.1.1.m1.*******">𝐱</ci><ci id="S4.SS1.p1.1.1.m1.*******.cmml" xref="S4.SS1.p1.1.1.m1.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.p1.1.1.m1.1c">p_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.p1.1.1.m1.1d">italic_p start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math></em> satisfying <math alttext="\hat{p}_{{\bm{x}}_{t}}&gt;thres" class="ltx_Math" display="inline" id="S4.SS1.p1.2.m1.1"><semantics id="S4.SS1.p1.2.m1.1a"><mrow id="S4.SS1.p1.2.m1.1.1" xref="S4.SS1.p1.2.m1.1.1.cmml"><msub id="S4.SS1.p1.2.m*******" xref="S4.SS1.p1.2.m*******.cmml"><mover accent="true" id="S4.SS1.p1.2.m1.*******" xref="S4.SS1.p1.2.m1.*******.cmml"><mi id="S4.SS1.p1.2.m1.*******.2" xref="S4.SS1.p1.2.m1.*******.2.cmml">p</mi><mo id="S4.SS1.p1.2.m1.*******.1" xref="S4.SS1.p1.2.m1.*******.1.cmml">^</mo></mover><msub id="S4.SS1.p1.2.m1.*******" xref="S4.SS1.p1.2.m1.*******.cmml"><mi id="S4.SS1.p1.2.m1.*******.2" xref="S4.SS1.p1.2.m1.*******.2.cmml">𝒙</mi><mi id="S4.SS1.p1.2.m1.*******.3" xref="S4.SS1.p1.2.m1.*******.3.cmml">t</mi></msub></msub><mo id="S4.SS1.p1.2.m*******" xref="S4.SS1.p1.2.m*******.cmml">&gt;</mo><mrow id="S4.SS1.p1.2.m*******" xref="S4.SS1.p1.2.m*******.cmml"><mi id="S4.SS1.p1.2.m1.*******" xref="S4.SS1.p1.2.m1.*******.cmml">t</mi><mo id="S4.SS1.p1.2.m1.*******" xref="S4.SS1.p1.2.m1.*******.cmml">⁢</mo><mi id="S4.SS1.p1.2.m1.*******" xref="S4.SS1.p1.2.m1.*******.cmml">h</mi><mo id="S4.SS1.p1.2.m1.*******a" xref="S4.SS1.p1.2.m1.*******.cmml">⁢</mo><mi id="S4.SS1.p1.2.m*******.4" xref="S4.SS1.p1.2.m*******.4.cmml">r</mi><mo id="S4.SS1.p1.2.m1.*******b" xref="S4.SS1.p1.2.m1.*******.cmml">⁢</mo><mi id="S4.SS1.p1.2.m*******.5" xref="S4.SS1.p1.2.m*******.5.cmml">e</mi><mo id="S4.SS1.p1.2.m1.*******c" xref="S4.SS1.p1.2.m1.*******.cmml">⁢</mo><mi id="S4.SS1.p1.2.m*******.6" xref="S4.SS1.p1.2.m*******.6.cmml">s</mi></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.p1.2.m1.1b"><apply id="S4.SS1.p1.2.m1.1.1.cmml" xref="S4.SS1.p1.2.m1.1.1"><gt id="S4.SS1.p1.2.m*******.cmml" xref="S4.SS1.p1.2.m*******"></gt><apply id="S4.SS1.p1.2.m*******.cmml" xref="S4.SS1.p1.2.m*******"><csymbol cd="ambiguous" id="S4.SS1.p1.2.m1.*******.cmml" xref="S4.SS1.p1.2.m*******">subscript</csymbol><apply id="S4.SS1.p1.2.m1.*******.cmml" xref="S4.SS1.p1.2.m1.*******"><ci id="S4.SS1.p1.2.m1.*******.1.cmml" xref="S4.SS1.p1.2.m1.*******.1">^</ci><ci id="S4.SS1.p1.2.m1.*******.2.cmml" xref="S4.SS1.p1.2.m1.*******.2">𝑝</ci></apply><apply id="S4.SS1.p1.2.m1.*******.cmml" xref="S4.SS1.p1.2.m1.*******"><csymbol cd="ambiguous" id="S4.SS1.p1.2.m1.*******.1.cmml" xref="S4.SS1.p1.2.m1.*******">subscript</csymbol><ci id="S4.SS1.p1.2.m1.*******.2.cmml" xref="S4.SS1.p1.2.m1.*******.2">𝒙</ci><ci id="S4.SS1.p1.2.m1.*******.3.cmml" xref="S4.SS1.p1.2.m1.*******.3">𝑡</ci></apply></apply><apply id="S4.SS1.p1.2.m*******.cmml" xref="S4.SS1.p1.2.m*******"><times id="S4.SS1.p1.2.m1.*******.cmml" xref="S4.SS1.p1.2.m1.*******"></times><ci id="S4.SS1.p1.2.m1.*******.cmml" xref="S4.SS1.p1.2.m1.*******">𝑡</ci><ci id="S4.SS1.p1.2.m1.*******.cmml" xref="S4.SS1.p1.2.m1.*******">ℎ</ci><ci id="S4.SS1.p1.2.m*******.4.cmml" xref="S4.SS1.p1.2.m*******.4">𝑟</ci><ci id="S4.SS1.p1.2.m*******.5.cmml" xref="S4.SS1.p1.2.m*******.5">𝑒</ci><ci id="S4.SS1.p1.2.m*******.6.cmml" xref="S4.SS1.p1.2.m*******.6">𝑠</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.p1.2.m1.1c">\hat{p}_{{\bm{x}}_{t}}&gt;thres</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.p1.2.m1.1d">over^ start_ARG italic_p end_ARG start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT &gt; italic_t italic_h italic_r italic_e italic_s</annotation></semantics></math> (<math alttext="thres" class="ltx_Math" display="inline" id="S4.SS1.p1.3.m2.1"><semantics id="S4.SS1.p1.3.m2.1a"><mrow id="S4.SS1.p1.3.m2.1.1" xref="S4.SS1.p1.3.m2.1.1.cmml"><mi id="S4.SS1.p1.3.m2.1.1.2" xref="S4.SS1.p1.3.m2.1.1.2.cmml">t</mi><mo id="S4.SS1.p1.3.m2.1.1.1" xref="S4.SS1.p1.3.m2.1.1.1.cmml">⁢</mo><mi id="S4.SS1.p1.3.m2.1.1.3" xref="S4.SS1.p1.3.m2.1.1.3.cmml">h</mi><mo id="S4.SS1.p1.3.m2.1.1.1a" xref="S4.SS1.p1.3.m2.1.1.1.cmml">⁢</mo><mi id="S4.SS1.p1.3.m2.1.1.4" xref="S4.SS1.p1.3.m2.1.1.4.cmml">r</mi><mo id="S4.SS1.p1.3.m2.1.1.1b" xref="S4.SS1.p1.3.m2.1.1.1.cmml">⁢</mo><mi id="S4.SS1.p1.3.m2.1.1.5" xref="S4.SS1.p1.3.m2.1.1.5.cmml">e</mi><mo id="S4.SS1.p1.3.m2.1.1.1c" xref="S4.SS1.p1.3.m2.1.1.1.cmml">⁢</mo><mi id="S4.SS1.p1.3.m2.1.1.6" xref="S4.SS1.p1.3.m2.1.1.6.cmml">s</mi></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.p1.3.m2.1b"><apply id="S4.SS1.p1.3.m2.1.1.cmml" xref="S4.SS1.p1.3.m2.1.1"><times id="S4.SS1.p1.3.m2.1.1.1.cmml" xref="S4.SS1.p1.3.m2.1.1.1"></times><ci id="S4.SS1.p1.3.m2.1.1.2.cmml" xref="S4.SS1.p1.3.m2.1.1.2">𝑡</ci><ci id="S4.SS1.p1.3.m2.1.1.3.cmml" xref="S4.SS1.p1.3.m2.1.1.3">ℎ</ci><ci id="S4.SS1.p1.3.m2.1.1.4.cmml" xref="S4.SS1.p1.3.m2.1.1.4">𝑟</ci><ci id="S4.SS1.p1.3.m2.1.1.5.cmml" xref="S4.SS1.p1.3.m2.1.1.5">𝑒</ci><ci id="S4.SS1.p1.3.m2.1.1.6.cmml" xref="S4.SS1.p1.3.m2.1.1.6">𝑠</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.p1.3.m2.1c">thres</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.p1.3.m2.1d">italic_t italic_h italic_r italic_e italic_s</annotation></semantics></math> denotes the desired probability that one sample yields the positive class), which implicitly denote the potentially profitable samples.
Then we train a more robust model on these selected samples according to the corresponding supervised losses.
In the testing phase, we first gather the <em class="ltx_emph ltx_font_italic" id="S4.SS1.p1.4.2">samples with high <math alttext="p_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S4.SS1.p1.4.2.m1.1"><semantics id="S4.SS1.p1.4.2.m1.1a"><msub id="S4.SS1.p1.4.2.m1.1.1" xref="S4.SS1.p1.4.2.m1.1.1.cmml"><mi id="S4.SS1.p1.4.2.m*******" xref="S4.SS1.p1.4.2.m*******.cmml">p</mi><msub id="S4.SS1.p1.4.2.m*******" xref="S4.SS1.p1.4.2.m*******.cmml"><mi id="S4.SS1.p1.4.2.m1.*******" xref="S4.SS1.p1.4.2.m1.*******.cmml">𝐱</mi><mi id="S4.SS1.p1.4.2.m1.*******" xref="S4.SS1.p1.4.2.m1.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.p1.4.2.m1.1b"><apply id="S4.SS1.p1.4.2.m1.1.1.cmml" xref="S4.SS1.p1.4.2.m1.1.1"><csymbol cd="ambiguous" id="S4.SS1.p1.4.2.m*******.cmml" xref="S4.SS1.p1.4.2.m1.1.1">subscript</csymbol><ci id="S4.SS1.p1.4.2.m*******.cmml" xref="S4.SS1.p1.4.2.m*******">𝑝</ci><apply id="S4.SS1.p1.4.2.m*******.cmml" xref="S4.SS1.p1.4.2.m*******"><csymbol cd="ambiguous" id="S4.SS1.p1.4.2.m1.*******.cmml" xref="S4.SS1.p1.4.2.m*******">subscript</csymbol><ci id="S4.SS1.p1.4.2.m1.*******.cmml" xref="S4.SS1.p1.4.2.m1.*******">𝐱</ci><ci id="S4.SS1.p1.4.2.m1.*******.cmml" xref="S4.SS1.p1.4.2.m1.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.p1.4.2.m1.1c">p_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.p1.4.2.m1.1d">italic_p start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math></em> by the <math alttext="\hat{p}_{{\bm{x}}_{t}}&gt;thres" class="ltx_Math" display="inline" id="S4.SS1.p1.5.m3.1"><semantics id="S4.SS1.p1.5.m3.1a"><mrow id="S4.SS1.p1.5.m3.1.1" xref="S4.SS1.p1.5.m3.1.1.cmml"><msub id="S4.SS1.p1.5.m3.1.1.2" xref="S4.SS1.p1.5.m3.1.1.2.cmml"><mover accent="true" id="S4.SS1.p1.5.m3.*******" xref="S4.SS1.p1.5.m3.*******.cmml"><mi id="S4.SS1.p1.5.m3.*******.2" xref="S4.SS1.p1.5.m3.*******.2.cmml">p</mi><mo id="S4.SS1.p1.5.m3.*******.1" xref="S4.SS1.p1.5.m3.*******.1.cmml">^</mo></mover><msub id="S4.SS1.p1.5.m3.*******" xref="S4.SS1.p1.5.m3.*******.cmml"><mi id="S4.SS1.p1.5.m3.*******.2" xref="S4.SS1.p1.5.m3.*******.2.cmml">𝒙</mi><mi id="S4.SS1.p1.5.m3.*******.3" xref="S4.SS1.p1.5.m3.*******.3.cmml">t</mi></msub></msub><mo id="S4.SS1.p1.5.m3.1.1.1" xref="S4.SS1.p1.5.m3.1.1.1.cmml">&gt;</mo><mrow id="S4.SS1.p1.5.m3.1.1.3" xref="S4.SS1.p1.5.m3.1.1.3.cmml"><mi id="S4.SS1.p1.5.m3.*******" xref="S4.SS1.p1.5.m3.*******.cmml">t</mi><mo id="S4.SS1.p1.5.m3.*******" xref="S4.SS1.p1.5.m3.*******.cmml">⁢</mo><mi id="S4.SS1.p1.5.m3.*******" xref="S4.SS1.p1.5.m3.*******.cmml">h</mi><mo id="S4.SS1.p1.5.m3.*******a" xref="S4.SS1.p1.5.m3.*******.cmml">⁢</mo><mi id="S4.SS1.p1.5.m3.*******" xref="S4.SS1.p1.5.m3.*******.cmml">r</mi><mo id="S4.SS1.p1.5.m3.*******b" xref="S4.SS1.p1.5.m3.*******.cmml">⁢</mo><mi id="S4.SS1.p1.5.m3.*******" xref="S4.SS1.p1.5.m3.*******.cmml">e</mi><mo id="S4.SS1.p1.5.m3.*******c" xref="S4.SS1.p1.5.m3.*******.cmml">⁢</mo><mi id="S4.SS1.p1.5.m3.*******" xref="S4.SS1.p1.5.m3.*******.cmml">s</mi></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.p1.5.m3.1b"><apply id="S4.SS1.p1.5.m3.1.1.cmml" xref="S4.SS1.p1.5.m3.1.1"><gt id="S4.SS1.p1.5.m3.1.1.1.cmml" xref="S4.SS1.p1.5.m3.1.1.1"></gt><apply id="S4.SS1.p1.5.m3.1.1.2.cmml" xref="S4.SS1.p1.5.m3.1.1.2"><csymbol cd="ambiguous" id="S4.SS1.p1.5.m3.*******.cmml" xref="S4.SS1.p1.5.m3.1.1.2">subscript</csymbol><apply id="S4.SS1.p1.5.m3.*******.cmml" xref="S4.SS1.p1.5.m3.*******"><ci id="S4.SS1.p1.5.m3.*******.1.cmml" xref="S4.SS1.p1.5.m3.*******.1">^</ci><ci id="S4.SS1.p1.5.m3.*******.2.cmml" xref="S4.SS1.p1.5.m3.*******.2">𝑝</ci></apply><apply id="S4.SS1.p1.5.m3.*******.cmml" xref="S4.SS1.p1.5.m3.*******"><csymbol cd="ambiguous" id="S4.SS1.p1.5.m3.*******.1.cmml" xref="S4.SS1.p1.5.m3.*******">subscript</csymbol><ci id="S4.SS1.p1.5.m3.*******.2.cmml" xref="S4.SS1.p1.5.m3.*******.2">𝒙</ci><ci id="S4.SS1.p1.5.m3.*******.3.cmml" xref="S4.SS1.p1.5.m3.*******.3">𝑡</ci></apply></apply><apply id="S4.SS1.p1.5.m3.1.1.3.cmml" xref="S4.SS1.p1.5.m3.1.1.3"><times id="S4.SS1.p1.5.m3.*******.cmml" xref="S4.SS1.p1.5.m3.*******"></times><ci id="S4.SS1.p1.5.m3.*******.cmml" xref="S4.SS1.p1.5.m3.*******">𝑡</ci><ci id="S4.SS1.p1.5.m3.*******.cmml" xref="S4.SS1.p1.5.m3.*******">ℎ</ci><ci id="S4.SS1.p1.5.m3.*******.cmml" xref="S4.SS1.p1.5.m3.*******">𝑟</ci><ci id="S4.SS1.p1.5.m3.*******.cmml" xref="S4.SS1.p1.5.m3.*******">𝑒</ci><ci id="S4.SS1.p1.5.m3.*******.cmml" xref="S4.SS1.p1.5.m3.*******">𝑠</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.p1.5.m3.1c">\hat{p}_{{\bm{x}}_{t}}&gt;thres</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.p1.5.m3.1d">over^ start_ARG italic_p end_ARG start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT &gt; italic_t italic_h italic_r italic_e italic_s</annotation></semantics></math> criterion, and then we only make predictions on these selected samples.
Thanks to this two-step modular design, LA-Attention can largely improve the precision of the prediction model on empirical evaluations.
The complete algorithm is summarized in Algorithm <a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#alg1" title="Algorithm 1 ‣ LA-Attention Has a Proper Estimation of 𝑝_𝒙_𝑡. ‣ 4.1 Locality-Aware Attention (LA-Attention) ‣ 4 LARA: The Proposed Framework ‣ Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling"><span class="ltx_text ltx_ref_tag">1</span></a>.
In what follows, we first introduce the localization module to estimate <math alttext="\hat{p}_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S4.SS1.p1.6.m4.1"><semantics id="S4.SS1.p1.6.m4.1a"><msub id="S4.SS1.p1.6.m4.1.1" xref="S4.SS1.p1.6.m4.1.1.cmml"><mover accent="true" id="S4.SS1.p1.6.m4.1.1.2" xref="S4.SS1.p1.6.m4.1.1.2.cmml"><mi id="S4.SS1.p1.6.m4.*******" xref="S4.SS1.p1.6.m4.*******.cmml">p</mi><mo id="S4.SS1.p1.6.m4.*******" xref="S4.SS1.p1.6.m4.*******.cmml">^</mo></mover><msub id="S4.SS1.p1.6.m4.1.1.3" xref="S4.SS1.p1.6.m4.1.1.3.cmml"><mi id="S4.SS1.p1.6.m4.*******" xref="S4.SS1.p1.6.m4.*******.cmml">𝒙</mi><mi id="S4.SS1.p1.6.m4.*******" xref="S4.SS1.p1.6.m4.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.p1.6.m4.1b"><apply id="S4.SS1.p1.6.m4.1.1.cmml" xref="S4.SS1.p1.6.m4.1.1"><csymbol cd="ambiguous" id="S4.SS1.p1.6.m4.1.1.1.cmml" xref="S4.SS1.p1.6.m4.1.1">subscript</csymbol><apply id="S4.SS1.p1.6.m4.1.1.2.cmml" xref="S4.SS1.p1.6.m4.1.1.2"><ci id="S4.SS1.p1.6.m4.*******.cmml" xref="S4.SS1.p1.6.m4.*******">^</ci><ci id="S4.SS1.p1.6.m4.*******.cmml" xref="S4.SS1.p1.6.m4.*******">𝑝</ci></apply><apply id="S4.SS1.p1.6.m4.1.1.3.cmml" xref="S4.SS1.p1.6.m4.1.1.3"><csymbol cd="ambiguous" id="S4.SS1.p1.6.m4.*******.cmml" xref="S4.SS1.p1.6.m4.1.1.3">subscript</csymbol><ci id="S4.SS1.p1.6.m4.*******.cmml" xref="S4.SS1.p1.6.m4.*******">𝒙</ci><ci id="S4.SS1.p1.6.m4.*******.cmml" xref="S4.SS1.p1.6.m4.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.p1.6.m4.1c">\hat{p}_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.p1.6.m4.1d">over^ start_ARG italic_p end_ARG start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math> and then introduce the metric learning module to assist the <em class="ltx_emph ltx_font_italic" id="S4.SS1.p1.6.3">localization module</em> by learning a well-suited distance metric.</p>
</div>
<section class="ltx_paragraph" id="S4.SS1.SSS0.Px1">
<h4 class="ltx_title ltx_title_paragraph">Localization Module.</h4>
<div class="ltx_para" id="S4.SS1.SSS0.Px1.p1">
<p class="ltx_p" id="S4.SS1.SSS0.Px1.p1.10">It is hard to recover the label distribution <math alttext="y_{t}\sim\text{Bernoulli}(p_{{\bm{x}}_{t}})" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px1.p1.1.m1.1"><semantics id="S4.SS1.SSS0.Px1.p1.1.m1.1a"><mrow id="S4.SS1.SSS0.Px1.p1.1.m1.1.1" xref="S4.SS1.SSS0.Px1.p1.1.m1.1.1.cmml"><msub id="S4.SS1.SSS0.Px1.p1.1.m*******" xref="S4.SS1.SSS0.Px1.p1.1.m*******.cmml"><mi id="S4.SS1.SSS0.Px1.p1.1.m1.*******" xref="S4.SS1.SSS0.Px1.p1.1.m1.*******.cmml">y</mi><mi id="S4.SS1.SSS0.Px1.p1.1.m1.*******" xref="S4.SS1.SSS0.Px1.p1.1.m1.*******.cmml">t</mi></msub><mo id="S4.SS1.SSS0.Px1.p1.1.m*******" xref="S4.SS1.SSS0.Px1.p1.1.m*******.cmml">∼</mo><mrow id="S4.SS1.SSS0.Px1.p1.1.m*******" xref="S4.SS1.SSS0.Px1.p1.1.m*******.cmml"><mtext id="S4.SS1.SSS0.Px1.p1.1.m*******.3" xref="S4.SS1.SSS0.Px1.p1.1.m*******.3a.cmml">Bernoulli</mtext><mo id="S4.SS1.SSS0.Px1.p1.1.m*******.2" xref="S4.SS1.SSS0.Px1.p1.1.m*******.2.cmml">⁢</mo><mrow id="S4.SS1.SSS0.Px1.p1.1.m*******.1.1" xref="S4.SS1.SSS0.Px1.p1.1.m*******.1.1.1.cmml"><mo id="S4.SS1.SSS0.Px1.p1.1.m*******.1.1.2" stretchy="false" xref="S4.SS1.SSS0.Px1.p1.1.m*******.1.1.1.cmml">(</mo><msub id="S4.SS1.SSS0.Px1.p1.1.m*******.1.1.1" xref="S4.SS1.SSS0.Px1.p1.1.m*******.1.1.1.cmml"><mi id="S4.SS1.SSS0.Px1.p1.1.m*******.*******" xref="S4.SS1.SSS0.Px1.p1.1.m*******.*******.cmml">p</mi><msub id="S4.SS1.SSS0.Px1.p1.1.m*******.*******" xref="S4.SS1.SSS0.Px1.p1.1.m*******.*******.cmml"><mi id="S4.SS1.SSS0.Px1.p1.1.m*******.1.*******" xref="S4.SS1.SSS0.Px1.p1.1.m*******.1.*******.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px1.p1.1.m*******.1.*******" xref="S4.SS1.SSS0.Px1.p1.1.m*******.1.*******.cmml">t</mi></msub></msub><mo id="S4.SS1.SSS0.Px1.p1.1.m*******.1.1.3" stretchy="false" xref="S4.SS1.SSS0.Px1.p1.1.m*******.1.1.1.cmml">)</mo></mrow></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px1.p1.1.m1.1b"><apply id="S4.SS1.SSS0.Px1.p1.1.m1.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.1.m1.1.1"><csymbol cd="latexml" id="S4.SS1.SSS0.Px1.p1.1.m*******.cmml" xref="S4.SS1.SSS0.Px1.p1.1.m*******">similar-to</csymbol><apply id="S4.SS1.SSS0.Px1.p1.1.m*******.cmml" xref="S4.SS1.SSS0.Px1.p1.1.m*******"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.1.m1.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.1.m*******">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.1.m1.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.1.m1.*******">𝑦</ci><ci id="S4.SS1.SSS0.Px1.p1.1.m1.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.1.m1.*******">𝑡</ci></apply><apply id="S4.SS1.SSS0.Px1.p1.1.m*******.cmml" xref="S4.SS1.SSS0.Px1.p1.1.m*******"><times id="S4.SS1.SSS0.Px1.p1.1.m*******.2.cmml" xref="S4.SS1.SSS0.Px1.p1.1.m*******.2"></times><ci id="S4.SS1.SSS0.Px1.p1.1.m*******.3a.cmml" xref="S4.SS1.SSS0.Px1.p1.1.m*******.3"><mtext id="S4.SS1.SSS0.Px1.p1.1.m*******.3.cmml" xref="S4.SS1.SSS0.Px1.p1.1.m*******.3">Bernoulli</mtext></ci><apply id="S4.SS1.SSS0.Px1.p1.1.m*******.1.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.1.m*******.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.1.m*******.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.1.m*******.1.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.1.m*******.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.1.m*******.*******">𝑝</ci><apply id="S4.SS1.SSS0.Px1.p1.1.m*******.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.1.m*******.*******"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.1.m*******.1.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.1.m*******.*******">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.1.m*******.1.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.1.m*******.1.*******">𝒙</ci><ci id="S4.SS1.SSS0.Px1.p1.1.m*******.1.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.1.m*******.1.*******">𝑡</ci></apply></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px1.p1.1.m1.1c">y_{t}\sim\text{Bernoulli}(p_{{\bm{x}}_{t}})</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px1.p1.1.m1.1d">italic_y start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT ∼ Bernoulli ( italic_p start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT )</annotation></semantics></math> from the noisy asset price dataset, as we can only observe one sample of the distribution at the <math alttext="t" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px1.p1.2.m2.1"><semantics id="S4.SS1.SSS0.Px1.p1.2.m2.1a"><mi id="S4.SS1.SSS0.Px1.p1.2.m2.1.1" xref="S4.SS1.SSS0.Px1.p1.2.m2.1.1.cmml">t</mi><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px1.p1.2.m2.1b"><ci id="S4.SS1.SSS0.Px1.p1.2.m2.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.2.m2.1.1">𝑡</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px1.p1.2.m2.1c">t</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px1.p1.2.m2.1d">italic_t</annotation></semantics></math>-th time-step. Suppose the parameter <math alttext="p_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px1.p1.3.m3.1"><semantics id="S4.SS1.SSS0.Px1.p1.3.m3.1a"><msub id="S4.SS1.SSS0.Px1.p1.3.m3.1.1" xref="S4.SS1.SSS0.Px1.p1.3.m3.1.1.cmml"><mi id="S4.SS1.SSS0.Px1.p1.3.m3.1.1.2" xref="S4.SS1.SSS0.Px1.p1.3.m3.1.1.2.cmml">p</mi><msub id="S4.SS1.SSS0.Px1.p1.3.m3.1.1.3" xref="S4.SS1.SSS0.Px1.p1.3.m3.1.1.3.cmml"><mi id="S4.SS1.SSS0.Px1.p1.3.m3.*******" xref="S4.SS1.SSS0.Px1.p1.3.m3.*******.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px1.p1.3.m3.*******" xref="S4.SS1.SSS0.Px1.p1.3.m3.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px1.p1.3.m3.1b"><apply id="S4.SS1.SSS0.Px1.p1.3.m3.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.3.m3.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.3.m3.1.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.3.m3.1.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.3.m3.1.1.2.cmml" xref="S4.SS1.SSS0.Px1.p1.3.m3.1.1.2">𝑝</ci><apply id="S4.SS1.SSS0.Px1.p1.3.m3.1.1.3.cmml" xref="S4.SS1.SSS0.Px1.p1.3.m3.1.1.3"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.3.m3.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.3.m3.1.1.3">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.3.m3.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.3.m3.*******">𝒙</ci><ci id="S4.SS1.SSS0.Px1.p1.3.m3.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.3.m3.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px1.p1.3.m3.1c">p_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px1.p1.3.m3.1d">italic_p start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math> of the probability distribution is continuous with respect to known information <math alttext="{\bm{x}}_{t}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px1.p1.4.m4.1"><semantics id="S4.SS1.SSS0.Px1.p1.4.m4.1a"><msub id="S4.SS1.SSS0.Px1.p1.4.m4.1.1" xref="S4.SS1.SSS0.Px1.p1.4.m4.1.1.cmml"><mi id="S4.SS1.SSS0.Px1.p1.4.m4.1.1.2" xref="S4.SS1.SSS0.Px1.p1.4.m4.1.1.2.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px1.p1.4.m4.1.1.3" xref="S4.SS1.SSS0.Px1.p1.4.m4.1.1.3.cmml">t</mi></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px1.p1.4.m4.1b"><apply id="S4.SS1.SSS0.Px1.p1.4.m4.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.4.m4.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.4.m4.1.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.4.m4.1.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.4.m4.1.1.2.cmml" xref="S4.SS1.SSS0.Px1.p1.4.m4.1.1.2">𝒙</ci><ci id="S4.SS1.SSS0.Px1.p1.4.m4.1.1.3.cmml" xref="S4.SS1.SSS0.Px1.p1.4.m4.1.1.3">𝑡</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px1.p1.4.m4.1c">{\bm{x}}_{t}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px1.p1.4.m4.1d">bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT</annotation></semantics></math> (<em class="ltx_emph ltx_font_italic" id="S4.SS1.SSS0.Px1.p1.10.1">e.g.,</em> the asset price).
We design a <em class="ltx_emph ltx_font_italic" id="S4.SS1.SSS0.Px1.p1.10.2">localization module</em> attending to other observed samples in the dataset to obtain an approximate estimation <math alttext="\hat{p}_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px1.p1.5.m5.1"><semantics id="S4.SS1.SSS0.Px1.p1.5.m5.1a"><msub id="S4.SS1.SSS0.Px1.p1.5.m5.1.1" xref="S4.SS1.SSS0.Px1.p1.5.m5.1.1.cmml"><mover accent="true" id="S4.SS1.SSS0.Px1.p1.5.m5.1.1.2" xref="S4.SS1.SSS0.Px1.p1.5.m5.1.1.2.cmml"><mi id="S4.SS1.SSS0.Px1.p1.5.m5.*******" xref="S4.SS1.SSS0.Px1.p1.5.m5.*******.cmml">p</mi><mo id="S4.SS1.SSS0.Px1.p1.5.m5.*******" xref="S4.SS1.SSS0.Px1.p1.5.m5.*******.cmml">^</mo></mover><msub id="S4.SS1.SSS0.Px1.p1.5.m5.1.1.3" xref="S4.SS1.SSS0.Px1.p1.5.m5.1.1.3.cmml"><mi id="S4.SS1.SSS0.Px1.p1.5.m5.*******" xref="S4.SS1.SSS0.Px1.p1.5.m5.*******.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px1.p1.5.m5.*******" xref="S4.SS1.SSS0.Px1.p1.5.m5.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px1.p1.5.m5.1b"><apply id="S4.SS1.SSS0.Px1.p1.5.m5.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.5.m5.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.5.m5.1.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.5.m5.1.1">subscript</csymbol><apply id="S4.SS1.SSS0.Px1.p1.5.m5.1.1.2.cmml" xref="S4.SS1.SSS0.Px1.p1.5.m5.1.1.2"><ci id="S4.SS1.SSS0.Px1.p1.5.m5.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.5.m5.*******">^</ci><ci id="S4.SS1.SSS0.Px1.p1.5.m5.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.5.m5.*******">𝑝</ci></apply><apply id="S4.SS1.SSS0.Px1.p1.5.m5.1.1.3.cmml" xref="S4.SS1.SSS0.Px1.p1.5.m5.1.1.3"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.5.m5.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.5.m5.1.1.3">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.5.m5.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.5.m5.*******">𝒙</ci><ci id="S4.SS1.SSS0.Px1.p1.5.m5.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.5.m5.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px1.p1.5.m5.1c">\hat{p}_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px1.p1.5.m5.1d">over^ start_ARG italic_p end_ARG start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math> of <math alttext="p_{{\bm{x}}}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px1.p1.6.m6.1"><semantics id="S4.SS1.SSS0.Px1.p1.6.m6.1a"><msub id="S4.SS1.SSS0.Px1.p1.6.m6.1.1" xref="S4.SS1.SSS0.Px1.p1.6.m6.1.1.cmml"><mi id="S4.SS1.SSS0.Px1.p1.6.m6.1.1.2" xref="S4.SS1.SSS0.Px1.p1.6.m6.1.1.2.cmml">p</mi><mi id="S4.SS1.SSS0.Px1.p1.6.m6.1.1.3" xref="S4.SS1.SSS0.Px1.p1.6.m6.1.1.3.cmml">𝒙</mi></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px1.p1.6.m6.1b"><apply id="S4.SS1.SSS0.Px1.p1.6.m6.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.6.m6.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.6.m6.1.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.6.m6.1.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.6.m6.1.1.2.cmml" xref="S4.SS1.SSS0.Px1.p1.6.m6.1.1.2">𝑝</ci><ci id="S4.SS1.SSS0.Px1.p1.6.m6.1.1.3.cmml" xref="S4.SS1.SSS0.Px1.p1.6.m6.1.1.3">𝒙</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px1.p1.6.m6.1c">p_{{\bm{x}}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px1.p1.6.m6.1d">italic_p start_POSTSUBSCRIPT bold_italic_x end_POSTSUBSCRIPT</annotation></semantics></math>. In light of the concepts of attention mechanism <cite class="ltx_cite ltx_citemacro_cite">Vaswani <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib27" title="">2017</a>)</cite>, we denote <math alttext="\left({\bm{x}}_{i},y_{i}\right)" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px1.p1.7.m7.2"><semantics id="S4.SS1.SSS0.Px1.p1.7.m7.2a"><mrow id="S4.SS1.SSS0.Px1.p1.7.m7.2.2.2" xref="S4.SS1.SSS0.Px1.p1.7.m7.2.2.3.cmml"><mo id="S4.SS1.SSS0.Px1.p1.7.m7.*******" xref="S4.SS1.SSS0.Px1.p1.7.m7.2.2.3.cmml">(</mo><msub id="S4.SS1.SSS0.Px1.p1.7.m7.*******" xref="S4.SS1.SSS0.Px1.p1.7.m7.*******.cmml"><mi id="S4.SS1.SSS0.Px1.p1.7.m7.*******.2" xref="S4.SS1.SSS0.Px1.p1.7.m7.*******.2.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px1.p1.7.m7.*******.3" xref="S4.SS1.SSS0.Px1.p1.7.m7.*******.3.cmml">i</mi></msub><mo id="S4.SS1.SSS0.Px1.p1.7.m7.*******" xref="S4.SS1.SSS0.Px1.p1.7.m7.2.2.3.cmml">,</mo><msub id="S4.SS1.SSS0.Px1.p1.7.m7.*******" xref="S4.SS1.SSS0.Px1.p1.7.m7.*******.cmml"><mi id="S4.SS1.SSS0.Px1.p1.7.m7.*******.2" xref="S4.SS1.SSS0.Px1.p1.7.m7.*******.2.cmml">y</mi><mi id="S4.SS1.SSS0.Px1.p1.7.m7.*******.3" xref="S4.SS1.SSS0.Px1.p1.7.m7.*******.3.cmml">i</mi></msub><mo id="S4.SS1.SSS0.Px1.p1.7.m7.*******" xref="S4.SS1.SSS0.Px1.p1.7.m7.2.2.3.cmml">)</mo></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px1.p1.7.m7.2b"><interval closure="open" id="S4.SS1.SSS0.Px1.p1.7.m7.2.2.3.cmml" xref="S4.SS1.SSS0.Px1.p1.7.m7.2.2.2"><apply id="S4.SS1.SSS0.Px1.p1.7.m7.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.7.m7.*******"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.7.m7.*******.1.cmml" xref="S4.SS1.SSS0.Px1.p1.7.m7.*******">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.7.m7.*******.2.cmml" xref="S4.SS1.SSS0.Px1.p1.7.m7.*******.2">𝒙</ci><ci id="S4.SS1.SSS0.Px1.p1.7.m7.*******.3.cmml" xref="S4.SS1.SSS0.Px1.p1.7.m7.*******.3">𝑖</ci></apply><apply id="S4.SS1.SSS0.Px1.p1.7.m7.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.7.m7.*******"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.7.m7.2.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.7.m7.*******">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.7.m7.*******.2.cmml" xref="S4.SS1.SSS0.Px1.p1.7.m7.*******.2">𝑦</ci><ci id="S4.SS1.SSS0.Px1.p1.7.m7.*******.3.cmml" xref="S4.SS1.SSS0.Px1.p1.7.m7.*******.3">𝑖</ci></apply></interval></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px1.p1.7.m7.2c">\left({\bm{x}}_{i},y_{i}\right)</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px1.p1.7.m7.2d">( bold_italic_x start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT , italic_y start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT )</annotation></semantics></math> as the key-value pair, where <math alttext="y_{i}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px1.p1.8.m8.1"><semantics id="S4.SS1.SSS0.Px1.p1.8.m8.1a"><msub id="S4.SS1.SSS0.Px1.p1.8.m8.1.1" xref="S4.SS1.SSS0.Px1.p1.8.m8.1.1.cmml"><mi id="S4.SS1.SSS0.Px1.p1.8.m8.1.1.2" xref="S4.SS1.SSS0.Px1.p1.8.m8.1.1.2.cmml">y</mi><mi id="S4.SS1.SSS0.Px1.p1.8.m8.1.1.3" xref="S4.SS1.SSS0.Px1.p1.8.m8.1.1.3.cmml">i</mi></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px1.p1.8.m8.1b"><apply id="S4.SS1.SSS0.Px1.p1.8.m8.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.8.m8.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.8.m8.1.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.8.m8.1.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.8.m8.1.1.2.cmml" xref="S4.SS1.SSS0.Px1.p1.8.m8.1.1.2">𝑦</ci><ci id="S4.SS1.SSS0.Px1.p1.8.m8.1.1.3.cmml" xref="S4.SS1.SSS0.Px1.p1.8.m8.1.1.3">𝑖</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px1.p1.8.m8.1c">y_{i}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px1.p1.8.m8.1d">italic_y start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT</annotation></semantics></math> is the label of <math alttext="{\bm{x}}_{i}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px1.p1.9.m9.1"><semantics id="S4.SS1.SSS0.Px1.p1.9.m9.1a"><msub id="S4.SS1.SSS0.Px1.p1.9.m9.1.1" xref="S4.SS1.SSS0.Px1.p1.9.m9.1.1.cmml"><mi id="S4.SS1.SSS0.Px1.p1.9.m9.1.1.2" xref="S4.SS1.SSS0.Px1.p1.9.m9.1.1.2.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px1.p1.9.m9.1.1.3" xref="S4.SS1.SSS0.Px1.p1.9.m9.1.1.3.cmml">i</mi></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px1.p1.9.m9.1b"><apply id="S4.SS1.SSS0.Px1.p1.9.m9.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.9.m9.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.9.m9.1.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.9.m9.1.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.9.m9.1.1.2.cmml" xref="S4.SS1.SSS0.Px1.p1.9.m9.1.1.2">𝒙</ci><ci id="S4.SS1.SSS0.Px1.p1.9.m9.1.1.3.cmml" xref="S4.SS1.SSS0.Px1.p1.9.m9.1.1.3">𝑖</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px1.p1.9.m9.1c">{\bm{x}}_{i}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px1.p1.9.m9.1d">bold_italic_x start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT</annotation></semantics></math>. Let <math alttext="{\bm{x}}_{t}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px1.p1.10.m10.1"><semantics id="S4.SS1.SSS0.Px1.p1.10.m10.1a"><msub id="S4.SS1.SSS0.Px1.p1.10.m10.1.1" xref="S4.SS1.SSS0.Px1.p1.10.m10.1.1.cmml"><mi id="S4.SS1.SSS0.Px1.p1.10.m10.1.1.2" xref="S4.SS1.SSS0.Px1.p1.10.m10.1.1.2.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px1.p1.10.m10.1.1.3" xref="S4.SS1.SSS0.Px1.p1.10.m10.1.1.3.cmml">t</mi></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px1.p1.10.m10.1b"><apply id="S4.SS1.SSS0.Px1.p1.10.m10.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.10.m10.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.10.m10.1.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.10.m10.1.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.10.m10.1.1.2.cmml" xref="S4.SS1.SSS0.Px1.p1.10.m10.1.1.2">𝒙</ci><ci id="S4.SS1.SSS0.Px1.p1.10.m10.1.1.3.cmml" xref="S4.SS1.SSS0.Px1.p1.10.m10.1.1.3">𝑡</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px1.p1.10.m10.1c">{\bm{x}}_{t}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px1.p1.10.m10.1d">bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT</annotation></semantics></math> be the query sample. The <em class="ltx_emph ltx_font_italic" id="S4.SS1.SSS0.Px1.p1.10.3">localization module</em> can be formulated as follows:</p>
<table class="ltx_equation ltx_eqn_table" id="S4.E2">
<tbody><tr class="ltx_equation ltx_eqn_row ltx_align_baseline">
<td class="ltx_eqn_cell ltx_eqn_center_padleft"></td>
<td class="ltx_eqn_cell ltx_align_center"><math alttext="\hat{p}_{{\bm{x}}_{t}}=\sum_{1\leq i\leq N}y_{i}\cdot\frac{k({\bm{x}}_{i},{\bm%
{x}}_{t})}{\sum_{1\leq j\leq N}k({\bm{x}}_{j},{\bm{x}}_{t})}\quad," class="ltx_Math" display="block" id="S4.E2.m1.5"><semantics id="S4.E2.m1.5a"><mrow id="S4.E2.m1.5.5.1" xref="S4.E2.m1.*******.cmml"><mrow id="S4.E2.m1.*******" xref="S4.E2.m1.*******.cmml"><msub id="S4.E2.m1.*******.2" xref="S4.E2.m1.*******.2.cmml"><mover accent="true" id="S4.E2.m1.*******.2.2" xref="S4.E2.m1.*******.2.2.cmml"><mi id="S4.E2.m1.*******.2.2.2" xref="S4.E2.m1.*******.2.2.2.cmml">p</mi><mo id="S4.E2.m1.*******.2.2.1" xref="S4.E2.m1.*******.2.2.1.cmml">^</mo></mover><msub id="S4.E2.m1.*******.2.3" xref="S4.E2.m1.*******.2.3.cmml"><mi id="S4.E2.m1.*******.2.3.2" xref="S4.E2.m1.*******.2.3.2.cmml">𝒙</mi><mi id="S4.E2.m1.*******.2.3.3" xref="S4.E2.m1.*******.2.3.3.cmml">t</mi></msub></msub><mo id="S4.E2.m1.*******.1" rspace="0.111em" xref="S4.E2.m1.*******.1.cmml">=</mo><mrow id="S4.E2.m1.*******.3" xref="S4.E2.m1.*******.3.cmml"><munder id="S4.E2.m1.*******.3.1" xref="S4.E2.m1.*******.3.1.cmml"><mo id="S4.E2.m1.*******.3.1.2" movablelimits="false" xref="S4.E2.m1.*******.3.1.2.cmml">∑</mo><mrow id="S4.E2.m1.*******.3.1.3" xref="S4.E2.m1.*******.3.1.3.cmml"><mn id="S4.E2.m1.*******.*******" xref="S4.E2.m1.*******.*******.cmml">1</mn><mo id="S4.E2.m1.*******.*******" xref="S4.E2.m1.*******.*******.cmml">≤</mo><mi id="S4.E2.m1.*******.*******" xref="S4.E2.m1.*******.*******.cmml">i</mi><mo id="S4.E2.m1.*******.*******" xref="S4.E2.m1.*******.*******.cmml">≤</mo><mi id="S4.E2.m1.*******.*******" xref="S4.E2.m1.*******.*******.cmml">N</mi></mrow></munder><mrow id="S4.E2.m1.*******.3.2" xref="S4.E2.m1.*******.3.2.cmml"><msub id="S4.E2.m1.*******.3.2.2" xref="S4.E2.m1.*******.3.2.2.cmml"><mi id="S4.E2.m1.*******.*******" xref="S4.E2.m1.*******.*******.cmml">y</mi><mi id="S4.E2.m1.*******.*******" xref="S4.E2.m1.*******.*******.cmml">i</mi></msub><mo id="S4.E2.m1.*******.3.2.1" lspace="0.222em" rspace="0.222em" xref="S4.E2.m1.*******.3.2.1.cmml">⋅</mo><mfrac id="S4.E2.m1.4.4" xref="S4.E2.m1.4.4.cmml"><mrow id="S4.E2.m*******" xref="S4.E2.m*******.cmml"><mi id="S4.E2.m1.*******" xref="S4.E2.m1.*******.cmml">k</mi><mo id="S4.E2.m1.*******" xref="S4.E2.m1.*******.cmml">⁢</mo><mrow id="S4.E2.m1.*******.2" xref="S4.E2.m1.*******.3.cmml"><mo id="S4.E2.m1.*******.2.3" stretchy="false" xref="S4.E2.m1.*******.3.cmml">(</mo><msub id="S4.E2.m*******.1.1.1" xref="S4.E2.m*******.1.1.1.cmml"><mi id="S4.E2.m*******.*******" xref="S4.E2.m*******.*******.cmml">𝒙</mi><mi id="S4.E2.m*******.*******" xref="S4.E2.m*******.*******.cmml">i</mi></msub><mo id="S4.E2.m1.*******.2.4" xref="S4.E2.m1.*******.3.cmml">,</mo><msub id="S4.E2.m1.*******.2.2" xref="S4.E2.m1.*******.2.2.cmml"><mi id="S4.E2.m1.*******.2.2.2" xref="S4.E2.m1.*******.2.2.2.cmml">𝒙</mi><mi id="S4.E2.m1.*******.2.2.3" xref="S4.E2.m1.*******.2.2.3.cmml">t</mi></msub><mo id="S4.E2.m1.*******.2.5" stretchy="false" xref="S4.E2.m1.*******.3.cmml">)</mo></mrow></mrow><mrow id="S4.E2.m1.4.4.4" xref="S4.E2.m1.4.4.4.cmml"><msub id="S4.E2.m1.*******" xref="S4.E2.m1.*******.cmml"><mo id="S4.E2.m1.*******.2" xref="S4.E2.m1.*******.2.cmml">∑</mo><mrow id="S4.E2.m1.*******.3" xref="S4.E2.m1.*******.3.cmml"><mn id="S4.E2.m1.*******.3.2" xref="S4.E2.m1.*******.3.2.cmml">1</mn><mo id="S4.E2.m1.*******.3.3" xref="S4.E2.m1.*******.3.3.cmml">≤</mo><mi id="S4.E2.m1.*******.3.4" xref="S4.E2.m1.*******.3.4.cmml">j</mi><mo id="S4.E2.m1.*******.3.5" xref="S4.E2.m1.*******.3.5.cmml">≤</mo><mi id="S4.E2.m1.*******.3.6" xref="S4.E2.m1.*******.3.6.cmml">N</mi></mrow></msub><mrow id="S4.E2.m1.*******" xref="S4.E2.m1.*******.cmml"><mi id="S4.E2.m1.4.*******" xref="S4.E2.m1.4.*******.cmml">k</mi><mo id="S4.E2.m1.4.*******" xref="S4.E2.m1.4.*******.cmml">⁢</mo><mrow id="S4.E2.m1.4.*******.2" xref="S4.E2.m1.4.*******.3.cmml"><mo id="S4.E2.m1.4.4.4.*******" stretchy="false" xref="S4.E2.m1.4.*******.3.cmml">(</mo><msub id="S4.E2.m1.3.3.3.*******" xref="S4.E2.m1.3.3.3.*******.cmml"><mi id="S4.E2.m1.3.3.3.*******.2" xref="S4.E2.m1.3.3.3.*******.2.cmml">𝒙</mi><mi id="S4.E2.m1.3.3.3.*******.3" xref="S4.E2.m1.3.3.3.*******.3.cmml">j</mi></msub><mo id="S4.E2.m1.4.4.4.*******" xref="S4.E2.m1.4.*******.3.cmml">,</mo><msub id="S4.E2.m1.4.4.4.*******" xref="S4.E2.m1.4.4.4.*******.cmml"><mi id="S4.E2.m1.4.4.4.*******.2" xref="S4.E2.m1.4.4.4.*******.2.cmml">𝒙</mi><mi id="S4.E2.m1.4.4.4.*******.3" xref="S4.E2.m1.4.4.4.*******.3.cmml">t</mi></msub><mo id="S4.E2.m1.4.4.4.*******" stretchy="false" xref="S4.E2.m1.4.*******.3.cmml">)</mo></mrow></mrow></mrow></mfrac></mrow></mrow></mrow><mspace id="S4.E2.m1.*******" width="1em" xref="S4.E2.m1.*******.cmml"></mspace><mo id="S4.E2.m1.*******" xref="S4.E2.m1.*******.cmml">,</mo></mrow><annotation-xml encoding="MathML-Content" id="S4.E2.m1.5b"><apply id="S4.E2.m1.*******.cmml" xref="S4.E2.m1.5.5.1"><eq id="S4.E2.m1.*******.1.cmml" xref="S4.E2.m1.*******.1"></eq><apply id="S4.E2.m1.*******.2.cmml" xref="S4.E2.m1.*******.2"><csymbol cd="ambiguous" id="S4.E2.m1.*******.2.1.cmml" xref="S4.E2.m1.*******.2">subscript</csymbol><apply id="S4.E2.m1.*******.2.2.cmml" xref="S4.E2.m1.*******.2.2"><ci id="S4.E2.m1.*******.2.2.1.cmml" xref="S4.E2.m1.*******.2.2.1">^</ci><ci id="S4.E2.m1.*******.2.2.2.cmml" xref="S4.E2.m1.*******.2.2.2">𝑝</ci></apply><apply id="S4.E2.m1.*******.2.3.cmml" xref="S4.E2.m1.*******.2.3"><csymbol cd="ambiguous" id="S4.E2.m1.*******.2.3.1.cmml" xref="S4.E2.m1.*******.2.3">subscript</csymbol><ci id="S4.E2.m1.*******.2.3.2.cmml" xref="S4.E2.m1.*******.2.3.2">𝒙</ci><ci id="S4.E2.m1.*******.2.3.3.cmml" xref="S4.E2.m1.*******.2.3.3">𝑡</ci></apply></apply><apply id="S4.E2.m1.*******.3.cmml" xref="S4.E2.m1.*******.3"><apply id="S4.E2.m1.*******.3.1.cmml" xref="S4.E2.m1.*******.3.1"><csymbol cd="ambiguous" id="S4.E2.m1.*******.3.1.1.cmml" xref="S4.E2.m1.*******.3.1">subscript</csymbol><sum id="S4.E2.m1.*******.3.1.2.cmml" xref="S4.E2.m1.*******.3.1.2"></sum><apply id="S4.E2.m1.*******.3.1.3.cmml" xref="S4.E2.m1.*******.3.1.3"><and id="S4.E2.m1.*******.3.1.3a.cmml" xref="S4.E2.m1.*******.3.1.3"></and><apply id="S4.E2.m1.*******.3.1.3b.cmml" xref="S4.E2.m1.*******.3.1.3"><leq id="S4.E2.m1.*******.*******.cmml" xref="S4.E2.m1.*******.*******"></leq><cn id="S4.E2.m1.*******.*******.cmml" type="integer" xref="S4.E2.m1.*******.*******">1</cn><ci id="S4.E2.m1.*******.*******.cmml" xref="S4.E2.m1.*******.*******">𝑖</ci></apply><apply id="S4.E2.m1.*******.3.1.3c.cmml" xref="S4.E2.m1.*******.3.1.3"><leq id="S4.E2.m1.*******.*******.cmml" xref="S4.E2.m1.*******.*******"></leq><share href="https://arxiv.org/html/2107.11972v4#S4.E2.m1.*******.*******.cmml" id="S4.E2.m1.*******.3.1.3d.cmml" xref="S4.E2.m1.*******.3.1.3"></share><ci id="S4.E2.m1.*******.*******.cmml" xref="S4.E2.m1.*******.*******">𝑁</ci></apply></apply></apply><apply id="S4.E2.m1.*******.3.2.cmml" xref="S4.E2.m1.*******.3.2"><ci id="S4.E2.m1.*******.3.2.1.cmml" xref="S4.E2.m1.*******.3.2.1">⋅</ci><apply id="S4.E2.m1.*******.3.2.2.cmml" xref="S4.E2.m1.*******.3.2.2"><csymbol cd="ambiguous" id="S4.E2.m1.*******.*******.cmml" xref="S4.E2.m1.*******.3.2.2">subscript</csymbol><ci id="S4.E2.m1.*******.*******.cmml" xref="S4.E2.m1.*******.*******">𝑦</ci><ci id="S4.E2.m1.*******.*******.cmml" xref="S4.E2.m1.*******.*******">𝑖</ci></apply><apply id="S4.E2.m1.4.4.cmml" xref="S4.E2.m1.4.4"><divide id="S4.E2.m1.4.4.5.cmml" xref="S4.E2.m1.4.4"></divide><apply id="S4.E2.m*******.cmml" xref="S4.E2.m*******"><times id="S4.E2.m1.*******.cmml" xref="S4.E2.m1.*******"></times><ci id="S4.E2.m1.*******.cmml" xref="S4.E2.m1.*******">𝑘</ci><interval closure="open" id="S4.E2.m1.*******.3.cmml" xref="S4.E2.m1.*******.2"><apply id="S4.E2.m*******.1.1.1.cmml" xref="S4.E2.m*******.1.1.1"><csymbol cd="ambiguous" id="S4.E2.m*******.*******.cmml" xref="S4.E2.m*******.1.1.1">subscript</csymbol><ci id="S4.E2.m*******.*******.cmml" xref="S4.E2.m*******.*******">𝒙</ci><ci id="S4.E2.m*******.*******.cmml" xref="S4.E2.m*******.*******">𝑖</ci></apply><apply id="S4.E2.m1.*******.2.2.cmml" xref="S4.E2.m1.*******.2.2"><csymbol cd="ambiguous" id="S4.E2.m*******.*******.cmml" xref="S4.E2.m1.*******.2.2">subscript</csymbol><ci id="S4.E2.m1.*******.2.2.2.cmml" xref="S4.E2.m1.*******.2.2.2">𝒙</ci><ci id="S4.E2.m1.*******.2.2.3.cmml" xref="S4.E2.m1.*******.2.2.3">𝑡</ci></apply></interval></apply><apply id="S4.E2.m1.4.4.4.cmml" xref="S4.E2.m1.4.4.4"><apply id="S4.E2.m1.*******.cmml" xref="S4.E2.m1.*******"><csymbol cd="ambiguous" id="S4.E2.m1.*******.1.cmml" xref="S4.E2.m1.*******">subscript</csymbol><sum id="S4.E2.m1.*******.2.cmml" xref="S4.E2.m1.*******.2"></sum><apply id="S4.E2.m1.*******.3.cmml" xref="S4.E2.m1.*******.3"><and id="S4.E2.m1.*******.3a.cmml" xref="S4.E2.m1.*******.3"></and><apply id="S4.E2.m1.*******.3b.cmml" xref="S4.E2.m1.*******.3"><leq id="S4.E2.m1.*******.3.3.cmml" xref="S4.E2.m1.*******.3.3"></leq><cn id="S4.E2.m1.*******.3.2.cmml" type="integer" xref="S4.E2.m1.*******.3.2">1</cn><ci id="S4.E2.m1.*******.3.4.cmml" xref="S4.E2.m1.*******.3.4">𝑗</ci></apply><apply id="S4.E2.m1.*******.3c.cmml" xref="S4.E2.m1.*******.3"><leq id="S4.E2.m1.*******.3.5.cmml" xref="S4.E2.m1.*******.3.5"></leq><share href="https://arxiv.org/html/2107.11972v4#S4.E2.m1.*******.3.4.cmml" id="S4.E2.m1.*******.3d.cmml" xref="S4.E2.m1.*******.3"></share><ci id="S4.E2.m1.*******.3.6.cmml" xref="S4.E2.m1.*******.3.6">𝑁</ci></apply></apply></apply><apply id="S4.E2.m1.*******.cmml" xref="S4.E2.m1.*******"><times id="S4.E2.m1.4.*******.cmml" xref="S4.E2.m1.4.*******"></times><ci id="S4.E2.m1.4.*******.cmml" xref="S4.E2.m1.4.*******">𝑘</ci><interval closure="open" id="S4.E2.m1.4.*******.3.cmml" xref="S4.E2.m1.4.*******.2"><apply id="S4.E2.m1.3.3.3.*******.cmml" xref="S4.E2.m1.3.3.3.*******"><csymbol cd="ambiguous" id="S4.E2.m1.3.3.3.*******.1.cmml" xref="S4.E2.m1.3.3.3.*******">subscript</csymbol><ci id="S4.E2.m1.3.3.3.*******.2.cmml" xref="S4.E2.m1.3.3.3.*******.2">𝒙</ci><ci id="S4.E2.m1.3.3.3.*******.3.cmml" xref="S4.E2.m1.3.3.3.*******.3">𝑗</ci></apply><apply id="S4.E2.m1.4.4.4.*******.cmml" xref="S4.E2.m1.4.4.4.*******"><csymbol cd="ambiguous" id="S4.E2.m1.*******.*******.cmml" xref="S4.E2.m1.4.4.4.*******">subscript</csymbol><ci id="S4.E2.m1.4.4.4.*******.2.cmml" xref="S4.E2.m1.4.4.4.*******.2">𝒙</ci><ci id="S4.E2.m1.4.4.4.*******.3.cmml" xref="S4.E2.m1.4.4.4.*******.3">𝑡</ci></apply></interval></apply></apply></apply></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.E2.m1.5c">\hat{p}_{{\bm{x}}_{t}}=\sum_{1\leq i\leq N}y_{i}\cdot\frac{k({\bm{x}}_{i},{\bm%
{x}}_{t})}{\sum_{1\leq j\leq N}k({\bm{x}}_{j},{\bm{x}}_{t})}\quad,</annotation><annotation encoding="application/x-llamapun" id="S4.E2.m1.5d">over^ start_ARG italic_p end_ARG start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT = ∑ start_POSTSUBSCRIPT 1 ≤ italic_i ≤ italic_N end_POSTSUBSCRIPT italic_y start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT ⋅ divide start_ARG italic_k ( bold_italic_x start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT , bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT ) end_ARG start_ARG ∑ start_POSTSUBSCRIPT 1 ≤ italic_j ≤ italic_N end_POSTSUBSCRIPT italic_k ( bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT , bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT ) end_ARG ,</annotation></semantics></math></td>
<td class="ltx_eqn_cell ltx_eqn_center_padright"></td>
<td class="ltx_eqn_cell ltx_eqn_eqno ltx_align_middle ltx_align_right" rowspan="1"><span class="ltx_tag ltx_tag_equation ltx_align_right">(2)</span></td>
</tr></tbody>
</table>
<p class="ltx_p" id="S4.SS1.SSS0.Px1.p1.16">where <math alttext="N" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px1.p1.11.m1.1"><semantics id="S4.SS1.SSS0.Px1.p1.11.m1.1a"><mi id="S4.SS1.SSS0.Px1.p1.11.m1.1.1" xref="S4.SS1.SSS0.Px1.p1.11.m1.1.1.cmml">N</mi><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px1.p1.11.m1.1b"><ci id="S4.SS1.SSS0.Px1.p1.11.m1.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.11.m1.1.1">𝑁</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px1.p1.11.m1.1c">N</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px1.p1.11.m1.1d">italic_N</annotation></semantics></math> is the size of training data, and <math alttext="k({\bm{x}}_{i},{\bm{x}}_{t})" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px1.p1.12.m2.2"><semantics id="S4.SS1.SSS0.Px1.p1.12.m2.2a"><mrow id="S4.SS1.SSS0.Px1.p1.12.m2.2.2" xref="S4.SS1.SSS0.Px1.p1.12.m2.2.2.cmml"><mi id="S4.SS1.SSS0.Px1.p1.12.m*******" xref="S4.SS1.SSS0.Px1.p1.12.m*******.cmml">k</mi><mo id="S4.SS1.SSS0.Px1.p1.12.m*******" xref="S4.SS1.SSS0.Px1.p1.12.m*******.cmml">⁢</mo><mrow id="S4.SS1.SSS0.Px1.p1.12.m*******.2" xref="S4.SS1.SSS0.Px1.p1.12.m*******.3.cmml"><mo id="S4.SS1.SSS0.Px1.p1.12.m*******.2.3" stretchy="false" xref="S4.SS1.SSS0.Px1.p1.12.m*******.3.cmml">(</mo><msub id="S4.SS1.SSS0.Px1.p1.12.m2.*******.1" xref="S4.SS1.SSS0.Px1.p1.12.m2.*******.1.cmml"><mi id="S4.SS1.SSS0.Px1.p1.12.m2.*******.1.2" xref="S4.SS1.SSS0.Px1.p1.12.m2.*******.1.2.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px1.p1.12.m2.*******.1.3" xref="S4.SS1.SSS0.Px1.p1.12.m2.*******.1.3.cmml">i</mi></msub><mo id="S4.SS1.SSS0.Px1.p1.12.m*******.2.4" xref="S4.SS1.SSS0.Px1.p1.12.m*******.3.cmml">,</mo><msub id="S4.SS1.SSS0.Px1.p1.12.m*******.2.2" xref="S4.SS1.SSS0.Px1.p1.12.m*******.2.2.cmml"><mi id="S4.SS1.SSS0.Px1.p1.12.m*******.2.2.2" xref="S4.SS1.SSS0.Px1.p1.12.m*******.2.2.2.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px1.p1.12.m*******.2.2.3" xref="S4.SS1.SSS0.Px1.p1.12.m*******.2.2.3.cmml">t</mi></msub><mo id="S4.SS1.SSS0.Px1.p1.12.m*******.2.5" stretchy="false" xref="S4.SS1.SSS0.Px1.p1.12.m*******.3.cmml">)</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px1.p1.12.m2.2b"><apply id="S4.SS1.SSS0.Px1.p1.12.m2.2.2.cmml" xref="S4.SS1.SSS0.Px1.p1.12.m2.2.2"><times id="S4.SS1.SSS0.Px1.p1.12.m*******.cmml" xref="S4.SS1.SSS0.Px1.p1.12.m*******"></times><ci id="S4.SS1.SSS0.Px1.p1.12.m*******.cmml" xref="S4.SS1.SSS0.Px1.p1.12.m*******">𝑘</ci><interval closure="open" id="S4.SS1.SSS0.Px1.p1.12.m*******.3.cmml" xref="S4.SS1.SSS0.Px1.p1.12.m*******.2"><apply id="S4.SS1.SSS0.Px1.p1.12.m2.*******.1.cmml" xref="S4.SS1.SSS0.Px1.p1.12.m2.*******.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.12.m2.*******.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.12.m2.*******.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.12.m2.*******.1.2.cmml" xref="S4.SS1.SSS0.Px1.p1.12.m2.*******.1.2">𝒙</ci><ci id="S4.SS1.SSS0.Px1.p1.12.m2.*******.1.3.cmml" xref="S4.SS1.SSS0.Px1.p1.12.m2.*******.1.3">𝑖</ci></apply><apply id="S4.SS1.SSS0.Px1.p1.12.m*******.2.2.cmml" xref="S4.SS1.SSS0.Px1.p1.12.m*******.2.2"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.12.m2.2.2.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.12.m*******.2.2">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.12.m*******.2.2.2.cmml" xref="S4.SS1.SSS0.Px1.p1.12.m*******.2.2.2">𝒙</ci><ci id="S4.SS1.SSS0.Px1.p1.12.m*******.2.2.3.cmml" xref="S4.SS1.SSS0.Px1.p1.12.m*******.2.2.3">𝑡</ci></apply></interval></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px1.p1.12.m2.2c">k({\bm{x}}_{i},{\bm{x}}_{t})</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px1.p1.12.m2.2d">italic_k ( bold_italic_x start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT , bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT )</annotation></semantics></math> is the attention weight between <math alttext="{\bm{x}}_{i}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px1.p1.13.m3.1"><semantics id="S4.SS1.SSS0.Px1.p1.13.m3.1a"><msub id="S4.SS1.SSS0.Px1.p1.13.m3.1.1" xref="S4.SS1.SSS0.Px1.p1.13.m3.1.1.cmml"><mi id="S4.SS1.SSS0.Px1.p1.13.m3.1.1.2" xref="S4.SS1.SSS0.Px1.p1.13.m3.1.1.2.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px1.p1.13.m3.1.1.3" xref="S4.SS1.SSS0.Px1.p1.13.m3.1.1.3.cmml">i</mi></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px1.p1.13.m3.1b"><apply id="S4.SS1.SSS0.Px1.p1.13.m3.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.13.m3.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.13.m3.1.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.13.m3.1.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.13.m3.1.1.2.cmml" xref="S4.SS1.SSS0.Px1.p1.13.m3.1.1.2">𝒙</ci><ci id="S4.SS1.SSS0.Px1.p1.13.m3.1.1.3.cmml" xref="S4.SS1.SSS0.Px1.p1.13.m3.1.1.3">𝑖</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px1.p1.13.m3.1c">{\bm{x}}_{i}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px1.p1.13.m3.1d">bold_italic_x start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT</annotation></semantics></math> and <math alttext="{\bm{x}}_{t}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px1.p1.14.m4.1"><semantics id="S4.SS1.SSS0.Px1.p1.14.m4.1a"><msub id="S4.SS1.SSS0.Px1.p1.14.m4.1.1" xref="S4.SS1.SSS0.Px1.p1.14.m4.1.1.cmml"><mi id="S4.SS1.SSS0.Px1.p1.14.m4.1.1.2" xref="S4.SS1.SSS0.Px1.p1.14.m4.1.1.2.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px1.p1.14.m4.1.1.3" xref="S4.SS1.SSS0.Px1.p1.14.m4.1.1.3.cmml">t</mi></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px1.p1.14.m4.1b"><apply id="S4.SS1.SSS0.Px1.p1.14.m4.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.14.m4.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.14.m4.1.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.14.m4.1.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.14.m4.1.1.2.cmml" xref="S4.SS1.SSS0.Px1.p1.14.m4.1.1.2">𝒙</ci><ci id="S4.SS1.SSS0.Px1.p1.14.m4.1.1.3.cmml" xref="S4.SS1.SSS0.Px1.p1.14.m4.1.1.3">𝑡</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px1.p1.14.m4.1c">{\bm{x}}_{t}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px1.p1.14.m4.1d">bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT</annotation></semantics></math>.
However, in the most general attention mechanisms, the model allows each sample to attend to every other sample.
Intuitively, closer neighbors of a query sample have greater <em class="ltx_emph ltx_font_italic" id="S4.SS1.SSS0.Px1.p1.16.1">similarity</em> than neighbors which are further away.
We inject the locality structure into the module by performing the <span class="ltx_text ltx_font_italic" id="S4.SS1.SSS0.Px1.p1.16.2">masked attention</span> scheme and merely paying attention to the neighbors <math alttext="\mathcal{N}({\bm{x}}_{t})" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px1.p1.15.m5.1"><semantics id="S4.SS1.SSS0.Px1.p1.15.m5.1a"><mrow id="S4.SS1.SSS0.Px1.p1.15.m5.1.1" xref="S4.SS1.SSS0.Px1.p1.15.m5.1.1.cmml"><mi class="ltx_font_mathcaligraphic" id="S4.SS1.SSS0.Px1.p1.15.m5.1.1.3" xref="S4.SS1.SSS0.Px1.p1.15.m5.1.1.3.cmml">𝒩</mi><mo id="S4.SS1.SSS0.Px1.p1.15.m5.1.1.2" xref="S4.SS1.SSS0.Px1.p1.15.m5.1.1.2.cmml">⁢</mo><mrow id="S4.SS1.SSS0.Px1.p1.15.m5.*******" xref="S4.SS1.SSS0.Px1.p1.15.m5.*******.1.cmml"><mo id="S4.SS1.SSS0.Px1.p1.15.m5.*******.2" stretchy="false" xref="S4.SS1.SSS0.Px1.p1.15.m5.*******.1.cmml">(</mo><msub id="S4.SS1.SSS0.Px1.p1.15.m5.*******.1" xref="S4.SS1.SSS0.Px1.p1.15.m5.*******.1.cmml"><mi id="S4.SS1.SSS0.Px1.p1.15.m5.*******.1.2" xref="S4.SS1.SSS0.Px1.p1.15.m5.*******.1.2.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px1.p1.15.m5.*******.1.3" xref="S4.SS1.SSS0.Px1.p1.15.m5.*******.1.3.cmml">t</mi></msub><mo id="S4.SS1.SSS0.Px1.p1.15.m5.*******.3" stretchy="false" xref="S4.SS1.SSS0.Px1.p1.15.m5.*******.1.cmml">)</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px1.p1.15.m5.1b"><apply id="S4.SS1.SSS0.Px1.p1.15.m5.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.15.m5.1.1"><times id="S4.SS1.SSS0.Px1.p1.15.m5.1.1.2.cmml" xref="S4.SS1.SSS0.Px1.p1.15.m5.1.1.2"></times><ci id="S4.SS1.SSS0.Px1.p1.15.m5.1.1.3.cmml" xref="S4.SS1.SSS0.Px1.p1.15.m5.1.1.3">𝒩</ci><apply id="S4.SS1.SSS0.Px1.p1.15.m5.*******.1.cmml" xref="S4.SS1.SSS0.Px1.p1.15.m5.*******"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.15.m5.*******.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.15.m5.*******">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.15.m5.*******.1.2.cmml" xref="S4.SS1.SSS0.Px1.p1.15.m5.*******.1.2">𝒙</ci><ci id="S4.SS1.SSS0.Px1.p1.15.m5.*******.1.3.cmml" xref="S4.SS1.SSS0.Px1.p1.15.m5.*******.1.3">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px1.p1.15.m5.1c">\mathcal{N}({\bm{x}}_{t})</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px1.p1.15.m5.1d">caligraphic_N ( bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT )</annotation></semantics></math> of <math alttext="{\bm{x}}_{t}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px1.p1.16.m6.1"><semantics id="S4.SS1.SSS0.Px1.p1.16.m6.1a"><msub id="S4.SS1.SSS0.Px1.p1.16.m6.1.1" xref="S4.SS1.SSS0.Px1.p1.16.m6.1.1.cmml"><mi id="S4.SS1.SSS0.Px1.p1.16.m6.1.1.2" xref="S4.SS1.SSS0.Px1.p1.16.m6.1.1.2.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px1.p1.16.m6.1.1.3" xref="S4.SS1.SSS0.Px1.p1.16.m6.1.1.3.cmml">t</mi></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px1.p1.16.m6.1b"><apply id="S4.SS1.SSS0.Px1.p1.16.m6.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.16.m6.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.16.m6.1.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.16.m6.1.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.16.m6.1.1.2.cmml" xref="S4.SS1.SSS0.Px1.p1.16.m6.1.1.2">𝒙</ci><ci id="S4.SS1.SSS0.Px1.p1.16.m6.1.1.3.cmml" xref="S4.SS1.SSS0.Px1.p1.16.m6.1.1.3">𝑡</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px1.p1.16.m6.1c">{\bm{x}}_{t}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px1.p1.16.m6.1d">bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT</annotation></semantics></math>. Then we reformulate the <em class="ltx_emph ltx_font_italic" id="S4.SS1.SSS0.Px1.p1.16.3">localization module</em> as follows:</p>
<table class="ltx_equation ltx_eqn_table" id="S4.E3">
<tbody><tr class="ltx_equation ltx_eqn_row ltx_align_baseline">
<td class="ltx_eqn_cell ltx_eqn_center_padleft"></td>
<td class="ltx_eqn_cell ltx_align_center"><math alttext="\hat{p}_{{\bm{x}}_{t}}=\sum_{{\bm{z}}_{i}\in\mathcal{N}({\bm{x}}_{t})}y_{{\bm{%
z}}_{i}}\cdot\frac{k({\bm{z}}_{i},{\bm{x}}_{t})}{\sum_{{\bm{z}}_{j}\in\mathcal%
{N}({\bm{x}}_{t})}k({\bm{z}}_{j},{\bm{x}}_{t})}\quad." class="ltx_Math" display="block" id="S4.E3.m1.7"><semantics id="S4.E3.m1.7a"><mrow id="S4.E3.m1.7.7.1" xref="S4.E3.m1.*******.cmml"><mrow id="S4.E3.m1.*******" xref="S4.E3.m1.*******.cmml"><msub id="S4.E3.m1.*******.2" xref="S4.E3.m1.*******.2.cmml"><mover accent="true" id="S4.E3.m1.7.7.*******" xref="S4.E3.m1.7.7.*******.cmml"><mi id="S4.E3.m1.7.7.*******.2" xref="S4.E3.m1.7.7.*******.2.cmml">p</mi><mo id="S4.E3.m1.7.7.*******.1" xref="S4.E3.m1.7.7.*******.1.cmml">^</mo></mover><msub id="S4.E3.m1.7.7.*******" xref="S4.E3.m1.7.7.*******.cmml"><mi id="S4.E3.m1.7.7.*******.2" xref="S4.E3.m1.7.7.*******.2.cmml">𝒙</mi><mi id="S4.E3.m1.7.7.*******.3" xref="S4.E3.m1.7.7.*******.3.cmml">t</mi></msub></msub><mo id="S4.E3.m1.*******.1" rspace="0.111em" xref="S4.E3.m1.*******.1.cmml">=</mo><mrow id="S4.E3.m1.*******.3" xref="S4.E3.m1.*******.3.cmml"><munder id="S4.E3.m1.7.7.*******" xref="S4.E3.m1.7.7.*******.cmml"><mo id="S4.E3.m1.7.7.*******.2" movablelimits="false" xref="S4.E3.m1.7.7.*******.2.cmml">∑</mo><mrow id="S4.E3.m*******" xref="S4.E3.m*******.cmml"><msub id="S4.E3.m*******.3" xref="S4.E3.m*******.3.cmml"><mi id="S4.E3.m1.1.*******" xref="S4.E3.m1.1.*******.cmml">𝒛</mi><mi id="S4.E3.m*******.3.3" xref="S4.E3.m*******.3.3.cmml">i</mi></msub><mo id="S4.E3.m*******.2" xref="S4.E3.m*******.2.cmml">∈</mo><mrow id="S4.E3.m*******.1" xref="S4.E3.m*******.1.cmml"><mi class="ltx_font_mathcaligraphic" id="S4.E3.m*******.1.3" xref="S4.E3.m*******.1.3.cmml">𝒩</mi><mo id="S4.E3.m*******.1.2" xref="S4.E3.m*******.1.2.cmml">⁢</mo><mrow id="S4.E3.m*******.1.1.1" xref="S4.E3.m*******.*******.cmml"><mo id="S4.E3.m*******.*******" stretchy="false" xref="S4.E3.m*******.*******.cmml">(</mo><msub id="S4.E3.m*******.*******" xref="S4.E3.m*******.*******.cmml"><mi id="S4.E3.m*******.*******.2" xref="S4.E3.m*******.*******.2.cmml">𝒙</mi><mi id="S4.E3.m*******.*******.3" xref="S4.E3.m*******.*******.3.cmml">t</mi></msub><mo id="S4.E3.m*******.*******" stretchy="false" xref="S4.E3.m*******.*******.cmml">)</mo></mrow></mrow></mrow></munder><mrow id="S4.E3.m1.7.7.*******" xref="S4.E3.m1.7.7.*******.cmml"><msub id="S4.E3.m1.7.7.*******.2" xref="S4.E3.m1.7.7.*******.2.cmml"><mi id="S4.E3.m1.7.7.*******.2.2" xref="S4.E3.m1.7.7.*******.2.2.cmml">y</mi><msub id="S4.E3.m1.7.7.*******.2.3" xref="S4.E3.m1.7.7.*******.2.3.cmml"><mi id="S4.E3.m1.7.7.*******.2.3.2" xref="S4.E3.m1.7.7.*******.2.3.2.cmml">𝒛</mi><mi id="S4.E3.m1.7.7.*******.2.3.3" xref="S4.E3.m1.7.7.*******.2.3.3.cmml">i</mi></msub></msub><mo id="S4.E3.m1.7.7.*******.1" lspace="0.222em" rspace="0.222em" xref="S4.E3.m1.7.7.*******.1.cmml">⋅</mo><mfrac id="S4.E3.m1.6.6" xref="S4.E3.m1.6.6.cmml"><mrow id="S4.E3.m1.3.3.2" xref="S4.E3.m1.3.3.2.cmml"><mi id="S4.E3.m1.*******" xref="S4.E3.m1.*******.cmml">k</mi><mo id="S4.E3.m1.*******" xref="S4.E3.m1.*******.cmml">⁢</mo><mrow id="S4.E3.m1.*******.2" xref="S4.E3.m1.*******.3.cmml"><mo id="S4.E3.m1.*******.2.3" stretchy="false" xref="S4.E3.m1.*******.3.cmml">(</mo><msub id="S4.E3.m1.*******.1.1" xref="S4.E3.m1.*******.1.1.cmml"><mi id="S4.E3.m1.*******.1.1.2" xref="S4.E3.m1.*******.1.1.2.cmml">𝒛</mi><mi id="S4.E3.m1.*******.1.1.3" xref="S4.E3.m1.*******.1.1.3.cmml">i</mi></msub><mo id="S4.E3.m1.*******.2.4" xref="S4.E3.m1.*******.3.cmml">,</mo><msub id="S4.E3.m1.*******.2.2" xref="S4.E3.m1.*******.2.2.cmml"><mi id="S4.E3.m1.*******.2.2.2" xref="S4.E3.m1.*******.2.2.2.cmml">𝒙</mi><mi id="S4.E3.m1.*******.2.2.3" xref="S4.E3.m1.*******.2.2.3.cmml">t</mi></msub><mo id="S4.E3.m1.*******.2.5" stretchy="false" xref="S4.E3.m1.*******.3.cmml">)</mo></mrow></mrow><mrow id="S4.E3.m1.6.6.5" xref="S4.E3.m1.6.6.5.cmml"><msub id="S4.E3.m1.6.6.5.4" xref="S4.E3.m1.6.6.5.4.cmml"><mo id="S4.E3.m1.6.6.5.4.2" xref="S4.E3.m1.6.6.5.4.2.cmml">∑</mo><mrow id="S4.E3.m1.4.4.3.1.1" xref="S4.E3.m1.4.4.3.1.1.cmml"><msub id="S4.E3.m1.4.4.3.1.1.3" xref="S4.E3.m1.4.4.3.1.1.3.cmml"><mi id="S4.E3.m1.4.4.3.*******" xref="S4.E3.m1.4.4.3.*******.cmml">𝒛</mi><mi id="S4.E3.m1.4.4.3.*******" xref="S4.E3.m1.4.4.3.*******.cmml">j</mi></msub><mo id="S4.E3.m1.4.4.3.1.1.2" xref="S4.E3.m1.4.4.3.1.1.2.cmml">∈</mo><mrow id="S4.E3.m1.4.4.3.1.1.1" xref="S4.E3.m1.4.4.3.1.1.1.cmml"><mi class="ltx_font_mathcaligraphic" id="S4.E3.m1.4.4.3.*******" xref="S4.E3.m1.4.4.3.*******.cmml">𝒩</mi><mo id="S4.E3.m1.4.4.3.*******" xref="S4.E3.m1.4.4.3.*******.cmml">⁢</mo><mrow id="S4.E3.m1.4.4.3.*******.1" xref="S4.E3.m1.4.4.3.*******.1.1.cmml"><mo id="S4.E3.m1.4.4.3.*******.1.2" stretchy="false" xref="S4.E3.m1.4.4.3.*******.1.1.cmml">(</mo><msub id="S4.E3.m1.4.4.3.*******.1.1" xref="S4.E3.m1.4.4.3.*******.1.1.cmml"><mi id="S4.E3.m1.4.4.3.*******.1.1.2" xref="S4.E3.m1.4.4.3.*******.1.1.2.cmml">𝒙</mi><mi id="S4.E3.m1.4.4.3.*******.1.1.3" xref="S4.E3.m1.4.4.3.*******.1.1.3.cmml">t</mi></msub><mo id="S4.E3.m1.4.4.3.*******.1.3" stretchy="false" xref="S4.E3.m1.4.4.3.*******.1.1.cmml">)</mo></mrow></mrow></mrow></msub><mrow id="S4.E3.m1.6.6.5.3" xref="S4.E3.m1.6.6.5.3.cmml"><mi id="S4.E3.m1.6.6.5.3.4" xref="S4.E3.m1.6.6.5.3.4.cmml">k</mi><mo id="S4.E3.m1.6.6.5.3.3" xref="S4.E3.m1.6.6.5.3.3.cmml">⁢</mo><mrow id="S4.E3.m1.6.6.5.3.2.2" xref="S4.E3.m1.6.6.5.3.2.3.cmml"><mo id="S4.E3.m1.6.6.5.*******" stretchy="false" xref="S4.E3.m1.6.6.5.3.2.3.cmml">(</mo><msub id="S4.E3.m1.5.5.4.2.1.1.1" xref="S4.E3.m1.5.5.4.2.1.1.1.cmml"><mi id="S4.E3.m1.5.5.4.2.*******" xref="S4.E3.m1.5.5.4.2.*******.cmml">𝒛</mi><mi id="S4.E3.m1.5.5.4.2.*******" xref="S4.E3.m1.5.5.4.2.*******.cmml">j</mi></msub><mo id="S4.E3.m1.6.6.5.3.2.2.4" xref="S4.E3.m1.6.6.5.3.2.3.cmml">,</mo><msub id="S4.E3.m1.6.6.5.*******" xref="S4.E3.m1.6.6.5.*******.cmml"><mi id="S4.E3.m1.6.6.5.3.*******" xref="S4.E3.m1.6.6.5.3.*******.cmml">𝒙</mi><mi id="S4.E3.m1.6.6.5.3.*******" xref="S4.E3.m1.6.6.5.3.*******.cmml">t</mi></msub><mo id="S4.E3.m1.6.6.5.3.2.2.5" stretchy="false" xref="S4.E3.m1.6.6.5.3.2.3.cmml">)</mo></mrow></mrow></mrow></mfrac></mrow></mrow></mrow><mspace id="S4.E3.m1.*******" width="1.000em" xref="S4.E3.m1.*******.cmml"></mspace><mo id="S4.E3.m1.*******" xref="S4.E3.m1.*******.cmml">.</mo></mrow><annotation-xml encoding="MathML-Content" id="S4.E3.m1.7b"><apply id="S4.E3.m1.*******.cmml" xref="S4.E3.m1.7.7.1"><eq id="S4.E3.m1.*******.1.cmml" xref="S4.E3.m1.*******.1"></eq><apply id="S4.E3.m1.*******.2.cmml" xref="S4.E3.m1.*******.2"><csymbol cd="ambiguous" id="S4.E3.m1.7.7.*******.cmml" xref="S4.E3.m1.*******.2">subscript</csymbol><apply id="S4.E3.m1.7.7.*******.cmml" xref="S4.E3.m1.7.7.*******"><ci id="S4.E3.m1.7.7.*******.1.cmml" xref="S4.E3.m1.7.7.*******.1">^</ci><ci id="S4.E3.m1.7.7.*******.2.cmml" xref="S4.E3.m1.7.7.*******.2">𝑝</ci></apply><apply id="S4.E3.m1.7.7.*******.cmml" xref="S4.E3.m1.7.7.*******"><csymbol cd="ambiguous" id="S4.E3.m1.7.7.*******.1.cmml" xref="S4.E3.m1.7.7.*******">subscript</csymbol><ci id="S4.E3.m1.7.7.*******.2.cmml" xref="S4.E3.m1.7.7.*******.2">𝒙</ci><ci id="S4.E3.m1.7.7.*******.3.cmml" xref="S4.E3.m1.7.7.*******.3">𝑡</ci></apply></apply><apply id="S4.E3.m1.*******.3.cmml" xref="S4.E3.m1.*******.3"><apply id="S4.E3.m1.7.7.*******.cmml" xref="S4.E3.m1.7.7.*******"><csymbol cd="ambiguous" id="S4.E3.m1.7.7.*******.1.cmml" xref="S4.E3.m1.7.7.*******">subscript</csymbol><sum id="S4.E3.m1.7.7.*******.2.cmml" xref="S4.E3.m1.7.7.*******.2"></sum><apply id="S4.E3.m*******.cmml" xref="S4.E3.m*******"><in id="S4.E3.m*******.2.cmml" xref="S4.E3.m*******.2"></in><apply id="S4.E3.m*******.3.cmml" xref="S4.E3.m*******.3"><csymbol cd="ambiguous" id="S4.E3.m*******.3.1.cmml" xref="S4.E3.m*******.3">subscript</csymbol><ci id="S4.E3.m1.1.*******.cmml" xref="S4.E3.m1.1.*******">𝒛</ci><ci id="S4.E3.m*******.3.3.cmml" xref="S4.E3.m*******.3.3">𝑖</ci></apply><apply id="S4.E3.m*******.1.cmml" xref="S4.E3.m*******.1"><times id="S4.E3.m*******.1.2.cmml" xref="S4.E3.m*******.1.2"></times><ci id="S4.E3.m*******.1.3.cmml" xref="S4.E3.m*******.1.3">𝒩</ci><apply id="S4.E3.m*******.*******.cmml" xref="S4.E3.m*******.1.1.1"><csymbol cd="ambiguous" id="S4.E3.m*******.*******.1.cmml" xref="S4.E3.m*******.1.1.1">subscript</csymbol><ci id="S4.E3.m*******.*******.2.cmml" xref="S4.E3.m*******.*******.2">𝒙</ci><ci id="S4.E3.m*******.*******.3.cmml" xref="S4.E3.m*******.*******.3">𝑡</ci></apply></apply></apply></apply><apply id="S4.E3.m1.7.7.*******.cmml" xref="S4.E3.m1.7.7.*******"><ci id="S4.E3.m1.7.7.*******.1.cmml" xref="S4.E3.m1.7.7.*******.1">⋅</ci><apply id="S4.E3.m1.7.7.*******.2.cmml" xref="S4.E3.m1.7.7.*******.2"><csymbol cd="ambiguous" id="S4.E3.m1.7.7.*******.2.1.cmml" xref="S4.E3.m1.7.7.*******.2">subscript</csymbol><ci id="S4.E3.m1.7.7.*******.2.2.cmml" xref="S4.E3.m1.7.7.*******.2.2">𝑦</ci><apply id="S4.E3.m1.7.7.*******.2.3.cmml" xref="S4.E3.m1.7.7.*******.2.3"><csymbol cd="ambiguous" id="S4.E3.m1.7.7.*******.2.3.1.cmml" xref="S4.E3.m1.7.7.*******.2.3">subscript</csymbol><ci id="S4.E3.m1.7.7.*******.2.3.2.cmml" xref="S4.E3.m1.7.7.*******.2.3.2">𝒛</ci><ci id="S4.E3.m1.7.7.*******.2.3.3.cmml" xref="S4.E3.m1.7.7.*******.2.3.3">𝑖</ci></apply></apply><apply id="S4.E3.m1.6.6.cmml" xref="S4.E3.m1.6.6"><divide id="S4.E3.m1.6.6.6.cmml" xref="S4.E3.m1.6.6"></divide><apply id="S4.E3.m1.3.3.2.cmml" xref="S4.E3.m1.3.3.2"><times id="S4.E3.m1.*******.cmml" xref="S4.E3.m1.*******"></times><ci id="S4.E3.m1.*******.cmml" xref="S4.E3.m1.*******">𝑘</ci><interval closure="open" id="S4.E3.m1.*******.3.cmml" xref="S4.E3.m1.*******.2"><apply id="S4.E3.m1.*******.1.1.cmml" xref="S4.E3.m1.*******.1.1"><csymbol cd="ambiguous" id="S4.E3.m1.*******.1.1.1.cmml" xref="S4.E3.m1.*******.1.1">subscript</csymbol><ci id="S4.E3.m1.*******.1.1.2.cmml" xref="S4.E3.m1.*******.1.1.2">𝒛</ci><ci id="S4.E3.m1.*******.1.1.3.cmml" xref="S4.E3.m1.*******.1.1.3">𝑖</ci></apply><apply id="S4.E3.m1.*******.2.2.cmml" xref="S4.E3.m1.*******.2.2"><csymbol cd="ambiguous" id="S4.E3.m1.*******.2.2.1.cmml" xref="S4.E3.m1.*******.2.2">subscript</csymbol><ci id="S4.E3.m1.*******.2.2.2.cmml" xref="S4.E3.m1.*******.2.2.2">𝒙</ci><ci id="S4.E3.m1.*******.2.2.3.cmml" xref="S4.E3.m1.*******.2.2.3">𝑡</ci></apply></interval></apply><apply id="S4.E3.m1.6.6.5.cmml" xref="S4.E3.m1.6.6.5"><apply id="S4.E3.m1.6.6.5.4.cmml" xref="S4.E3.m1.6.6.5.4"><csymbol cd="ambiguous" id="S4.E3.m1.6.6.5.4.1.cmml" xref="S4.E3.m1.6.6.5.4">subscript</csymbol><sum id="S4.E3.m1.6.6.5.4.2.cmml" xref="S4.E3.m1.6.6.5.4.2"></sum><apply id="S4.E3.m1.4.4.3.1.1.cmml" xref="S4.E3.m1.4.4.3.1.1"><in id="S4.E3.m1.4.4.3.1.1.2.cmml" xref="S4.E3.m1.4.4.3.1.1.2"></in><apply id="S4.E3.m1.4.4.3.1.1.3.cmml" xref="S4.E3.m1.4.4.3.1.1.3"><csymbol cd="ambiguous" id="S4.E3.m1.4.4.3.*******.cmml" xref="S4.E3.m1.4.4.3.1.1.3">subscript</csymbol><ci id="S4.E3.m1.4.4.3.*******.cmml" xref="S4.E3.m1.4.4.3.*******">𝒛</ci><ci id="S4.E3.m1.4.4.3.*******.cmml" xref="S4.E3.m1.4.4.3.*******">𝑗</ci></apply><apply id="S4.E3.m1.4.4.3.1.1.1.cmml" xref="S4.E3.m1.4.4.3.1.1.1"><times id="S4.E3.m1.4.4.3.*******.cmml" xref="S4.E3.m1.4.4.3.*******"></times><ci id="S4.E3.m1.4.4.3.*******.cmml" xref="S4.E3.m1.4.4.3.*******">𝒩</ci><apply id="S4.E3.m1.4.4.3.*******.1.1.cmml" xref="S4.E3.m1.4.4.3.*******.1"><csymbol cd="ambiguous" id="S4.E3.m1.4.4.3.*******.1.1.1.cmml" xref="S4.E3.m1.4.4.3.*******.1">subscript</csymbol><ci id="S4.E3.m1.4.4.3.*******.1.1.2.cmml" xref="S4.E3.m1.4.4.3.*******.1.1.2">𝒙</ci><ci id="S4.E3.m1.4.4.3.*******.1.1.3.cmml" xref="S4.E3.m1.4.4.3.*******.1.1.3">𝑡</ci></apply></apply></apply></apply><apply id="S4.E3.m1.6.6.5.3.cmml" xref="S4.E3.m1.6.6.5.3"><times id="S4.E3.m1.6.6.5.3.3.cmml" xref="S4.E3.m1.6.6.5.3.3"></times><ci id="S4.E3.m1.6.6.5.3.4.cmml" xref="S4.E3.m1.6.6.5.3.4">𝑘</ci><interval closure="open" id="S4.E3.m1.6.6.5.3.2.3.cmml" xref="S4.E3.m1.6.6.5.3.2.2"><apply id="S4.E3.m1.5.5.4.2.1.1.1.cmml" xref="S4.E3.m1.5.5.4.2.1.1.1"><csymbol cd="ambiguous" id="S4.E3.m1.5.5.4.2.*******.cmml" xref="S4.E3.m1.5.5.4.2.1.1.1">subscript</csymbol><ci id="S4.E3.m1.5.5.4.2.*******.cmml" xref="S4.E3.m1.5.5.4.2.*******">𝒛</ci><ci id="S4.E3.m1.5.5.4.2.*******.cmml" xref="S4.E3.m1.5.5.4.2.*******">𝑗</ci></apply><apply id="S4.E3.m1.6.6.5.*******.cmml" xref="S4.E3.m1.6.6.5.*******"><csymbol cd="ambiguous" id="S4.E3.m1.6.6.5.3.*******.cmml" xref="S4.E3.m1.6.6.5.*******">subscript</csymbol><ci id="S4.E3.m1.6.6.5.3.*******.cmml" xref="S4.E3.m1.6.6.5.3.*******">𝒙</ci><ci id="S4.E3.m1.6.6.5.3.*******.cmml" xref="S4.E3.m1.6.6.5.3.*******">𝑡</ci></apply></interval></apply></apply></apply></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.E3.m1.7c">\hat{p}_{{\bm{x}}_{t}}=\sum_{{\bm{z}}_{i}\in\mathcal{N}({\bm{x}}_{t})}y_{{\bm{%
z}}_{i}}\cdot\frac{k({\bm{z}}_{i},{\bm{x}}_{t})}{\sum_{{\bm{z}}_{j}\in\mathcal%
{N}({\bm{x}}_{t})}k({\bm{z}}_{j},{\bm{x}}_{t})}\quad.</annotation><annotation encoding="application/x-llamapun" id="S4.E3.m1.7d">over^ start_ARG italic_p end_ARG start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT = ∑ start_POSTSUBSCRIPT bold_italic_z start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT ∈ caligraphic_N ( bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT ) end_POSTSUBSCRIPT italic_y start_POSTSUBSCRIPT bold_italic_z start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT end_POSTSUBSCRIPT ⋅ divide start_ARG italic_k ( bold_italic_z start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT , bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT ) end_ARG start_ARG ∑ start_POSTSUBSCRIPT bold_italic_z start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT ∈ caligraphic_N ( bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT ) end_POSTSUBSCRIPT italic_k ( bold_italic_z start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT , bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT ) end_ARG .</annotation></semantics></math></td>
<td class="ltx_eqn_cell ltx_eqn_center_padright"></td>
<td class="ltx_eqn_cell ltx_eqn_eqno ltx_align_middle ltx_align_right" rowspan="1"><span class="ltx_tag ltx_tag_equation ltx_align_right">(3)</span></td>
</tr></tbody>
</table>
<p class="ltx_p" id="S4.SS1.SSS0.Px1.p1.18">We propose two schemes (detailed in Appendix) to define the neighbors <math alttext="\mathcal{N}({\bm{x}}_{t})" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px1.p1.17.m1.1"><semantics id="S4.SS1.SSS0.Px1.p1.17.m1.1a"><mrow id="S4.SS1.SSS0.Px1.p1.17.m1.1.1" xref="S4.SS1.SSS0.Px1.p1.17.m1.1.1.cmml"><mi class="ltx_font_mathcaligraphic" id="S4.SS1.SSS0.Px1.p1.17.m*******" xref="S4.SS1.SSS0.Px1.p1.17.m*******.cmml">𝒩</mi><mo id="S4.SS1.SSS0.Px1.p1.17.m*******" xref="S4.SS1.SSS0.Px1.p1.17.m*******.cmml">⁢</mo><mrow id="S4.SS1.SSS0.Px1.p1.17.m*******.1" xref="S4.SS1.SSS0.Px1.p1.17.m*******.1.1.cmml"><mo id="S4.SS1.SSS0.Px1.p1.17.m*******.1.2" stretchy="false" xref="S4.SS1.SSS0.Px1.p1.17.m*******.1.1.cmml">(</mo><msub id="S4.SS1.SSS0.Px1.p1.17.m*******.1.1" xref="S4.SS1.SSS0.Px1.p1.17.m*******.1.1.cmml"><mi id="S4.SS1.SSS0.Px1.p1.17.m*******.1.1.2" xref="S4.SS1.SSS0.Px1.p1.17.m*******.1.1.2.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px1.p1.17.m*******.1.1.3" xref="S4.SS1.SSS0.Px1.p1.17.m*******.1.1.3.cmml">t</mi></msub><mo id="S4.SS1.SSS0.Px1.p1.17.m*******.1.3" stretchy="false" xref="S4.SS1.SSS0.Px1.p1.17.m*******.1.1.cmml">)</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px1.p1.17.m1.1b"><apply id="S4.SS1.SSS0.Px1.p1.17.m1.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.17.m1.1.1"><times id="S4.SS1.SSS0.Px1.p1.17.m*******.cmml" xref="S4.SS1.SSS0.Px1.p1.17.m*******"></times><ci id="S4.SS1.SSS0.Px1.p1.17.m*******.cmml" xref="S4.SS1.SSS0.Px1.p1.17.m*******">𝒩</ci><apply id="S4.SS1.SSS0.Px1.p1.17.m*******.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.17.m*******.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.17.m*******.1.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.17.m*******.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.17.m*******.1.1.2.cmml" xref="S4.SS1.SSS0.Px1.p1.17.m*******.1.1.2">𝒙</ci><ci id="S4.SS1.SSS0.Px1.p1.17.m*******.1.1.3.cmml" xref="S4.SS1.SSS0.Px1.p1.17.m*******.1.1.3">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px1.p1.17.m1.1c">\mathcal{N}({\bm{x}}_{t})</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px1.p1.17.m1.1d">caligraphic_N ( bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT )</annotation></semantics></math> in the <em class="ltx_emph ltx_font_italic" id="S4.SS1.SSS0.Px1.p1.18.1">localization module</em>: 1) the k-nearest neighbors of the query point (K-Neighbor); 2) the k-nearest neighbors within a moderate radius (R-Neighbor).
Moreover, there exist several reasonable similarity metrics to implement the attention weight in Eq.(<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#S4.E3" title="Equation 3 ‣ Localization Module. ‣ 4.1 Locality-Aware Attention (LA-Attention) ‣ 4 LARA: The Proposed Framework ‣ Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling"><span class="ltx_text ltx_ref_tag">3</span></a>). We recommend two practical approaches: 1) the identical weight; 2) the reciprocal of Mahalanobis distance <math alttext="d_{{\bm{M}}}({\bm{z}},{\bm{x}}_{t})" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px1.p1.18.m2.2"><semantics id="S4.SS1.SSS0.Px1.p1.18.m2.2a"><mrow id="S4.SS1.SSS0.Px1.p1.18.m2.2.2" xref="S4.SS1.SSS0.Px1.p1.18.m2.2.2.cmml"><msub id="S4.SS1.SSS0.Px1.p1.18.m*******" xref="S4.SS1.SSS0.Px1.p1.18.m*******.cmml"><mi id="S4.SS1.SSS0.Px1.p1.18.m2.*******" xref="S4.SS1.SSS0.Px1.p1.18.m2.*******.cmml">d</mi><mi id="S4.SS1.SSS0.Px1.p1.18.m2.*******" xref="S4.SS1.SSS0.Px1.p1.18.m2.*******.cmml">𝑴</mi></msub><mo id="S4.SS1.SSS0.Px1.p1.18.m*******" xref="S4.SS1.SSS0.Px1.p1.18.m*******.cmml">⁢</mo><mrow id="S4.SS1.SSS0.Px1.p1.18.m2.*******" xref="S4.SS1.SSS0.Px1.p1.18.m*******.2.cmml"><mo id="S4.SS1.SSS0.Px1.p1.18.m2.*******.2" stretchy="false" xref="S4.SS1.SSS0.Px1.p1.18.m*******.2.cmml">(</mo><mi id="S4.SS1.SSS0.Px1.p1.18.m2.1.1" xref="S4.SS1.SSS0.Px1.p1.18.m2.1.1.cmml">𝒛</mi><mo id="S4.SS1.SSS0.Px1.p1.18.m2.*******.3" xref="S4.SS1.SSS0.Px1.p1.18.m*******.2.cmml">,</mo><msub id="S4.SS1.SSS0.Px1.p1.18.m2.*******.1" xref="S4.SS1.SSS0.Px1.p1.18.m2.*******.1.cmml"><mi id="S4.SS1.SSS0.Px1.p1.18.m2.*******.1.2" xref="S4.SS1.SSS0.Px1.p1.18.m2.*******.1.2.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px1.p1.18.m2.*******.1.3" xref="S4.SS1.SSS0.Px1.p1.18.m2.*******.1.3.cmml">t</mi></msub><mo id="S4.SS1.SSS0.Px1.p1.18.m2.*******.4" stretchy="false" xref="S4.SS1.SSS0.Px1.p1.18.m*******.2.cmml">)</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px1.p1.18.m2.2b"><apply id="S4.SS1.SSS0.Px1.p1.18.m2.2.2.cmml" xref="S4.SS1.SSS0.Px1.p1.18.m2.2.2"><times id="S4.SS1.SSS0.Px1.p1.18.m*******.cmml" xref="S4.SS1.SSS0.Px1.p1.18.m*******"></times><apply id="S4.SS1.SSS0.Px1.p1.18.m*******.cmml" xref="S4.SS1.SSS0.Px1.p1.18.m*******"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.18.m2.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.18.m*******">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.18.m2.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.18.m2.*******">𝑑</ci><ci id="S4.SS1.SSS0.Px1.p1.18.m2.*******.cmml" xref="S4.SS1.SSS0.Px1.p1.18.m2.*******">𝑴</ci></apply><interval closure="open" id="S4.SS1.SSS0.Px1.p1.18.m*******.2.cmml" xref="S4.SS1.SSS0.Px1.p1.18.m2.*******"><ci id="S4.SS1.SSS0.Px1.p1.18.m2.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.18.m2.1.1">𝒛</ci><apply id="S4.SS1.SSS0.Px1.p1.18.m2.*******.1.cmml" xref="S4.SS1.SSS0.Px1.p1.18.m2.*******.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px1.p1.18.m2.*******.1.1.cmml" xref="S4.SS1.SSS0.Px1.p1.18.m2.*******.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px1.p1.18.m2.*******.1.2.cmml" xref="S4.SS1.SSS0.Px1.p1.18.m2.*******.1.2">𝒙</ci><ci id="S4.SS1.SSS0.Px1.p1.18.m2.*******.1.3.cmml" xref="S4.SS1.SSS0.Px1.p1.18.m2.*******.1.3">𝑡</ci></apply></interval></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px1.p1.18.m2.2c">d_{{\bm{M}}}({\bm{z}},{\bm{x}}_{t})</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px1.p1.18.m2.2d">italic_d start_POSTSUBSCRIPT bold_italic_M end_POSTSUBSCRIPT ( bold_italic_z , bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT )</annotation></semantics></math> (introduced below), detailed as follows:</p>
<table class="ltx_equation ltx_eqn_table" id="S4.E4">
<tbody><tr class="ltx_equation ltx_eqn_row ltx_align_baseline">
<td class="ltx_eqn_cell ltx_eqn_center_padleft"></td>
<td class="ltx_eqn_cell ltx_align_center"><math alttext="k({\bm{z}},{\bm{x}}_{t}):=\left\{\begin{array}[]{ll}k_{I}({\bm{z}},{\bm{x}}_{t%
})=1\\
k_{R}({\bm{z}},{\bm{x}}_{t})=1/d_{{\bm{M}}}({\bm{z}},{\bm{x}}_{t})\end{array}%
\right.\;." class="ltx_Math" display="block" id="S4.E4.m1.8"><semantics id="S4.E4.m1.8a"><mrow id="S4.E4.m1.8.8.1" xref="S4.E4.m1.*******.cmml"><mrow id="S4.E4.m1.*******" xref="S4.E4.m1.*******.cmml"><mrow id="S4.E4.m1.*******.1" xref="S4.E4.m1.*******.1.cmml"><mi id="S4.E4.m1.8.8.*******" xref="S4.E4.m1.8.8.*******.cmml">k</mi><mo id="S4.E4.m1.8.8.*******" xref="S4.E4.m1.8.8.*******.cmml">⁢</mo><mrow id="S4.E4.m1.8.8.*******.1" xref="S4.E4.m1.8.8.*******.2.cmml"><mo id="S4.E4.m1.8.8.*******.1.2" stretchy="false" xref="S4.E4.m1.8.8.*******.2.cmml">(</mo><mi id="S4.E4.m1.7.7" xref="S4.E4.m1.7.7.cmml">𝒛</mi><mo id="S4.E4.m1.8.8.*******.1.3" xref="S4.E4.m1.8.8.*******.2.cmml">,</mo><msub id="S4.E4.m1.8.8.*******.1.1" xref="S4.E4.m1.8.8.*******.1.1.cmml"><mi id="S4.E4.m1.8.8.*******.1.1.2" xref="S4.E4.m1.8.8.*******.1.1.2.cmml">𝒙</mi><mi id="S4.E4.m1.8.8.*******.1.1.3" xref="S4.E4.m1.8.8.*******.1.1.3.cmml">t</mi></msub><mo id="S4.E4.m1.8.8.*******.1.4" rspace="0.278em" stretchy="false" xref="S4.E4.m1.8.8.*******.2.cmml">)</mo></mrow></mrow><mo id="S4.E4.m1.*******.2" rspace="0.278em" xref="S4.E4.m1.*******.2.cmml">:=</mo><mrow id="S4.E4.m1.8.8.*******" xref="S4.E4.m1.8.8.*******.cmml"><mo id="S4.E4.m1.8.8.*******.1" xref="S4.E4.m1.8.8.*******.1.cmml">{</mo><mtable columnspacing="5pt" displaystyle="true" id="S4.E4.m1.6.6" rowspacing="0pt" xref="S4.E4.m1.6.6.cmml"><mtr id="S4.E4.m1.6.6a" xref="S4.E4.m1.6.6.cmml"><mtd class="ltx_align_left" columnalign="left" id="S4.E4.m1.6.6b" xref="S4.E4.m1.6.6.cmml"><mrow id="S4.E4.m1.*******.2" xref="S4.E4.m1.*******.2.cmml"><mrow id="S4.E4.m1.*******.2.2" xref="S4.E4.m1.*******.2.2.cmml"><msub id="S4.E4.m1.*******.2.2.3" xref="S4.E4.m1.*******.2.2.3.cmml"><mi id="S4.E4.m1.*******.*******" xref="S4.E4.m1.*******.*******.cmml">k</mi><mi id="S4.E4.m1.*******.*******" xref="S4.E4.m1.*******.*******.cmml">I</mi></msub><mo id="S4.E4.m1.*******.2.2.2" xref="S4.E4.m1.*******.2.2.2.cmml">⁢</mo><mrow id="S4.E4.m1.*******.*******" xref="S4.E4.m*******.*******.2.cmml"><mo id="S4.E4.m1.*******.*******.2" stretchy="false" xref="S4.E4.m*******.*******.2.cmml">(</mo><mi id="S4.E4.m*******.1.1.1" xref="S4.E4.m*******.1.1.1.cmml">𝒛</mi><mo id="S4.E4.m1.*******.*******.3" xref="S4.E4.m*******.*******.2.cmml">,</mo><msub id="S4.E4.m1.*******.*******.1" xref="S4.E4.m1.*******.*******.1.cmml"><mi id="S4.E4.m1.*******.*******.1.2" xref="S4.E4.m1.*******.*******.1.2.cmml">𝒙</mi><mi id="S4.E4.m1.*******.*******.1.3" xref="S4.E4.m1.*******.*******.1.3.cmml">t</mi></msub><mo id="S4.E4.m1.*******.*******.4" stretchy="false" xref="S4.E4.m*******.*******.2.cmml">)</mo></mrow></mrow><mo id="S4.E4.m1.*******.2.3" xref="S4.E4.m1.*******.2.3.cmml">=</mo><mn id="S4.E4.m1.*******.2.4" xref="S4.E4.m1.*******.2.4.cmml">1</mn></mrow></mtd><mtd id="S4.E4.m1.6.6c" xref="S4.E4.m1.6.6.cmml"></mtd></mtr><mtr id="S4.E4.m1.6.6d" xref="S4.E4.m1.6.6.cmml"><mtd class="ltx_align_left" columnalign="left" id="S4.E4.m1.6.6e" xref="S4.E4.m1.6.6.cmml"><mrow id="S4.E4.m1.*******.4" xref="S4.E4.m1.*******.4.cmml"><mrow id="S4.E4.m1.*******.3.3" xref="S4.E4.m1.*******.3.3.cmml"><msub id="S4.E4.m1.*******.3.3.3" xref="S4.E4.m1.*******.3.3.3.cmml"><mi id="S4.E4.m1.*******.*******" xref="S4.E4.m1.*******.*******.cmml">k</mi><mi id="S4.E4.m1.*******.*******" xref="S4.E4.m1.*******.*******.cmml">R</mi></msub><mo id="S4.E4.m1.*******.3.3.2" xref="S4.E4.m1.*******.3.3.2.cmml">⁢</mo><mrow id="S4.E4.m1.*******.*******" xref="S4.E4.m1.*******.*******.cmml"><mo id="S4.E4.m1.*******.*******.2" stretchy="false" xref="S4.E4.m1.*******.*******.cmml">(</mo><mi id="S4.E4.m1.*******.1.1" xref="S4.E4.m1.*******.1.1.cmml">𝒛</mi><mo id="S4.E4.m1.*******.*******.3" xref="S4.E4.m1.*******.*******.cmml">,</mo><msub id="S4.E4.m1.*******.*******.1" xref="S4.E4.m1.*******.*******.1.cmml"><mi id="S4.E4.m1.*******.3.3.*******" xref="S4.E4.m1.*******.3.3.*******.cmml">𝒙</mi><mi id="S4.E4.m1.*******.3.3.*******" xref="S4.E4.m1.*******.3.3.*******.cmml">t</mi></msub><mo id="S4.E4.m1.*******.*******.4" stretchy="false" xref="S4.E4.m1.*******.*******.cmml">)</mo></mrow></mrow><mo id="S4.E4.m1.*******.4.5" xref="S4.E4.m1.*******.4.5.cmml">=</mo><mrow id="S4.E4.m1.*******.4.4" xref="S4.E4.m1.*******.4.4.cmml"><mrow id="S4.E4.m1.*******.4.4.3" xref="S4.E4.m1.*******.4.4.3.cmml"><mn id="S4.E4.m1.*******.4.4.3.2" xref="S4.E4.m1.*******.4.4.3.2.cmml">1</mn><mo id="S4.E4.m1.*******.4.4.3.1" xref="S4.E4.m1.*******.4.4.3.1.cmml">/</mo><msub id="S4.E4.m1.*******.4.4.3.3" xref="S4.E4.m1.*******.4.4.3.3.cmml"><mi id="S4.E4.m1.*******.4.4.3.3.2" xref="S4.E4.m1.*******.4.4.3.3.2.cmml">d</mi><mi id="S4.E4.m1.*******.4.4.3.3.3" xref="S4.E4.m1.*******.4.4.3.3.3.cmml">𝑴</mi></msub></mrow><mo id="S4.E4.m1.*******.4.4.2" xref="S4.E4.m1.*******.4.4.2.cmml">⁢</mo><mrow id="S4.E4.m1.*******.4.4.1.1" xref="S4.E4.m1.*******.4.4.1.2.cmml"><mo id="S4.E4.m1.*******.4.4.1.1.2" stretchy="false" xref="S4.E4.m1.*******.4.4.1.2.cmml">(</mo><mi id="S4.E4.m1.4.*******.2" xref="S4.E4.m1.4.*******.2.cmml">𝒛</mi><mo id="S4.E4.m1.*******.4.4.1.1.3" xref="S4.E4.m1.*******.4.4.1.2.cmml">,</mo><msub id="S4.E4.m1.*******.4.4.1.1.1" xref="S4.E4.m1.*******.4.4.1.1.1.cmml"><mi id="S4.E4.m1.*******.4.4.*******" xref="S4.E4.m1.*******.4.4.*******.cmml">𝒙</mi><mi id="S4.E4.m1.*******.4.4.*******" xref="S4.E4.m1.*******.4.4.*******.cmml">t</mi></msub><mo id="S4.E4.m1.*******.4.4.1.1.4" stretchy="false" xref="S4.E4.m1.*******.4.4.1.2.cmml">)</mo></mrow></mrow></mrow></mtd><mtd id="S4.E4.m1.6.6f" xref="S4.E4.m1.6.6.cmml"></mtd></mtr></mtable><mi id="S4.E4.m1.8.8.*******.2" xref="S4.E4.m1.8.8.*******.1.cmml"></mi></mrow></mrow><mo id="S4.E4.m1.8.8.1.2" lspace="0em" xref="S4.E4.m1.*******.cmml">.</mo></mrow><annotation-xml encoding="MathML-Content" id="S4.E4.m1.8b"><apply id="S4.E4.m1.*******.cmml" xref="S4.E4.m1.8.8.1"><csymbol cd="latexml" id="S4.E4.m1.*******.2.cmml" xref="S4.E4.m1.*******.2">assign</csymbol><apply id="S4.E4.m1.*******.1.cmml" xref="S4.E4.m1.*******.1"><times id="S4.E4.m1.8.8.*******.cmml" xref="S4.E4.m1.8.8.*******"></times><ci id="S4.E4.m1.8.8.*******.cmml" xref="S4.E4.m1.8.8.*******">𝑘</ci><interval closure="open" id="S4.E4.m1.8.8.*******.2.cmml" xref="S4.E4.m1.8.8.*******.1"><ci id="S4.E4.m1.7.7.cmml" xref="S4.E4.m1.7.7">𝒛</ci><apply id="S4.E4.m1.8.8.*******.1.1.cmml" xref="S4.E4.m1.8.8.*******.1.1"><csymbol cd="ambiguous" id="S4.E4.m1.8.8.*******.1.1.1.cmml" xref="S4.E4.m1.8.8.*******.1.1">subscript</csymbol><ci id="S4.E4.m1.8.8.*******.1.1.2.cmml" xref="S4.E4.m1.8.8.*******.1.1.2">𝒙</ci><ci id="S4.E4.m1.8.8.*******.1.1.3.cmml" xref="S4.E4.m1.8.8.*******.1.1.3">𝑡</ci></apply></interval></apply><apply id="S4.E4.m1.8.8.*******.cmml" xref="S4.E4.m1.8.8.*******"><csymbol cd="latexml" id="S4.E4.m1.8.8.*******.1.cmml" xref="S4.E4.m1.8.8.*******.1">cases</csymbol><matrix id="S4.E4.m1.6.6.cmml" xref="S4.E4.m1.6.6"><matrixrow id="S4.E4.m1.6.6a.cmml" xref="S4.E4.m1.6.6"><apply id="S4.E4.m1.*******.2.cmml" xref="S4.E4.m1.*******.2"><eq id="S4.E4.m1.*******.2.3.cmml" xref="S4.E4.m1.*******.2.3"></eq><apply id="S4.E4.m1.*******.2.2.cmml" xref="S4.E4.m1.*******.2.2"><times id="S4.E4.m1.*******.2.2.2.cmml" xref="S4.E4.m1.*******.2.2.2"></times><apply id="S4.E4.m1.*******.2.2.3.cmml" xref="S4.E4.m1.*******.2.2.3"><csymbol cd="ambiguous" id="S4.E4.m1.*******.*******.cmml" xref="S4.E4.m1.*******.2.2.3">subscript</csymbol><ci id="S4.E4.m1.*******.*******.cmml" xref="S4.E4.m1.*******.*******">𝑘</ci><ci id="S4.E4.m1.*******.*******.cmml" xref="S4.E4.m1.*******.*******">𝐼</ci></apply><interval closure="open" id="S4.E4.m*******.*******.2.cmml" xref="S4.E4.m1.*******.*******"><ci id="S4.E4.m*******.1.1.1.cmml" xref="S4.E4.m*******.1.1.1">𝒛</ci><apply id="S4.E4.m1.*******.*******.1.cmml" xref="S4.E4.m1.*******.*******.1"><csymbol cd="ambiguous" id="S4.E4.m1.*******.*******.1.1.cmml" xref="S4.E4.m1.*******.*******.1">subscript</csymbol><ci id="S4.E4.m1.*******.*******.1.2.cmml" xref="S4.E4.m1.*******.*******.1.2">𝒙</ci><ci id="S4.E4.m1.*******.*******.1.3.cmml" xref="S4.E4.m1.*******.*******.1.3">𝑡</ci></apply></interval></apply><cn id="S4.E4.m1.*******.2.4.cmml" type="integer" xref="S4.E4.m1.*******.2.4">1</cn></apply><cerror id="S4.E4.m1.6.6b.cmml" xref="S4.E4.m1.6.6"><csymbol cd="ambiguous" id="S4.E4.m1.6.6c.cmml" xref="S4.E4.m1.6.6">missing-subexpression</csymbol></cerror></matrixrow><matrixrow id="S4.E4.m1.6.6d.cmml" xref="S4.E4.m1.6.6"><apply id="S4.E4.m1.*******.4.cmml" xref="S4.E4.m1.*******.4"><eq id="S4.E4.m1.*******.4.5.cmml" xref="S4.E4.m1.*******.4.5"></eq><apply id="S4.E4.m1.*******.3.3.cmml" xref="S4.E4.m1.*******.3.3"><times id="S4.E4.m1.*******.3.3.2.cmml" xref="S4.E4.m1.*******.3.3.2"></times><apply id="S4.E4.m1.*******.3.3.3.cmml" xref="S4.E4.m1.*******.3.3.3"><csymbol cd="ambiguous" id="S4.E4.m1.*******.*******.cmml" xref="S4.E4.m1.*******.3.3.3">subscript</csymbol><ci id="S4.E4.m1.*******.*******.cmml" xref="S4.E4.m1.*******.*******">𝑘</ci><ci id="S4.E4.m1.*******.*******.cmml" xref="S4.E4.m1.*******.*******">𝑅</ci></apply><interval closure="open" id="S4.E4.m1.*******.*******.cmml" xref="S4.E4.m1.*******.*******"><ci id="S4.E4.m1.*******.1.1.cmml" xref="S4.E4.m1.*******.1.1">𝒛</ci><apply id="S4.E4.m1.*******.*******.1.cmml" xref="S4.E4.m1.*******.*******.1"><csymbol cd="ambiguous" id="S4.E4.m1.*******.3.3.*******.cmml" xref="S4.E4.m1.*******.*******.1">subscript</csymbol><ci id="S4.E4.m1.*******.3.3.*******.cmml" xref="S4.E4.m1.*******.3.3.*******">𝒙</ci><ci id="S4.E4.m1.*******.3.3.*******.cmml" xref="S4.E4.m1.*******.3.3.*******">𝑡</ci></apply></interval></apply><apply id="S4.E4.m1.*******.4.4.cmml" xref="S4.E4.m1.*******.4.4"><times id="S4.E4.m1.*******.4.4.2.cmml" xref="S4.E4.m1.*******.4.4.2"></times><apply id="S4.E4.m1.*******.4.4.3.cmml" xref="S4.E4.m1.*******.4.4.3"><divide id="S4.E4.m1.*******.4.4.3.1.cmml" xref="S4.E4.m1.*******.4.4.3.1"></divide><cn id="S4.E4.m1.*******.4.4.3.2.cmml" type="integer" xref="S4.E4.m1.*******.4.4.3.2">1</cn><apply id="S4.E4.m1.*******.4.4.3.3.cmml" xref="S4.E4.m1.*******.4.4.3.3"><csymbol cd="ambiguous" id="S4.E4.m1.*******.4.4.3.3.1.cmml" xref="S4.E4.m1.*******.4.4.3.3">subscript</csymbol><ci id="S4.E4.m1.*******.4.4.3.3.2.cmml" xref="S4.E4.m1.*******.4.4.3.3.2">𝑑</ci><ci id="S4.E4.m1.*******.4.4.3.3.3.cmml" xref="S4.E4.m1.*******.4.4.3.3.3">𝑴</ci></apply></apply><interval closure="open" id="S4.E4.m1.*******.4.4.1.2.cmml" xref="S4.E4.m1.*******.4.4.1.1"><ci id="S4.E4.m1.4.*******.2.cmml" xref="S4.E4.m1.4.*******.2">𝒛</ci><apply id="S4.E4.m1.*******.4.4.1.1.1.cmml" xref="S4.E4.m1.*******.4.4.1.1.1"><csymbol cd="ambiguous" id="S4.E4.m1.*******.4.4.*******.cmml" xref="S4.E4.m1.*******.4.4.1.1.1">subscript</csymbol><ci id="S4.E4.m1.*******.4.4.*******.cmml" xref="S4.E4.m1.*******.4.4.*******">𝒙</ci><ci id="S4.E4.m1.*******.4.4.*******.cmml" xref="S4.E4.m1.*******.4.4.*******">𝑡</ci></apply></interval></apply></apply><cerror id="S4.E4.m1.6.6e.cmml" xref="S4.E4.m1.6.6"><csymbol cd="ambiguous" id="S4.E4.m1.6.6f.cmml" xref="S4.E4.m1.6.6">missing-subexpression</csymbol></cerror></matrixrow></matrix></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.E4.m1.8c">k({\bm{z}},{\bm{x}}_{t}):=\left\{\begin{array}[]{ll}k_{I}({\bm{z}},{\bm{x}}_{t%
})=1\\
k_{R}({\bm{z}},{\bm{x}}_{t})=1/d_{{\bm{M}}}({\bm{z}},{\bm{x}}_{t})\end{array}%
\right.\;.</annotation><annotation encoding="application/x-llamapun" id="S4.E4.m1.8d">italic_k ( bold_italic_z , bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT ) := { start_ARRAY start_ROW start_CELL italic_k start_POSTSUBSCRIPT italic_I end_POSTSUBSCRIPT ( bold_italic_z , bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT ) = 1 end_CELL start_CELL end_CELL end_ROW start_ROW start_CELL italic_k start_POSTSUBSCRIPT italic_R end_POSTSUBSCRIPT ( bold_italic_z , bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT ) = 1 / italic_d start_POSTSUBSCRIPT bold_italic_M end_POSTSUBSCRIPT ( bold_italic_z , bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT ) end_CELL start_CELL end_CELL end_ROW end_ARRAY .</annotation></semantics></math></td>
<td class="ltx_eqn_cell ltx_eqn_center_padright"></td>
<td class="ltx_eqn_cell ltx_eqn_eqno ltx_align_middle ltx_align_right" rowspan="1"><span class="ltx_tag ltx_tag_equation ltx_align_right">(4)</span></td>
</tr></tbody>
</table>
</div>
</section>
<section class="ltx_paragraph" id="S4.SS1.SSS0.Px2">
<h4 class="ltx_title ltx_title_paragraph">Metric Learning Module.</h4>
<div class="ltx_para" id="S4.SS1.SSS0.Px2.p1">
<p class="ltx_p" id="S4.SS1.SSS0.Px2.p1.7">As introduced above, we need a reasonable measurement of the distance between data representations on the embedding space to select <em class="ltx_emph ltx_font_italic" id="S4.SS1.SSS0.Px2.p1.1.1">samples with high <math alttext="p_{x_{t}}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px2.p1.1.1.m1.1"><semantics id="S4.SS1.SSS0.Px2.p1.1.1.m1.1a"><msub id="S4.SS1.SSS0.Px2.p1.1.1.m1.1.1" xref="S4.SS1.SSS0.Px2.p1.1.1.m1.1.1.cmml"><mi id="S4.SS1.SSS0.Px2.p1.1.1.m*******" xref="S4.SS1.SSS0.Px2.p1.1.1.m*******.cmml">p</mi><msub id="S4.SS1.SSS0.Px2.p1.1.1.m*******" xref="S4.SS1.SSS0.Px2.p1.1.1.m*******.cmml"><mi id="S4.SS1.SSS0.Px2.p1.1.1.m1.*******" xref="S4.SS1.SSS0.Px2.p1.1.1.m1.*******.cmml">x</mi><mi id="S4.SS1.SSS0.Px2.p1.1.1.m1.*******" xref="S4.SS1.SSS0.Px2.p1.1.1.m1.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px2.p1.1.1.m1.1b"><apply id="S4.SS1.SSS0.Px2.p1.1.1.m1.1.1.cmml" xref="S4.SS1.SSS0.Px2.p1.1.1.m1.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px2.p1.1.1.m*******.cmml" xref="S4.SS1.SSS0.Px2.p1.1.1.m1.1.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px2.p1.1.1.m*******.cmml" xref="S4.SS1.SSS0.Px2.p1.1.1.m*******">𝑝</ci><apply id="S4.SS1.SSS0.Px2.p1.1.1.m*******.cmml" xref="S4.SS1.SSS0.Px2.p1.1.1.m*******"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px2.p1.1.1.m1.*******.cmml" xref="S4.SS1.SSS0.Px2.p1.1.1.m*******">subscript</csymbol><ci id="S4.SS1.SSS0.Px2.p1.1.1.m1.*******.cmml" xref="S4.SS1.SSS0.Px2.p1.1.1.m1.*******">𝑥</ci><ci id="S4.SS1.SSS0.Px2.p1.1.1.m1.*******.cmml" xref="S4.SS1.SSS0.Px2.p1.1.1.m1.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px2.p1.1.1.m1.1c">p_{x_{t}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px2.p1.1.1.m1.1d">italic_p start_POSTSUBSCRIPT italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math></em>.
However, in the context of quantitative investment, different investors have different understandings of the financial market and prefer completely different types of factors, <em class="ltx_emph ltx_font_italic" id="S4.SS1.SSS0.Px2.p1.7.2">e.g.,</em> value investors prefer fundamental factors, while technical analysts may choose technical factors.
Due to the multi-modal nature of financial data, we adopt the metric learning technique to mitigate the pressure of manually searching for the suitable distance measurement and enhance the flexibility of LARA.
The goal of metric learning is to learn a Mahalanobis distance metric <math alttext="{\bm{M}}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px2.p1.2.m1.1"><semantics id="S4.SS1.SSS0.Px2.p1.2.m1.1a"><mi id="S4.SS1.SSS0.Px2.p1.2.m1.1.1" xref="S4.SS1.SSS0.Px2.p1.2.m1.1.1.cmml">𝑴</mi><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px2.p1.2.m1.1b"><ci id="S4.SS1.SSS0.Px2.p1.2.m1.1.1.cmml" xref="S4.SS1.SSS0.Px2.p1.2.m1.1.1">𝑴</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px2.p1.2.m1.1c">{\bm{M}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px2.p1.2.m1.1d">bold_italic_M</annotation></semantics></math> to pull the similar samples close and push the dissimilar samples away. A straightforward solution is to minimize the dissimilarity of samples within the same class. We first define the indicator function as <math alttext="K_{i,j}=1" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px2.p1.3.m2.2"><semantics id="S4.SS1.SSS0.Px2.p1.3.m2.2a"><mrow id="S4.SS1.SSS0.Px2.p1.3.m2.2.3" xref="S4.SS1.SSS0.Px2.p1.3.m2.2.3.cmml"><msub id="S4.SS1.SSS0.Px2.p1.3.m*******" xref="S4.SS1.SSS0.Px2.p1.3.m*******.cmml"><mi id="S4.SS1.SSS0.Px2.p1.3.m*******.2" xref="S4.SS1.SSS0.Px2.p1.3.m*******.2.cmml">K</mi><mrow id="S4.SS1.SSS0.Px2.p1.3.m*******.4" xref="S4.SS1.SSS0.Px2.p1.3.m*******.3.cmml"><mi id="S4.SS1.SSS0.Px2.p1.3.m2.*******" xref="S4.SS1.SSS0.Px2.p1.3.m2.*******.cmml">i</mi><mo id="S4.SS1.SSS0.Px2.p1.3.m*******.4.1" xref="S4.SS1.SSS0.Px2.p1.3.m*******.3.cmml">,</mo><mi id="S4.SS1.SSS0.Px2.p1.3.m*******.2" xref="S4.SS1.SSS0.Px2.p1.3.m*******.2.cmml">j</mi></mrow></msub><mo id="S4.SS1.SSS0.Px2.p1.3.m*******" xref="S4.SS1.SSS0.Px2.p1.3.m*******.cmml">=</mo><mn id="S4.SS1.SSS0.Px2.p1.3.m*******" xref="S4.SS1.SSS0.Px2.p1.3.m*******.cmml">1</mn></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px2.p1.3.m2.2b"><apply id="S4.SS1.SSS0.Px2.p1.3.m2.2.3.cmml" xref="S4.SS1.SSS0.Px2.p1.3.m2.2.3"><eq id="S4.SS1.SSS0.Px2.p1.3.m*******.cmml" xref="S4.SS1.SSS0.Px2.p1.3.m*******"></eq><apply id="S4.SS1.SSS0.Px2.p1.3.m*******.cmml" xref="S4.SS1.SSS0.Px2.p1.3.m*******"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px2.p1.3.m*******.1.cmml" xref="S4.SS1.SSS0.Px2.p1.3.m*******">subscript</csymbol><ci id="S4.SS1.SSS0.Px2.p1.3.m*******.2.cmml" xref="S4.SS1.SSS0.Px2.p1.3.m*******.2">𝐾</ci><list id="S4.SS1.SSS0.Px2.p1.3.m*******.3.cmml" xref="S4.SS1.SSS0.Px2.p1.3.m*******.4"><ci id="S4.SS1.SSS0.Px2.p1.3.m2.*******.cmml" xref="S4.SS1.SSS0.Px2.p1.3.m2.*******">𝑖</ci><ci id="S4.SS1.SSS0.Px2.p1.3.m*******.2.cmml" xref="S4.SS1.SSS0.Px2.p1.3.m*******.2">𝑗</ci></list></apply><cn id="S4.SS1.SSS0.Px2.p1.3.m*******.cmml" type="integer" xref="S4.SS1.SSS0.Px2.p1.3.m*******">1</cn></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px2.p1.3.m2.2c">K_{i,j}=1</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px2.p1.3.m2.2d">italic_K start_POSTSUBSCRIPT italic_i , italic_j end_POSTSUBSCRIPT = 1</annotation></semantics></math> if <math alttext="y_{i}=y_{j}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px2.p1.4.m3.1"><semantics id="S4.SS1.SSS0.Px2.p1.4.m3.1a"><mrow id="S4.SS1.SSS0.Px2.p1.4.m3.1.1" xref="S4.SS1.SSS0.Px2.p1.4.m3.1.1.cmml"><msub id="S4.SS1.SSS0.Px2.p1.4.m3.1.1.2" xref="S4.SS1.SSS0.Px2.p1.4.m3.1.1.2.cmml"><mi id="S4.SS1.SSS0.Px2.p1.4.m3.*******" xref="S4.SS1.SSS0.Px2.p1.4.m3.*******.cmml">y</mi><mi id="S4.SS1.SSS0.Px2.p1.4.m3.*******" xref="S4.SS1.SSS0.Px2.p1.4.m3.*******.cmml">i</mi></msub><mo id="S4.SS1.SSS0.Px2.p1.4.m3.1.1.1" xref="S4.SS1.SSS0.Px2.p1.4.m3.1.1.1.cmml">=</mo><msub id="S4.SS1.SSS0.Px2.p1.4.m3.1.1.3" xref="S4.SS1.SSS0.Px2.p1.4.m3.1.1.3.cmml"><mi id="S4.SS1.SSS0.Px2.p1.4.m3.*******" xref="S4.SS1.SSS0.Px2.p1.4.m3.*******.cmml">y</mi><mi id="S4.SS1.SSS0.Px2.p1.4.m3.*******" xref="S4.SS1.SSS0.Px2.p1.4.m3.*******.cmml">j</mi></msub></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px2.p1.4.m3.1b"><apply id="S4.SS1.SSS0.Px2.p1.4.m3.1.1.cmml" xref="S4.SS1.SSS0.Px2.p1.4.m3.1.1"><eq id="S4.SS1.SSS0.Px2.p1.4.m3.1.1.1.cmml" xref="S4.SS1.SSS0.Px2.p1.4.m3.1.1.1"></eq><apply id="S4.SS1.SSS0.Px2.p1.4.m3.1.1.2.cmml" xref="S4.SS1.SSS0.Px2.p1.4.m3.1.1.2"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px2.p1.4.m3.*******.cmml" xref="S4.SS1.SSS0.Px2.p1.4.m3.1.1.2">subscript</csymbol><ci id="S4.SS1.SSS0.Px2.p1.4.m3.*******.cmml" xref="S4.SS1.SSS0.Px2.p1.4.m3.*******">𝑦</ci><ci id="S4.SS1.SSS0.Px2.p1.4.m3.*******.cmml" xref="S4.SS1.SSS0.Px2.p1.4.m3.*******">𝑖</ci></apply><apply id="S4.SS1.SSS0.Px2.p1.4.m3.1.1.3.cmml" xref="S4.SS1.SSS0.Px2.p1.4.m3.1.1.3"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px2.p1.4.m3.*******.cmml" xref="S4.SS1.SSS0.Px2.p1.4.m3.1.1.3">subscript</csymbol><ci id="S4.SS1.SSS0.Px2.p1.4.m3.*******.cmml" xref="S4.SS1.SSS0.Px2.p1.4.m3.*******">𝑦</ci><ci id="S4.SS1.SSS0.Px2.p1.4.m3.*******.cmml" xref="S4.SS1.SSS0.Px2.p1.4.m3.*******">𝑗</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px2.p1.4.m3.1c">y_{i}=y_{j}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px2.p1.4.m3.1d">italic_y start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT = italic_y start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT</annotation></semantics></math> and <math alttext="0" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px2.p1.5.m4.1"><semantics id="S4.SS1.SSS0.Px2.p1.5.m4.1a"><mn id="S4.SS1.SSS0.Px2.p1.5.m4.1.1" xref="S4.SS1.SSS0.Px2.p1.5.m4.1.1.cmml">0</mn><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px2.p1.5.m4.1b"><cn id="S4.SS1.SSS0.Px2.p1.5.m4.1.1.cmml" type="integer" xref="S4.SS1.SSS0.Px2.p1.5.m4.1.1">0</cn></annotation-xml></semantics></math> otherwise.
Given two embedding vectors <math alttext="{\bm{x}}_{i},{\bm{x}}_{j}\in\mathbb{R}^{d}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px2.p1.6.m5.2"><semantics id="S4.SS1.SSS0.Px2.p1.6.m5.2a"><mrow id="S4.SS1.SSS0.Px2.p1.6.m5.2.2" xref="S4.SS1.SSS0.Px2.p1.6.m5.2.2.cmml"><mrow id="S4.SS1.SSS0.Px2.p1.6.m5.*******" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******.cmml"><msub id="S4.SS1.SSS0.Px2.p1.6.m5.*******.1" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******.1.cmml"><mi id="S4.SS1.SSS0.Px2.p1.6.m5.*******.1.2" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******.1.2.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px2.p1.6.m5.*******.1.3" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******.1.3.cmml">i</mi></msub><mo id="S4.SS1.SSS0.Px2.p1.6.m5.*******.3" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******.cmml">,</mo><msub id="S4.SS1.SSS0.Px2.p1.6.m5.*******.2" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******.2.cmml"><mi id="S4.SS1.SSS0.Px2.p1.6.m5.*******.2.2" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******.2.2.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px2.p1.6.m5.*******.2.3" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******.2.3.cmml">j</mi></msub></mrow><mo id="S4.SS1.SSS0.Px2.p1.6.m5.2.2.3" xref="S4.SS1.SSS0.Px2.p1.6.m5.2.2.3.cmml">∈</mo><msup id="S4.SS1.SSS0.Px2.p1.6.m5.2.2.4" xref="S4.SS1.SSS0.Px2.p1.6.m5.2.2.4.cmml"><mi id="S4.SS1.SSS0.Px2.p1.6.m5.*******" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******.cmml">ℝ</mi><mi id="S4.SS1.SSS0.Px2.p1.6.m5.*******" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******.cmml">d</mi></msup></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px2.p1.6.m5.2b"><apply id="S4.SS1.SSS0.Px2.p1.6.m5.2.2.cmml" xref="S4.SS1.SSS0.Px2.p1.6.m5.2.2"><in id="S4.SS1.SSS0.Px2.p1.6.m5.2.2.3.cmml" xref="S4.SS1.SSS0.Px2.p1.6.m5.2.2.3"></in><list id="S4.SS1.SSS0.Px2.p1.6.m5.*******.cmml" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******"><apply id="S4.SS1.SSS0.Px2.p1.6.m5.*******.1.cmml" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px2.p1.6.m5.*******.1.1.cmml" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px2.p1.6.m5.*******.1.2.cmml" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******.1.2">𝒙</ci><ci id="S4.SS1.SSS0.Px2.p1.6.m5.*******.1.3.cmml" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******.1.3">𝑖</ci></apply><apply id="S4.SS1.SSS0.Px2.p1.6.m5.*******.2.cmml" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******.2"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px2.p1.6.m5.2.2.*******.cmml" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******.2">subscript</csymbol><ci id="S4.SS1.SSS0.Px2.p1.6.m5.*******.2.2.cmml" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******.2.2">𝒙</ci><ci id="S4.SS1.SSS0.Px2.p1.6.m5.*******.2.3.cmml" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******.2.3">𝑗</ci></apply></list><apply id="S4.SS1.SSS0.Px2.p1.6.m5.2.2.4.cmml" xref="S4.SS1.SSS0.Px2.p1.6.m5.2.2.4"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px2.p1.6.m5.*******.cmml" xref="S4.SS1.SSS0.Px2.p1.6.m5.2.2.4">superscript</csymbol><ci id="S4.SS1.SSS0.Px2.p1.6.m5.*******.cmml" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******">ℝ</ci><ci id="S4.SS1.SSS0.Px2.p1.6.m5.*******.cmml" xref="S4.SS1.SSS0.Px2.p1.6.m5.*******">𝑑</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px2.p1.6.m5.2c">{\bm{x}}_{i},{\bm{x}}_{j}\in\mathbb{R}^{d}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px2.p1.6.m5.2d">bold_italic_x start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT , bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT ∈ blackboard_R start_POSTSUPERSCRIPT italic_d end_POSTSUPERSCRIPT</annotation></semantics></math>, we optimize the (squared) Mahalanobis matrix <math alttext="{\bm{M}}\in\mathbb{R}^{d\times d}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px2.p1.7.m6.1"><semantics id="S4.SS1.SSS0.Px2.p1.7.m6.1a"><mrow id="S4.SS1.SSS0.Px2.p1.7.m6.1.1" xref="S4.SS1.SSS0.Px2.p1.7.m6.1.1.cmml"><mi id="S4.SS1.SSS0.Px2.p1.7.m6.1.1.2" xref="S4.SS1.SSS0.Px2.p1.7.m6.1.1.2.cmml">𝑴</mi><mo id="S4.SS1.SSS0.Px2.p1.7.m6.1.1.1" xref="S4.SS1.SSS0.Px2.p1.7.m6.1.1.1.cmml">∈</mo><msup id="S4.SS1.SSS0.Px2.p1.7.m6.1.1.3" xref="S4.SS1.SSS0.Px2.p1.7.m6.1.1.3.cmml"><mi id="S4.SS1.SSS0.Px2.p1.7.m6.*******" xref="S4.SS1.SSS0.Px2.p1.7.m6.*******.cmml">ℝ</mi><mrow id="S4.SS1.SSS0.Px2.p1.7.m6.*******" xref="S4.SS1.SSS0.Px2.p1.7.m6.*******.cmml"><mi id="S4.SS1.SSS0.Px2.p1.7.m6.*******.2" xref="S4.SS1.SSS0.Px2.p1.7.m6.*******.2.cmml">d</mi><mo id="S4.SS1.SSS0.Px2.p1.7.m6.*******.1" lspace="0.222em" rspace="0.222em" xref="S4.SS1.SSS0.Px2.p1.7.m6.*******.1.cmml">×</mo><mi id="S4.SS1.SSS0.Px2.p1.7.m6.*******.3" xref="S4.SS1.SSS0.Px2.p1.7.m6.*******.3.cmml">d</mi></mrow></msup></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px2.p1.7.m6.1b"><apply id="S4.SS1.SSS0.Px2.p1.7.m6.1.1.cmml" xref="S4.SS1.SSS0.Px2.p1.7.m6.1.1"><in id="S4.SS1.SSS0.Px2.p1.7.m6.1.1.1.cmml" xref="S4.SS1.SSS0.Px2.p1.7.m6.1.1.1"></in><ci id="S4.SS1.SSS0.Px2.p1.7.m6.1.1.2.cmml" xref="S4.SS1.SSS0.Px2.p1.7.m6.1.1.2">𝑴</ci><apply id="S4.SS1.SSS0.Px2.p1.7.m6.1.1.3.cmml" xref="S4.SS1.SSS0.Px2.p1.7.m6.1.1.3"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px2.p1.7.m6.*******.cmml" xref="S4.SS1.SSS0.Px2.p1.7.m6.1.1.3">superscript</csymbol><ci id="S4.SS1.SSS0.Px2.p1.7.m6.*******.cmml" xref="S4.SS1.SSS0.Px2.p1.7.m6.*******">ℝ</ci><apply id="S4.SS1.SSS0.Px2.p1.7.m6.*******.cmml" xref="S4.SS1.SSS0.Px2.p1.7.m6.*******"><times id="S4.SS1.SSS0.Px2.p1.7.m6.*******.1.cmml" xref="S4.SS1.SSS0.Px2.p1.7.m6.*******.1"></times><ci id="S4.SS1.SSS0.Px2.p1.7.m6.*******.2.cmml" xref="S4.SS1.SSS0.Px2.p1.7.m6.*******.2">𝑑</ci><ci id="S4.SS1.SSS0.Px2.p1.7.m6.*******.3.cmml" xref="S4.SS1.SSS0.Px2.p1.7.m6.*******.3">𝑑</ci></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px2.p1.7.m6.1c">{\bm{M}}\in\mathbb{R}^{d\times d}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px2.p1.7.m6.1d">bold_italic_M ∈ blackboard_R start_POSTSUPERSCRIPT italic_d × italic_d end_POSTSUPERSCRIPT</annotation></semantics></math> <cite class="ltx_cite ltx_citemacro_cite">Kaya and Bilge (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib14" title="">2019</a>)</cite> as follows:</p>
<table class="ltx_equationgroup ltx_eqn_table" id="S4.E5">
<tbody>
<tr class="ltx_equation ltx_eqn_row ltx_align_baseline" id="S4.E5X">
<td class="ltx_eqn_cell ltx_eqn_center_padleft"></td>
<td class="ltx_td ltx_eqn_cell"></td>
<td class="ltx_td ltx_align_left ltx_eqn_cell"><math alttext="\displaystyle\min_{{\bm{M}}}\quad\frac{1}{2}\sum\limits_{{\bm{x}}_{i},{\bm{x}}%
_{j}\in\mathcal{X}}d_{{\bm{M}}}({\bm{x}}_{i},{\bm{x}}_{j})K_{i,j}" class="ltx_Math" display="inline" id="S4.E5X.2.1.1.m1.6"><semantics id="S4.E5X.2.1.1.m1.6a"><mrow id="S4.E5X.2.1.1.m1.6.6.2" xref="S4.E5X.2.1.1.m1.6.6.3.cmml"><munder id="S4.E5X.2.1.1.m1.*******" xref="S4.E5X.2.1.1.m1.*******.cmml"><mi id="S4.E5X.2.1.1.m1.*******.2" xref="S4.E5X.2.1.1.m1.*******.2.cmml">min</mi><mi id="S4.E5X.2.1.1.m1.*******.3" xref="S4.E5X.2.1.1.m1.*******.3.cmml">𝑴</mi></munder><mspace id="S4.E5X.2.1.1.m1.*******" width="1.167em" xref="S4.E5X.2.1.1.m1.6.6.3.cmml"></mspace><mrow id="S4.E5X.2.1.1.m1.*******" xref="S4.E5X.2.1.1.m1.*******.cmml"><mstyle displaystyle="true" id="S4.E5X.2.1.1.m1.*******.4" xref="S4.E5X.2.1.1.m1.*******.4.cmml"><mfrac id="S4.E5X.2.1.1.m1.*******.4a" xref="S4.E5X.2.1.1.m1.*******.4.cmml"><mn id="S4.E5X.2.1.1.m1.*******.4.2" xref="S4.E5X.2.1.1.m1.*******.4.2.cmml">1</mn><mn id="S4.E5X.2.1.1.m1.*******.4.3" xref="S4.E5X.2.1.1.m1.*******.4.3.cmml">2</mn></mfrac></mstyle><mo id="S4.E5X.2.1.1.m1.*******.3" xref="S4.E5X.2.1.1.m1.*******.3.cmml">⁢</mo><mrow id="S4.E5X.2.1.1.m1.*******.2" xref="S4.E5X.2.1.1.m1.*******.2.cmml"><mstyle displaystyle="true" id="S4.E5X.2.1.1.m1.*******.2.3" xref="S4.E5X.2.1.1.m1.*******.2.3.cmml"><munder id="S4.E5X.2.1.1.m1.*******.2.3a" xref="S4.E5X.2.1.1.m1.*******.2.3.cmml"><mo id="S4.E5X.2.1.1.m1.*******.2.3.2" movablelimits="false" xref="S4.E5X.2.1.1.m1.*******.2.3.2.cmml">∑</mo><mrow id="S4.E5X.2.1.1.m*******" xref="S4.E5X.2.1.1.m*******.cmml"><mrow id="S4.E5X.2.1.1.m1.*******.2" xref="S4.E5X.2.1.1.m1.*******.3.cmml"><msub id="S4.E5X.2.1.1.m*******.1.1.1" xref="S4.E5X.2.1.1.m*******.1.1.1.cmml"><mi id="S4.E5X.2.1.1.m*******.*******" xref="S4.E5X.2.1.1.m*******.*******.cmml">𝒙</mi><mi id="S4.E5X.2.1.1.m*******.*******" xref="S4.E5X.2.1.1.m*******.*******.cmml">i</mi></msub><mo id="S4.E5X.2.1.1.m1.*******.2.3" xref="S4.E5X.2.1.1.m1.*******.3.cmml">,</mo><msub id="S4.E5X.2.1.1.m1.*******.2.2" xref="S4.E5X.2.1.1.m1.*******.2.2.cmml"><mi id="S4.E5X.2.1.1.m1.*******.2.2.2" xref="S4.E5X.2.1.1.m1.*******.2.2.2.cmml">𝒙</mi><mi id="S4.E5X.2.1.1.m1.*******.2.2.3" xref="S4.E5X.2.1.1.m1.*******.2.2.3.cmml">j</mi></msub></mrow><mo id="S4.E5X.2.1.1.m1.*******" xref="S4.E5X.2.1.1.m1.*******.cmml">∈</mo><mi class="ltx_font_mathcaligraphic" id="S4.E5X.2.1.1.m1.*******" xref="S4.E5X.2.1.1.m1.*******.cmml">𝒳</mi></mrow></munder></mstyle><mrow id="S4.E5X.2.1.1.m1.*******.2.2" xref="S4.E5X.2.1.1.m1.*******.2.2.cmml"><msub id="S4.E5X.2.1.1.m1.*******.2.2.4" xref="S4.E5X.2.1.1.m1.*******.2.2.4.cmml"><mi id="S4.E5X.2.1.1.m1.*******.*******" xref="S4.E5X.2.1.1.m1.*******.*******.cmml">d</mi><mi id="S4.E5X.2.1.1.m1.*******.*******" xref="S4.E5X.2.1.1.m1.*******.*******.cmml">𝑴</mi></msub><mo id="S4.E5X.2.1.1.m1.*******.2.2.3" xref="S4.E5X.2.1.1.m1.*******.2.2.3.cmml">⁢</mo><mrow id="S4.E5X.2.1.1.m1.*******.*******" xref="S4.E5X.2.1.1.m1.*******.*******.cmml"><mo id="S4.E5X.2.1.1.m1.*******.*******.3" stretchy="false" xref="S4.E5X.2.1.1.m1.*******.*******.cmml">(</mo><msub id="S4.E5X.2.1.1.m1.*******.*******.1" xref="S4.E5X.2.1.1.m1.*******.*******.1.cmml"><mi id="S4.E5X.2.1.1.m1.*******.*******.1.2" xref="S4.E5X.2.1.1.m1.*******.*******.1.2.cmml">𝒙</mi><mi id="S4.E5X.2.1.1.m1.*******.*******.1.3" xref="S4.E5X.2.1.1.m1.*******.*******.1.3.cmml">i</mi></msub><mo id="S4.E5X.2.1.1.m1.*******.*******.4" xref="S4.E5X.2.1.1.m1.*******.*******.cmml">,</mo><msub id="S4.E5X.2.1.1.m1.*******.*******.2" xref="S4.E5X.2.1.1.m1.*******.*******.2.cmml"><mi id="S4.E5X.2.1.1.m1.*******.*******.2.2" xref="S4.E5X.2.1.1.m1.*******.*******.2.2.cmml">𝒙</mi><mi id="S4.E5X.2.1.1.m1.*******.*******.2.3" xref="S4.E5X.2.1.1.m1.*******.*******.2.3.cmml">j</mi></msub><mo id="S4.E5X.2.1.1.m1.*******.*******.5" stretchy="false" xref="S4.E5X.2.1.1.m1.*******.*******.cmml">)</mo></mrow><mo id="S4.E5X.2.1.1.m1.*******.2.2.3a" xref="S4.E5X.2.1.1.m1.*******.2.2.3.cmml">⁢</mo><msub id="S4.E5X.2.1.1.m1.*******.2.2.5" xref="S4.E5X.2.1.1.m1.*******.2.2.5.cmml"><mi id="S4.E5X.2.1.1.m1.*******.*******" xref="S4.E5X.2.1.1.m1.*******.*******.cmml">K</mi><mrow id="S4.E5X.2.1.1.m1.*******" xref="S4.E5X.2.1.1.m1.*******.cmml"><mi id="S4.E5X.2.1.1.m1.*******" xref="S4.E5X.2.1.1.m1.*******.cmml">i</mi><mo id="S4.E5X.2.1.1.m1.*******.1" xref="S4.E5X.2.1.1.m1.*******.cmml">,</mo><mi id="S4.E5X.2.1.1.m1.*******" xref="S4.E5X.2.1.1.m1.*******.cmml">j</mi></mrow></msub></mrow></mrow></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.E5X.2.1.1.m1.6b"><list id="S4.E5X.2.1.1.m1.6.6.3.cmml" xref="S4.E5X.2.1.1.m1.6.6.2"><apply id="S4.E5X.2.1.1.m1.*******.cmml" xref="S4.E5X.2.1.1.m1.*******"><csymbol cd="ambiguous" id="S4.E5X.2.1.1.m1.*******.1.cmml" xref="S4.E5X.2.1.1.m1.*******">subscript</csymbol><min id="S4.E5X.2.1.1.m1.*******.2.cmml" xref="S4.E5X.2.1.1.m1.*******.2"></min><ci id="S4.E5X.2.1.1.m1.*******.3.cmml" xref="S4.E5X.2.1.1.m1.*******.3">𝑴</ci></apply><apply id="S4.E5X.2.1.1.m1.*******.cmml" xref="S4.E5X.2.1.1.m1.*******"><times id="S4.E5X.2.1.1.m1.*******.3.cmml" xref="S4.E5X.2.1.1.m1.*******.3"></times><apply id="S4.E5X.2.1.1.m1.*******.4.cmml" xref="S4.E5X.2.1.1.m1.*******.4"><divide id="S4.E5X.2.1.1.m1.*******.4.1.cmml" xref="S4.E5X.2.1.1.m1.*******.4"></divide><cn id="S4.E5X.2.1.1.m1.*******.4.2.cmml" type="integer" xref="S4.E5X.2.1.1.m1.*******.4.2">1</cn><cn id="S4.E5X.2.1.1.m1.*******.4.3.cmml" type="integer" xref="S4.E5X.2.1.1.m1.*******.4.3">2</cn></apply><apply id="S4.E5X.2.1.1.m1.*******.2.cmml" xref="S4.E5X.2.1.1.m1.*******.2"><apply id="S4.E5X.2.1.1.m1.*******.2.3.cmml" xref="S4.E5X.2.1.1.m1.*******.2.3"><csymbol cd="ambiguous" id="S4.E5X.2.1.1.m1.*******.2.3.1.cmml" xref="S4.E5X.2.1.1.m1.*******.2.3">subscript</csymbol><sum id="S4.E5X.2.1.1.m1.*******.2.3.2.cmml" xref="S4.E5X.2.1.1.m1.*******.2.3.2"></sum><apply id="S4.E5X.2.1.1.m*******.cmml" xref="S4.E5X.2.1.1.m*******"><in id="S4.E5X.2.1.1.m1.*******.cmml" xref="S4.E5X.2.1.1.m1.*******"></in><list id="S4.E5X.2.1.1.m1.*******.3.cmml" xref="S4.E5X.2.1.1.m1.*******.2"><apply id="S4.E5X.2.1.1.m*******.1.1.1.cmml" xref="S4.E5X.2.1.1.m*******.1.1.1"><csymbol cd="ambiguous" id="S4.E5X.2.1.1.m*******.*******.cmml" xref="S4.E5X.2.1.1.m*******.1.1.1">subscript</csymbol><ci id="S4.E5X.2.1.1.m*******.*******.cmml" xref="S4.E5X.2.1.1.m*******.*******">𝒙</ci><ci id="S4.E5X.2.1.1.m*******.*******.cmml" xref="S4.E5X.2.1.1.m*******.*******">𝑖</ci></apply><apply id="S4.E5X.2.1.1.m1.*******.2.2.cmml" xref="S4.E5X.2.1.1.m1.*******.2.2"><csymbol cd="ambiguous" id="S4.E5X.2.1.1.m*******.*******.cmml" xref="S4.E5X.2.1.1.m1.*******.2.2">subscript</csymbol><ci id="S4.E5X.2.1.1.m1.*******.2.2.2.cmml" xref="S4.E5X.2.1.1.m1.*******.2.2.2">𝒙</ci><ci id="S4.E5X.2.1.1.m1.*******.2.2.3.cmml" xref="S4.E5X.2.1.1.m1.*******.2.2.3">𝑗</ci></apply></list><ci id="S4.E5X.2.1.1.m1.*******.cmml" xref="S4.E5X.2.1.1.m1.*******">𝒳</ci></apply></apply><apply id="S4.E5X.2.1.1.m1.*******.2.2.cmml" xref="S4.E5X.2.1.1.m1.*******.2.2"><times id="S4.E5X.2.1.1.m1.*******.2.2.3.cmml" xref="S4.E5X.2.1.1.m1.*******.2.2.3"></times><apply id="S4.E5X.2.1.1.m1.*******.2.2.4.cmml" xref="S4.E5X.2.1.1.m1.*******.2.2.4"><csymbol cd="ambiguous" id="S4.E5X.2.1.1.m1.*******.*******.cmml" xref="S4.E5X.2.1.1.m1.*******.2.2.4">subscript</csymbol><ci id="S4.E5X.2.1.1.m1.*******.*******.cmml" xref="S4.E5X.2.1.1.m1.*******.*******">𝑑</ci><ci id="S4.E5X.2.1.1.m1.*******.*******.cmml" xref="S4.E5X.2.1.1.m1.*******.*******">𝑴</ci></apply><interval closure="open" id="S4.E5X.2.1.1.m1.*******.*******.cmml" xref="S4.E5X.2.1.1.m1.*******.*******"><apply id="S4.E5X.2.1.1.m1.*******.*******.1.cmml" xref="S4.E5X.2.1.1.m1.*******.*******.1"><csymbol cd="ambiguous" id="S4.E5X.2.1.1.m1.*******.*******.1.1.cmml" xref="S4.E5X.2.1.1.m1.*******.*******.1">subscript</csymbol><ci id="S4.E5X.2.1.1.m1.*******.*******.1.2.cmml" xref="S4.E5X.2.1.1.m1.*******.*******.1.2">𝒙</ci><ci id="S4.E5X.2.1.1.m1.*******.*******.1.3.cmml" xref="S4.E5X.2.1.1.m1.*******.*******.1.3">𝑖</ci></apply><apply id="S4.E5X.2.1.1.m1.*******.*******.2.cmml" xref="S4.E5X.2.1.1.m1.*******.*******.2"><csymbol cd="ambiguous" id="S4.E5X.2.1.1.m1.*******.2.2.*******.cmml" xref="S4.E5X.2.1.1.m1.*******.*******.2">subscript</csymbol><ci id="S4.E5X.2.1.1.m1.*******.*******.2.2.cmml" xref="S4.E5X.2.1.1.m1.*******.*******.2.2">𝒙</ci><ci id="S4.E5X.2.1.1.m1.*******.*******.2.3.cmml" xref="S4.E5X.2.1.1.m1.*******.*******.2.3">𝑗</ci></apply></interval><apply id="S4.E5X.2.1.1.m1.*******.2.2.5.cmml" xref="S4.E5X.2.1.1.m1.*******.2.2.5"><csymbol cd="ambiguous" id="S4.E5X.2.1.1.m1.*******.*******.cmml" xref="S4.E5X.2.1.1.m1.*******.2.2.5">subscript</csymbol><ci id="S4.E5X.2.1.1.m1.*******.*******.cmml" xref="S4.E5X.2.1.1.m1.*******.*******">𝐾</ci><list id="S4.E5X.2.1.1.m1.*******.cmml" xref="S4.E5X.2.1.1.m1.*******"><ci id="S4.E5X.2.1.1.m1.*******.cmml" xref="S4.E5X.2.1.1.m1.*******">𝑖</ci><ci id="S4.E5X.2.1.1.m1.*******.cmml" xref="S4.E5X.2.1.1.m1.*******">𝑗</ci></list></apply></apply></apply></apply></list></annotation-xml><annotation encoding="application/x-tex" id="S4.E5X.2.1.1.m1.6c">\displaystyle\min_{{\bm{M}}}\quad\frac{1}{2}\sum\limits_{{\bm{x}}_{i},{\bm{x}}%
_{j}\in\mathcal{X}}d_{{\bm{M}}}({\bm{x}}_{i},{\bm{x}}_{j})K_{i,j}</annotation><annotation encoding="application/x-llamapun" id="S4.E5X.2.1.1.m1.6d">roman_min start_POSTSUBSCRIPT bold_italic_M end_POSTSUBSCRIPT divide start_ARG 1 end_ARG start_ARG 2 end_ARG ∑ start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT , bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT ∈ caligraphic_X end_POSTSUBSCRIPT italic_d start_POSTSUBSCRIPT bold_italic_M end_POSTSUBSCRIPT ( bold_italic_x start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT , bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT ) italic_K start_POSTSUBSCRIPT italic_i , italic_j end_POSTSUBSCRIPT</annotation></semantics></math></td>
<td class="ltx_eqn_cell ltx_eqn_center_padright"></td>
<td class="ltx_eqn_cell ltx_eqn_eqno ltx_align_middle ltx_align_right" rowspan="3"><span class="ltx_tag ltx_tag_equationgroup ltx_align_right">(5)</span></td>
</tr>
<tr class="ltx_equation ltx_eqn_row ltx_align_baseline" id="S4.E5Xa">
<td class="ltx_eqn_cell ltx_eqn_center_padleft"></td>
<td class="ltx_td ltx_align_right ltx_eqn_cell"><math alttext="\displaystyle=" class="ltx_Math" display="inline" id="S4.E5Xa.2.1.1.m1.1"><semantics id="S4.E5Xa.2.1.1.m1.1a"><mo id="S4.E5Xa.2.1.1.m1.1.1" xref="S4.E5Xa.2.1.1.m1.1.1.cmml">=</mo><annotation-xml encoding="MathML-Content" id="S4.E5Xa.2.1.1.m1.1b"><eq id="S4.E5Xa.2.1.1.m1.1.1.cmml" xref="S4.E5Xa.2.1.1.m1.1.1"></eq></annotation-xml><annotation encoding="application/x-tex" id="S4.E5Xa.2.1.1.m1.1c">\displaystyle=</annotation><annotation encoding="application/x-llamapun" id="S4.E5Xa.2.1.1.m1.1d">=</annotation></semantics></math></td>
<td class="ltx_td ltx_align_left ltx_eqn_cell"><math alttext="\displaystyle\min_{{\bm{M}}}\quad\frac{1}{2}\sum\limits_{{\bm{x}}_{i},{\bm{x}}%
_{j}\in\mathcal{X}}({\bm{x}}_{i}-{\bm{x}}_{j})^{T}{\bm{M}}({\bm{x}}_{i}-{\bm{x%
}}_{j})K_{i,j}" class="ltx_Math" display="inline" id="S4.E5Xa.3.2.2.m1.6"><semantics id="S4.E5Xa.3.2.2.m1.6a"><mrow id="S4.E5Xa.3.2.2.m1.6.6.2" xref="S4.E5Xa.3.2.2.m1.6.6.3.cmml"><munder id="S4.E5Xa.3.2.2.m1.*******" xref="S4.E5Xa.3.2.2.m1.*******.cmml"><mi id="S4.E5Xa.3.2.2.m1.*******.2" xref="S4.E5Xa.3.2.2.m1.*******.2.cmml">min</mi><mi id="S4.E5Xa.3.2.2.m1.*******.3" xref="S4.E5Xa.3.2.2.m1.*******.3.cmml">𝑴</mi></munder><mspace id="S4.E5Xa.3.2.2.m1.*******" width="1.167em" xref="S4.E5Xa.3.2.2.m1.6.6.3.cmml"></mspace><mrow id="S4.E5Xa.3.2.2.m1.*******" xref="S4.E5Xa.3.2.2.m1.*******.cmml"><mstyle displaystyle="true" id="S4.E5Xa.3.2.2.m1.*******.4" xref="S4.E5Xa.3.2.2.m1.*******.4.cmml"><mfrac id="S4.E5Xa.3.2.2.m1.*******.4a" xref="S4.E5Xa.3.2.2.m1.*******.4.cmml"><mn id="S4.E5Xa.3.2.2.m1.*******.4.2" xref="S4.E5Xa.3.2.2.m1.*******.4.2.cmml">1</mn><mn id="S4.E5Xa.3.2.2.m1.*******.4.3" xref="S4.E5Xa.3.2.2.m1.*******.4.3.cmml">2</mn></mfrac></mstyle><mo id="S4.E5Xa.3.2.2.m1.*******.3" xref="S4.E5Xa.3.2.2.m1.*******.3.cmml">⁢</mo><mrow id="S4.E5Xa.3.2.2.m1.*******.2" xref="S4.E5Xa.3.2.2.m1.*******.2.cmml"><mstyle displaystyle="true" id="S4.E5Xa.3.2.2.m1.*******.2.3" xref="S4.E5Xa.3.2.2.m1.*******.2.3.cmml"><munder id="S4.E5Xa.3.2.2.m1.*******.2.3a" xref="S4.E5Xa.3.2.2.m1.*******.2.3.cmml"><mo id="S4.E5Xa.3.2.2.m1.*******.2.3.2" movablelimits="false" xref="S4.E5Xa.3.2.2.m1.*******.2.3.2.cmml">∑</mo><mrow id="S4.E5Xa.3.2.2.m*******" xref="S4.E5Xa.3.2.2.m*******.cmml"><mrow id="S4.E5Xa.3.2.2.m1.*******.2" xref="S4.E5Xa.3.2.2.m1.*******.3.cmml"><msub id="S4.E5Xa.3.2.2.m*******.1.1.1" xref="S4.E5Xa.3.2.2.m*******.1.1.1.cmml"><mi id="S4.E5Xa.3.2.2.m*******.*******" xref="S4.E5Xa.3.2.2.m*******.*******.cmml">𝒙</mi><mi id="S4.E5Xa.3.2.2.m*******.*******" xref="S4.E5Xa.3.2.2.m*******.*******.cmml">i</mi></msub><mo id="S4.E5Xa.3.2.2.m1.*******.2.3" xref="S4.E5Xa.3.2.2.m1.*******.3.cmml">,</mo><msub id="S4.E5Xa.3.2.2.m1.*******.2.2" xref="S4.E5Xa.3.2.2.m1.*******.2.2.cmml"><mi id="S4.E5Xa.3.2.2.m1.*******.2.2.2" xref="S4.E5Xa.3.2.2.m1.*******.2.2.2.cmml">𝒙</mi><mi id="S4.E5Xa.3.2.2.m1.*******.2.2.3" xref="S4.E5Xa.3.2.2.m1.*******.2.2.3.cmml">j</mi></msub></mrow><mo id="S4.E5Xa.3.2.2.m1.*******" xref="S4.E5Xa.3.2.2.m1.*******.cmml">∈</mo><mi class="ltx_font_mathcaligraphic" id="S4.E5Xa.3.2.2.m1.*******" xref="S4.E5Xa.3.2.2.m1.*******.cmml">𝒳</mi></mrow></munder></mstyle><mrow id="S4.E5Xa.3.2.2.m1.*******.2.2" xref="S4.E5Xa.3.2.2.m1.*******.2.2.cmml"><msup id="S4.E5Xa.3.2.2.m1.*******.1.1.1" xref="S4.E5Xa.3.2.2.m1.*******.1.1.1.cmml"><mrow id="S4.E5Xa.3.2.2.m1.*******.*******.1" xref="S4.E5Xa.3.2.2.m1.*******.*******.1.1.cmml"><mo id="S4.E5Xa.3.2.2.m1.*******.*******.1.2" stretchy="false" xref="S4.E5Xa.3.2.2.m1.*******.*******.1.1.cmml">(</mo><mrow id="S4.E5Xa.3.2.2.m1.*******.*******.1.1" xref="S4.E5Xa.3.2.2.m1.*******.*******.1.1.cmml"><msub id="S4.E5Xa.3.2.2.m1.*******.*******.1.1.2" xref="S4.E5Xa.3.2.2.m1.*******.*******.1.1.2.cmml"><mi id="S4.E5Xa.3.2.2.m1.*******.*******.*******" xref="S4.E5Xa.3.2.2.m1.*******.*******.*******.cmml">𝒙</mi><mi id="S4.E5Xa.3.2.2.m1.*******.*******.*******" xref="S4.E5Xa.3.2.2.m1.*******.*******.*******.cmml">i</mi></msub><mo id="S4.E5Xa.3.2.2.m1.*******.*******.1.1.1" xref="S4.E5Xa.3.2.2.m1.*******.*******.1.1.1.cmml">−</mo><msub id="S4.E5Xa.3.2.2.m1.*******.*******.1.1.3" xref="S4.E5Xa.3.2.2.m1.*******.*******.1.1.3.cmml"><mi id="S4.E5Xa.3.2.2.m1.*******.*******.*******" xref="S4.E5Xa.3.2.2.m1.*******.*******.*******.cmml">𝒙</mi><mi id="S4.E5Xa.3.2.2.m1.*******.*******.*******" xref="S4.E5Xa.3.2.2.m1.*******.*******.*******.cmml">j</mi></msub></mrow><mo id="S4.E5Xa.3.2.2.m1.*******.*******.1.3" stretchy="false" xref="S4.E5Xa.3.2.2.m1.*******.*******.1.1.cmml">)</mo></mrow><mi id="S4.E5Xa.3.2.2.m1.*******.*******" xref="S4.E5Xa.3.2.2.m1.*******.*******.cmml">T</mi></msup><mo id="S4.E5Xa.3.2.2.m1.*******.2.2.3" xref="S4.E5Xa.3.2.2.m1.*******.2.2.3.cmml">⁢</mo><mi id="S4.E5Xa.3.2.2.m1.*******.2.2.4" xref="S4.E5Xa.3.2.2.m1.*******.2.2.4.cmml">𝑴</mi><mo id="S4.E5Xa.3.2.2.m1.*******.2.2.3a" xref="S4.E5Xa.3.2.2.m1.*******.2.2.3.cmml">⁢</mo><mrow id="S4.E5Xa.3.2.2.m1.*******.*******" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.cmml"><mo id="S4.E5Xa.3.2.2.m1.*******.*******.2" stretchy="false" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.cmml">(</mo><mrow id="S4.E5Xa.3.2.2.m1.*******.2.*******" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.cmml"><msub id="S4.E5Xa.3.2.2.m1.*******.2.*******.2" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.2.cmml"><mi id="S4.E5Xa.3.2.2.m1.*******.2.*******.2.2" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.2.2.cmml">𝒙</mi><mi id="S4.E5Xa.3.2.2.m1.*******.2.*******.2.3" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.2.3.cmml">i</mi></msub><mo id="S4.E5Xa.3.2.2.m1.*******.2.*******.1" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.1.cmml">−</mo><msub id="S4.E5Xa.3.2.2.m1.*******.2.*******.3" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.3.cmml"><mi id="S4.E5Xa.3.2.2.m1.*******.2.*******.3.2" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.3.2.cmml">𝒙</mi><mi id="S4.E5Xa.3.2.2.m1.*******.2.*******.3.3" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.3.3.cmml">j</mi></msub></mrow><mo id="S4.E5Xa.3.2.2.m1.*******.*******.3" stretchy="false" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.cmml">)</mo></mrow><mo id="S4.E5Xa.3.2.2.m1.*******.2.2.3b" xref="S4.E5Xa.3.2.2.m1.*******.2.2.3.cmml">⁢</mo><msub id="S4.E5Xa.3.2.2.m1.*******.2.2.5" xref="S4.E5Xa.3.2.2.m1.*******.2.2.5.cmml"><mi id="S4.E5Xa.3.2.2.m1.*******.*******" xref="S4.E5Xa.3.2.2.m1.*******.*******.cmml">K</mi><mrow id="S4.E5Xa.3.2.2.m1.*******" xref="S4.E5Xa.3.2.2.m1.*******.cmml"><mi id="S4.E5Xa.3.2.2.m1.*******" xref="S4.E5Xa.3.2.2.m1.*******.cmml">i</mi><mo id="S4.E5Xa.3.2.2.m1.*******.1" xref="S4.E5Xa.3.2.2.m1.*******.cmml">,</mo><mi id="S4.E5Xa.3.2.2.m1.*******" xref="S4.E5Xa.3.2.2.m1.*******.cmml">j</mi></mrow></msub></mrow></mrow></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.E5Xa.3.2.2.m1.6b"><list id="S4.E5Xa.3.2.2.m1.6.6.3.cmml" xref="S4.E5Xa.3.2.2.m1.6.6.2"><apply id="S4.E5Xa.3.2.2.m1.*******.cmml" xref="S4.E5Xa.3.2.2.m1.*******"><csymbol cd="ambiguous" id="S4.E5Xa.3.2.2.m1.*******.1.cmml" xref="S4.E5Xa.3.2.2.m1.*******">subscript</csymbol><min id="S4.E5Xa.3.2.2.m1.*******.2.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2"></min><ci id="S4.E5Xa.3.2.2.m1.*******.3.cmml" xref="S4.E5Xa.3.2.2.m1.*******.3">𝑴</ci></apply><apply id="S4.E5Xa.3.2.2.m1.*******.cmml" xref="S4.E5Xa.3.2.2.m1.*******"><times id="S4.E5Xa.3.2.2.m1.*******.3.cmml" xref="S4.E5Xa.3.2.2.m1.*******.3"></times><apply id="S4.E5Xa.3.2.2.m1.*******.4.cmml" xref="S4.E5Xa.3.2.2.m1.*******.4"><divide id="S4.E5Xa.3.2.2.m1.*******.4.1.cmml" xref="S4.E5Xa.3.2.2.m1.*******.4"></divide><cn id="S4.E5Xa.3.2.2.m1.*******.4.2.cmml" type="integer" xref="S4.E5Xa.3.2.2.m1.*******.4.2">1</cn><cn id="S4.E5Xa.3.2.2.m1.*******.4.3.cmml" type="integer" xref="S4.E5Xa.3.2.2.m1.*******.4.3">2</cn></apply><apply id="S4.E5Xa.3.2.2.m1.*******.2.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2"><apply id="S4.E5Xa.3.2.2.m1.*******.2.3.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.3"><csymbol cd="ambiguous" id="S4.E5Xa.3.2.2.m1.*******.2.3.1.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.3">subscript</csymbol><sum id="S4.E5Xa.3.2.2.m1.*******.2.3.2.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.3.2"></sum><apply id="S4.E5Xa.3.2.2.m*******.cmml" xref="S4.E5Xa.3.2.2.m*******"><in id="S4.E5Xa.3.2.2.m1.*******.cmml" xref="S4.E5Xa.3.2.2.m1.*******"></in><list id="S4.E5Xa.3.2.2.m1.*******.3.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2"><apply id="S4.E5Xa.3.2.2.m*******.1.1.1.cmml" xref="S4.E5Xa.3.2.2.m*******.1.1.1"><csymbol cd="ambiguous" id="S4.E5Xa.3.2.2.m*******.*******.cmml" xref="S4.E5Xa.3.2.2.m*******.1.1.1">subscript</csymbol><ci id="S4.E5Xa.3.2.2.m*******.*******.cmml" xref="S4.E5Xa.3.2.2.m*******.*******">𝒙</ci><ci id="S4.E5Xa.3.2.2.m*******.*******.cmml" xref="S4.E5Xa.3.2.2.m*******.*******">𝑖</ci></apply><apply id="S4.E5Xa.3.2.2.m1.*******.2.2.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.2"><csymbol cd="ambiguous" id="S4.E5Xa.3.2.2.m*******.*******.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.2">subscript</csymbol><ci id="S4.E5Xa.3.2.2.m1.*******.2.2.2.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.2.2">𝒙</ci><ci id="S4.E5Xa.3.2.2.m1.*******.2.2.3.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.2.3">𝑗</ci></apply></list><ci id="S4.E5Xa.3.2.2.m1.*******.cmml" xref="S4.E5Xa.3.2.2.m1.*******">𝒳</ci></apply></apply><apply id="S4.E5Xa.3.2.2.m1.*******.2.2.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.2"><times id="S4.E5Xa.3.2.2.m1.*******.2.2.3.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.2.3"></times><apply id="S4.E5Xa.3.2.2.m1.*******.1.1.1.cmml" xref="S4.E5Xa.3.2.2.m1.*******.1.1.1"><csymbol cd="ambiguous" id="S4.E5Xa.3.2.2.m1.*******.*******.cmml" xref="S4.E5Xa.3.2.2.m1.*******.1.1.1">superscript</csymbol><apply id="S4.E5Xa.3.2.2.m1.*******.*******.1.1.cmml" xref="S4.E5Xa.3.2.2.m1.*******.*******.1"><minus id="S4.E5Xa.3.2.2.m1.*******.*******.1.1.1.cmml" xref="S4.E5Xa.3.2.2.m1.*******.*******.1.1.1"></minus><apply id="S4.E5Xa.3.2.2.m1.*******.*******.1.1.2.cmml" xref="S4.E5Xa.3.2.2.m1.*******.*******.1.1.2"><csymbol cd="ambiguous" id="S4.E5Xa.3.2.2.m1.*******.*******.*******.cmml" xref="S4.E5Xa.3.2.2.m1.*******.*******.1.1.2">subscript</csymbol><ci id="S4.E5Xa.3.2.2.m1.*******.*******.*******.cmml" xref="S4.E5Xa.3.2.2.m1.*******.*******.*******">𝒙</ci><ci id="S4.E5Xa.3.2.2.m1.*******.*******.*******.cmml" xref="S4.E5Xa.3.2.2.m1.*******.*******.*******">𝑖</ci></apply><apply id="S4.E5Xa.3.2.2.m1.*******.*******.1.1.3.cmml" xref="S4.E5Xa.3.2.2.m1.*******.*******.1.1.3"><csymbol cd="ambiguous" id="S4.E5Xa.3.2.2.m1.*******.*******.*******.cmml" xref="S4.E5Xa.3.2.2.m1.*******.*******.1.1.3">subscript</csymbol><ci id="S4.E5Xa.3.2.2.m1.*******.*******.*******.cmml" xref="S4.E5Xa.3.2.2.m1.*******.*******.*******">𝒙</ci><ci id="S4.E5Xa.3.2.2.m1.*******.*******.*******.cmml" xref="S4.E5Xa.3.2.2.m1.*******.*******.*******">𝑗</ci></apply></apply><ci id="S4.E5Xa.3.2.2.m1.*******.*******.cmml" xref="S4.E5Xa.3.2.2.m1.*******.*******">𝑇</ci></apply><ci id="S4.E5Xa.3.2.2.m1.*******.2.2.4.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.2.4">𝑴</ci><apply id="S4.E5Xa.3.2.2.m1.*******.2.*******.cmml" xref="S4.E5Xa.3.2.2.m1.*******.*******"><minus id="S4.E5Xa.3.2.2.m1.*******.2.*******.1.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.1"></minus><apply id="S4.E5Xa.3.2.2.m1.*******.2.*******.2.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.2"><csymbol cd="ambiguous" id="S4.E5Xa.3.2.2.m1.*******.2.*******.2.1.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.2">subscript</csymbol><ci id="S4.E5Xa.3.2.2.m1.*******.2.*******.2.2.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.2.2">𝒙</ci><ci id="S4.E5Xa.3.2.2.m1.*******.2.*******.2.3.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.2.3">𝑖</ci></apply><apply id="S4.E5Xa.3.2.2.m1.*******.2.*******.3.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.3"><csymbol cd="ambiguous" id="S4.E5Xa.3.2.2.m1.*******.2.*******.3.1.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.3">subscript</csymbol><ci id="S4.E5Xa.3.2.2.m1.*******.2.*******.3.2.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.3.2">𝒙</ci><ci id="S4.E5Xa.3.2.2.m1.*******.2.*******.3.3.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.*******.3.3">𝑗</ci></apply></apply><apply id="S4.E5Xa.3.2.2.m1.*******.2.2.5.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.2.5"><csymbol cd="ambiguous" id="S4.E5Xa.3.2.2.m1.*******.*******.cmml" xref="S4.E5Xa.3.2.2.m1.*******.2.2.5">subscript</csymbol><ci id="S4.E5Xa.3.2.2.m1.*******.*******.cmml" xref="S4.E5Xa.3.2.2.m1.*******.*******">𝐾</ci><list id="S4.E5Xa.3.2.2.m1.*******.cmml" xref="S4.E5Xa.3.2.2.m1.*******"><ci id="S4.E5Xa.3.2.2.m1.*******.cmml" xref="S4.E5Xa.3.2.2.m1.*******">𝑖</ci><ci id="S4.E5Xa.3.2.2.m1.*******.cmml" xref="S4.E5Xa.3.2.2.m1.*******">𝑗</ci></list></apply></apply></apply></apply></list></annotation-xml><annotation encoding="application/x-tex" id="S4.E5Xa.3.2.2.m1.6c">\displaystyle\min_{{\bm{M}}}\quad\frac{1}{2}\sum\limits_{{\bm{x}}_{i},{\bm{x}}%
_{j}\in\mathcal{X}}({\bm{x}}_{i}-{\bm{x}}_{j})^{T}{\bm{M}}({\bm{x}}_{i}-{\bm{x%
}}_{j})K_{i,j}</annotation><annotation encoding="application/x-llamapun" id="S4.E5Xa.3.2.2.m1.6d">roman_min start_POSTSUBSCRIPT bold_italic_M end_POSTSUBSCRIPT divide start_ARG 1 end_ARG start_ARG 2 end_ARG ∑ start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT , bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT ∈ caligraphic_X end_POSTSUBSCRIPT ( bold_italic_x start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT - bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT ) start_POSTSUPERSCRIPT italic_T end_POSTSUPERSCRIPT bold_italic_M ( bold_italic_x start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT - bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT ) italic_K start_POSTSUBSCRIPT italic_i , italic_j end_POSTSUBSCRIPT</annotation></semantics></math></td>
<td class="ltx_eqn_cell ltx_eqn_center_padright"></td>
</tr>
<tr class="ltx_equation ltx_eqn_row ltx_align_baseline" id="S4.E5Xb">
<td class="ltx_eqn_cell ltx_eqn_center_padleft"></td>
<td class="ltx_td ltx_align_right ltx_eqn_cell"><math alttext="\displaystyle=" class="ltx_Math" display="inline" id="S4.E5Xb.2.1.1.m1.1"><semantics id="S4.E5Xb.2.1.1.m1.1a"><mo id="S4.E5Xb.2.1.1.m1.1.1" xref="S4.E5Xb.2.1.1.m1.1.1.cmml">=</mo><annotation-xml encoding="MathML-Content" id="S4.E5Xb.2.1.1.m1.1b"><eq id="S4.E5Xb.2.1.1.m1.1.1.cmml" xref="S4.E5Xb.2.1.1.m1.1.1"></eq></annotation-xml><annotation encoding="application/x-tex" id="S4.E5Xb.2.1.1.m1.1c">\displaystyle=</annotation><annotation encoding="application/x-llamapun" id="S4.E5Xb.2.1.1.m1.1d">=</annotation></semantics></math></td>
<td class="ltx_td ltx_align_left ltx_eqn_cell"><math alttext="\displaystyle\min_{{\bm{M}}}\sum\limits_{{\bm{x}}_{i},{\bm{x}}_{j}\in\mathcal{%
X}}({\bm{x}}_{i}^{T}{\bm{M}}{\bm{x}}_{i}-{\bm{x}}_{i}^{T}{\bm{M}}{\bm{x}}_{j})%
K_{i,j}\quad," class="ltx_Math" display="inline" id="S4.E5Xb.3.2.2.m1.5"><semantics id="S4.E5Xb.3.2.2.m1.5a"><mrow id="S4.E5Xb.3.2.2.m1.5.5.1" xref="S4.E5Xb.3.2.2.m1.*******.cmml"><mrow id="S4.E5Xb.3.2.2.m1.*******" xref="S4.E5Xb.3.2.2.m1.*******.cmml"><munder id="S4.E5Xb.3.2.2.m1.*******.3" xref="S4.E5Xb.3.2.2.m1.*******.3.cmml"><mi id="S4.E5Xb.3.2.2.m1.*******.3.2" xref="S4.E5Xb.3.2.2.m1.*******.3.2.cmml">min</mi><mi id="S4.E5Xb.3.2.2.m1.*******.3.3" xref="S4.E5Xb.3.2.2.m1.*******.3.3.cmml">𝑴</mi></munder><mo id="S4.E5Xb.3.2.2.m1.*******.2" lspace="0.167em" xref="S4.E5Xb.3.2.2.m1.*******.2.cmml">⁢</mo><mrow id="S4.E5Xb.3.2.2.m1.*******.1" xref="S4.E5Xb.3.2.2.m1.*******.1.cmml"><mstyle displaystyle="true" id="S4.E5Xb.3.2.2.m1.*******.1.2" xref="S4.E5Xb.3.2.2.m1.*******.1.2.cmml"><munder id="S4.E5Xb.3.2.2.m1.*******.1.2a" xref="S4.E5Xb.3.2.2.m1.*******.1.2.cmml"><mo id="S4.E5Xb.3.2.2.m1.*******.1.2.2" movablelimits="false" xref="S4.E5Xb.3.2.2.m1.*******.1.2.2.cmml">∑</mo><mrow id="S4.E5Xb.3.2.2.m*******" xref="S4.E5Xb.3.2.2.m*******.cmml"><mrow id="S4.E5Xb.3.2.2.m1.*******.2" xref="S4.E5Xb.3.2.2.m1.*******.3.cmml"><msub id="S4.E5Xb.3.2.2.m*******.1.1.1" xref="S4.E5Xb.3.2.2.m*******.1.1.1.cmml"><mi id="S4.E5Xb.3.2.2.m*******.*******" xref="S4.E5Xb.3.2.2.m*******.*******.cmml">𝒙</mi><mi id="S4.E5Xb.3.2.2.m*******.*******" xref="S4.E5Xb.3.2.2.m*******.*******.cmml">i</mi></msub><mo id="S4.E5Xb.3.2.2.m1.*******.2.3" xref="S4.E5Xb.3.2.2.m1.*******.3.cmml">,</mo><msub id="S4.E5Xb.3.2.2.m1.*******.2.2" xref="S4.E5Xb.3.2.2.m1.*******.2.2.cmml"><mi id="S4.E5Xb.3.2.2.m1.*******.2.2.2" xref="S4.E5Xb.3.2.2.m1.*******.2.2.2.cmml">𝒙</mi><mi id="S4.E5Xb.3.2.2.m1.*******.2.2.3" xref="S4.E5Xb.3.2.2.m1.*******.2.2.3.cmml">j</mi></msub></mrow><mo id="S4.E5Xb.3.2.2.m1.*******" xref="S4.E5Xb.3.2.2.m1.*******.cmml">∈</mo><mi class="ltx_font_mathcaligraphic" id="S4.E5Xb.3.2.2.m1.*******" xref="S4.E5Xb.3.2.2.m1.*******.cmml">𝒳</mi></mrow></munder></mstyle><mrow id="S4.E5Xb.3.2.2.m1.*******.1.1" xref="S4.E5Xb.3.2.2.m1.*******.1.1.cmml"><mrow id="S4.E5Xb.3.2.2.m1.*******.*******" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.cmml"><mo id="S4.E5Xb.3.2.2.m1.*******.*******.2" stretchy="false" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.cmml">(</mo><mrow id="S4.E5Xb.3.2.2.m1.*******.*******.1" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.cmml"><mrow id="S4.E5Xb.3.2.2.m1.*******.*******.1.2" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.2.cmml"><msubsup id="S4.E5Xb.3.2.2.m1.*******.*******.1.2.2" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.2.2.cmml"><mi id="S4.E5Xb.3.2.2.m1.*******.*******.1.*******" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.*******.cmml">𝒙</mi><mi id="S4.E5Xb.3.2.2.m1.*******.*******.1.*******" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.*******.cmml">i</mi><mi id="S4.E5Xb.3.2.2.m1.*******.*******.*******" xref="S4.E5Xb.3.2.2.m1.*******.*******.*******.cmml">T</mi></msubsup><mo id="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******" xref="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.cmml">⁢</mo><mi id="S4.E5Xb.3.2.2.m1.*******.*******.1.2.3" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.2.3.cmml">𝑴</mi><mo id="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******a" xref="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.cmml">⁢</mo><msub id="S4.E5Xb.3.2.2.m1.*******.*******.1.2.4" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.2.4.cmml"><mi id="S4.E5Xb.3.2.2.m1.*******.*******.1.2.4.2" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.2.4.2.cmml">𝒙</mi><mi id="S4.E5Xb.3.2.2.m1.*******.*******.1.2.4.3" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.2.4.3.cmml">i</mi></msub></mrow><mo id="S4.E5Xb.3.2.2.m1.*******.*******.1.1" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.1.cmml">−</mo><mrow id="S4.E5Xb.3.2.2.m1.*******.*******.1.3" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.3.cmml"><msubsup id="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******" xref="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.cmml"><mi id="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.2.2" xref="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.2.2.cmml">𝒙</mi><mi id="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.2.3" xref="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.2.3.cmml">i</mi><mi id="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.3" xref="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.3.cmml">T</mi></msubsup><mo id="S4.E5Xb.3.2.2.m1.*******.*******.1.3.1" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.3.1.cmml">⁢</mo><mi id="S4.E5Xb.3.2.2.m1.*******.*******.1.3.3" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.3.3.cmml">𝑴</mi><mo id="S4.E5Xb.3.2.2.m1.*******.*******.1.3.1a" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.3.1.cmml">⁢</mo><msub id="S4.E5Xb.3.2.2.m1.*******.*******.1.3.4" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.3.4.cmml"><mi id="S4.E5Xb.3.2.2.m1.*******.*******.1.3.4.2" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.3.4.2.cmml">𝒙</mi><mi id="S4.E5Xb.3.2.2.m1.*******.*******.1.3.4.3" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.3.4.3.cmml">j</mi></msub></mrow></mrow><mo id="S4.E5Xb.3.2.2.m1.*******.*******.3" stretchy="false" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.cmml">)</mo></mrow><mo id="S4.E5Xb.3.2.2.m1.*******.1.1.2" xref="S4.E5Xb.3.2.2.m1.*******.1.1.2.cmml">⁢</mo><msub id="S4.E5Xb.3.2.2.m1.*******.1.1.3" xref="S4.E5Xb.3.2.2.m1.*******.1.1.3.cmml"><mi id="S4.E5Xb.3.2.2.m1.*******.*******" xref="S4.E5Xb.3.2.2.m1.*******.*******.cmml">K</mi><mrow id="S4.E5Xb.3.2.2.m1.*******" xref="S4.E5Xb.3.2.2.m1.*******.cmml"><mi id="S4.E5Xb.3.2.2.m1.*******" xref="S4.E5Xb.3.2.2.m1.*******.cmml">i</mi><mo id="S4.E5Xb.3.2.2.m1.*******.1" xref="S4.E5Xb.3.2.2.m1.*******.cmml">,</mo><mi id="S4.E5Xb.3.2.2.m1.*******" xref="S4.E5Xb.3.2.2.m1.*******.cmml">j</mi></mrow></msub></mrow></mrow></mrow><mspace id="S4.E5Xb.3.2.2.m1.*******" width="1em" xref="S4.E5Xb.3.2.2.m1.*******.cmml"></mspace><mo id="S4.E5Xb.3.2.2.m1.*******" xref="S4.E5Xb.3.2.2.m1.*******.cmml">,</mo></mrow><annotation-xml encoding="MathML-Content" id="S4.E5Xb.3.2.2.m1.5b"><apply id="S4.E5Xb.3.2.2.m1.*******.cmml" xref="S4.E5Xb.3.2.2.m1.5.5.1"><times id="S4.E5Xb.3.2.2.m1.*******.2.cmml" xref="S4.E5Xb.3.2.2.m1.*******.2"></times><apply id="S4.E5Xb.3.2.2.m1.*******.3.cmml" xref="S4.E5Xb.3.2.2.m1.*******.3"><csymbol cd="ambiguous" id="S4.E5Xb.3.2.2.m1.*******.3.1.cmml" xref="S4.E5Xb.3.2.2.m1.*******.3">subscript</csymbol><min id="S4.E5Xb.3.2.2.m1.*******.3.2.cmml" xref="S4.E5Xb.3.2.2.m1.*******.3.2"></min><ci id="S4.E5Xb.3.2.2.m1.*******.3.3.cmml" xref="S4.E5Xb.3.2.2.m1.*******.3.3">𝑴</ci></apply><apply id="S4.E5Xb.3.2.2.m1.*******.1.cmml" xref="S4.E5Xb.3.2.2.m1.*******.1"><apply id="S4.E5Xb.3.2.2.m1.*******.1.2.cmml" xref="S4.E5Xb.3.2.2.m1.*******.1.2"><csymbol cd="ambiguous" id="S4.E5Xb.3.2.2.m1.*******.1.2.1.cmml" xref="S4.E5Xb.3.2.2.m1.*******.1.2">subscript</csymbol><sum id="S4.E5Xb.3.2.2.m1.*******.1.2.2.cmml" xref="S4.E5Xb.3.2.2.m1.*******.1.2.2"></sum><apply id="S4.E5Xb.3.2.2.m*******.cmml" xref="S4.E5Xb.3.2.2.m*******"><in id="S4.E5Xb.3.2.2.m1.*******.cmml" xref="S4.E5Xb.3.2.2.m1.*******"></in><list id="S4.E5Xb.3.2.2.m1.*******.3.cmml" xref="S4.E5Xb.3.2.2.m1.*******.2"><apply id="S4.E5Xb.3.2.2.m*******.1.1.1.cmml" xref="S4.E5Xb.3.2.2.m*******.1.1.1"><csymbol cd="ambiguous" id="S4.E5Xb.3.2.2.m*******.*******.cmml" xref="S4.E5Xb.3.2.2.m*******.1.1.1">subscript</csymbol><ci id="S4.E5Xb.3.2.2.m*******.*******.cmml" xref="S4.E5Xb.3.2.2.m*******.*******">𝒙</ci><ci id="S4.E5Xb.3.2.2.m*******.*******.cmml" xref="S4.E5Xb.3.2.2.m*******.*******">𝑖</ci></apply><apply id="S4.E5Xb.3.2.2.m1.*******.2.2.cmml" xref="S4.E5Xb.3.2.2.m1.*******.2.2"><csymbol cd="ambiguous" id="S4.E5Xb.3.2.2.m*******.*******.cmml" xref="S4.E5Xb.3.2.2.m1.*******.2.2">subscript</csymbol><ci id="S4.E5Xb.3.2.2.m1.*******.2.2.2.cmml" xref="S4.E5Xb.3.2.2.m1.*******.2.2.2">𝒙</ci><ci id="S4.E5Xb.3.2.2.m1.*******.2.2.3.cmml" xref="S4.E5Xb.3.2.2.m1.*******.2.2.3">𝑗</ci></apply></list><ci id="S4.E5Xb.3.2.2.m1.*******.cmml" xref="S4.E5Xb.3.2.2.m1.*******">𝒳</ci></apply></apply><apply id="S4.E5Xb.3.2.2.m1.*******.1.1.cmml" xref="S4.E5Xb.3.2.2.m1.*******.1.1"><times id="S4.E5Xb.3.2.2.m1.*******.1.1.2.cmml" xref="S4.E5Xb.3.2.2.m1.*******.1.1.2"></times><apply id="S4.E5Xb.3.2.2.m1.*******.*******.1.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******"><minus id="S4.E5Xb.3.2.2.m1.*******.*******.1.1.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.1"></minus><apply id="S4.E5Xb.3.2.2.m1.*******.*******.1.2.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.2"><times id="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.cmml" xref="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******"></times><apply id="S4.E5Xb.3.2.2.m1.*******.*******.1.2.2.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.2.2"><csymbol cd="ambiguous" id="S4.E5Xb.3.2.2.m1.*******.*******.*******.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.2.2">superscript</csymbol><apply id="S4.E5Xb.3.2.2.m1.*******.*******.*******.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.2.2"><csymbol cd="ambiguous" id="S4.E5Xb.3.2.2.m1.*******.*******.1.*******.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.2.2">subscript</csymbol><ci id="S4.E5Xb.3.2.2.m1.*******.*******.1.*******.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.*******">𝒙</ci><ci id="S4.E5Xb.3.2.2.m1.*******.*******.1.*******.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.*******">𝑖</ci></apply><ci id="S4.E5Xb.3.2.2.m1.*******.*******.*******.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.*******">𝑇</ci></apply><ci id="S4.E5Xb.3.2.2.m1.*******.*******.1.2.3.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.2.3">𝑴</ci><apply id="S4.E5Xb.3.2.2.m1.*******.*******.1.2.4.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.2.4"><csymbol cd="ambiguous" id="S4.E5Xb.3.2.2.m1.*******.*******.1.2.4.1.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.2.4">subscript</csymbol><ci id="S4.E5Xb.3.2.2.m1.*******.*******.1.2.4.2.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.2.4.2">𝒙</ci><ci id="S4.E5Xb.3.2.2.m1.*******.*******.1.2.4.3.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.2.4.3">𝑖</ci></apply></apply><apply id="S4.E5Xb.3.2.2.m1.*******.*******.1.3.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.3"><times id="S4.E5Xb.3.2.2.m1.*******.*******.1.3.1.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.3.1"></times><apply id="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.cmml" xref="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******"><csymbol cd="ambiguous" id="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.1.cmml" xref="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******">superscript</csymbol><apply id="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.2.cmml" xref="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******"><csymbol cd="ambiguous" id="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.2.1.cmml" xref="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******">subscript</csymbol><ci id="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.2.2.cmml" xref="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.2.2">𝒙</ci><ci id="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.2.3.cmml" xref="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.2.3">𝑖</ci></apply><ci id="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.3.cmml" xref="S4.E5Xb.3.2.2.m1.*******.1.1.1.*******.3">𝑇</ci></apply><ci id="S4.E5Xb.3.2.2.m1.*******.*******.1.3.3.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.3.3">𝑴</ci><apply id="S4.E5Xb.3.2.2.m1.*******.*******.1.3.4.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.3.4"><csymbol cd="ambiguous" id="S4.E5Xb.3.2.2.m1.*******.*******.1.3.4.1.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.3.4">subscript</csymbol><ci id="S4.E5Xb.3.2.2.m1.*******.*******.1.3.4.2.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.3.4.2">𝒙</ci><ci id="S4.E5Xb.3.2.2.m1.*******.*******.1.3.4.3.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******.1.3.4.3">𝑗</ci></apply></apply></apply><apply id="S4.E5Xb.3.2.2.m1.*******.1.1.3.cmml" xref="S4.E5Xb.3.2.2.m1.*******.1.1.3"><csymbol cd="ambiguous" id="S4.E5Xb.3.2.2.m1.*******.*******.cmml" xref="S4.E5Xb.3.2.2.m1.*******.1.1.3">subscript</csymbol><ci id="S4.E5Xb.3.2.2.m1.*******.*******.cmml" xref="S4.E5Xb.3.2.2.m1.*******.*******">𝐾</ci><list id="S4.E5Xb.3.2.2.m1.*******.cmml" xref="S4.E5Xb.3.2.2.m1.*******"><ci id="S4.E5Xb.3.2.2.m1.*******.cmml" xref="S4.E5Xb.3.2.2.m1.*******">𝑖</ci><ci id="S4.E5Xb.3.2.2.m1.*******.cmml" xref="S4.E5Xb.3.2.2.m1.*******">𝑗</ci></list></apply></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.E5Xb.3.2.2.m1.5c">\displaystyle\min_{{\bm{M}}}\sum\limits_{{\bm{x}}_{i},{\bm{x}}_{j}\in\mathcal{%
X}}({\bm{x}}_{i}^{T}{\bm{M}}{\bm{x}}_{i}-{\bm{x}}_{i}^{T}{\bm{M}}{\bm{x}}_{j})%
K_{i,j}\quad,</annotation><annotation encoding="application/x-llamapun" id="S4.E5Xb.3.2.2.m1.5d">roman_min start_POSTSUBSCRIPT bold_italic_M end_POSTSUBSCRIPT ∑ start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT , bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT ∈ caligraphic_X end_POSTSUBSCRIPT ( bold_italic_x start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_T end_POSTSUPERSCRIPT bold_italic_M bold_italic_x start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT - bold_italic_x start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_T end_POSTSUPERSCRIPT bold_italic_M bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT ) italic_K start_POSTSUBSCRIPT italic_i , italic_j end_POSTSUBSCRIPT ,</annotation></semantics></math></td>
<td class="ltx_eqn_cell ltx_eqn_center_padright"></td>
</tr>
</tbody>
</table>
<p class="ltx_p" id="S4.SS1.SSS0.Px2.p1.9">where <math alttext="d" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px2.p1.8.m1.1"><semantics id="S4.SS1.SSS0.Px2.p1.8.m1.1a"><mi id="S4.SS1.SSS0.Px2.p1.8.m1.1.1" xref="S4.SS1.SSS0.Px2.p1.8.m1.1.1.cmml">d</mi><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px2.p1.8.m1.1b"><ci id="S4.SS1.SSS0.Px2.p1.8.m1.1.1.cmml" xref="S4.SS1.SSS0.Px2.p1.8.m1.1.1">𝑑</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px2.p1.8.m1.1c">d</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px2.p1.8.m1.1d">italic_d</annotation></semantics></math> is the feature dimension, and <math alttext="d_{{\bm{M}}}({\bm{x}}_{i},{\bm{x}}_{j})" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px2.p1.9.m2.2"><semantics id="S4.SS1.SSS0.Px2.p1.9.m2.2a"><mrow id="S4.SS1.SSS0.Px2.p1.9.m2.2.2" xref="S4.SS1.SSS0.Px2.p1.9.m2.2.2.cmml"><msub id="S4.SS1.SSS0.Px2.p1.9.m*******" xref="S4.SS1.SSS0.Px2.p1.9.m*******.cmml"><mi id="S4.SS1.SSS0.Px2.p1.9.m*******.2" xref="S4.SS1.SSS0.Px2.p1.9.m*******.2.cmml">d</mi><mi id="S4.SS1.SSS0.Px2.p1.9.m*******.3" xref="S4.SS1.SSS0.Px2.p1.9.m*******.3.cmml">𝑴</mi></msub><mo id="S4.SS1.SSS0.Px2.p1.9.m*******" xref="S4.SS1.SSS0.Px2.p1.9.m*******.cmml">⁢</mo><mrow id="S4.SS1.SSS0.Px2.p1.9.m*******.2" xref="S4.SS1.SSS0.Px2.p1.9.m*******.3.cmml"><mo id="S4.SS1.SSS0.Px2.p1.9.m*******.2.3" stretchy="false" xref="S4.SS1.SSS0.Px2.p1.9.m*******.3.cmml">(</mo><msub id="S4.SS1.SSS0.Px2.p1.9.m2.*******.1" xref="S4.SS1.SSS0.Px2.p1.9.m2.*******.1.cmml"><mi id="S4.SS1.SSS0.Px2.p1.9.m2.*******.1.2" xref="S4.SS1.SSS0.Px2.p1.9.m2.*******.1.2.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px2.p1.9.m2.*******.1.3" xref="S4.SS1.SSS0.Px2.p1.9.m2.*******.1.3.cmml">i</mi></msub><mo id="S4.SS1.SSS0.Px2.p1.9.m*******.2.4" xref="S4.SS1.SSS0.Px2.p1.9.m*******.3.cmml">,</mo><msub id="S4.SS1.SSS0.Px2.p1.9.m*******.2.2" xref="S4.SS1.SSS0.Px2.p1.9.m*******.2.2.cmml"><mi id="S4.SS1.SSS0.Px2.p1.9.m*******.2.2.2" xref="S4.SS1.SSS0.Px2.p1.9.m*******.2.2.2.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px2.p1.9.m*******.2.2.3" xref="S4.SS1.SSS0.Px2.p1.9.m*******.2.2.3.cmml">j</mi></msub><mo id="S4.SS1.SSS0.Px2.p1.9.m*******.2.5" stretchy="false" xref="S4.SS1.SSS0.Px2.p1.9.m*******.3.cmml">)</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px2.p1.9.m2.2b"><apply id="S4.SS1.SSS0.Px2.p1.9.m2.2.2.cmml" xref="S4.SS1.SSS0.Px2.p1.9.m2.2.2"><times id="S4.SS1.SSS0.Px2.p1.9.m*******.cmml" xref="S4.SS1.SSS0.Px2.p1.9.m*******"></times><apply id="S4.SS1.SSS0.Px2.p1.9.m*******.cmml" xref="S4.SS1.SSS0.Px2.p1.9.m*******"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px2.p1.9.m*******.1.cmml" xref="S4.SS1.SSS0.Px2.p1.9.m*******">subscript</csymbol><ci id="S4.SS1.SSS0.Px2.p1.9.m*******.2.cmml" xref="S4.SS1.SSS0.Px2.p1.9.m*******.2">𝑑</ci><ci id="S4.SS1.SSS0.Px2.p1.9.m*******.3.cmml" xref="S4.SS1.SSS0.Px2.p1.9.m*******.3">𝑴</ci></apply><interval closure="open" id="S4.SS1.SSS0.Px2.p1.9.m*******.3.cmml" xref="S4.SS1.SSS0.Px2.p1.9.m*******.2"><apply id="S4.SS1.SSS0.Px2.p1.9.m2.*******.1.cmml" xref="S4.SS1.SSS0.Px2.p1.9.m2.*******.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px2.p1.9.m2.*******.1.1.cmml" xref="S4.SS1.SSS0.Px2.p1.9.m2.*******.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px2.p1.9.m2.*******.1.2.cmml" xref="S4.SS1.SSS0.Px2.p1.9.m2.*******.1.2">𝒙</ci><ci id="S4.SS1.SSS0.Px2.p1.9.m2.*******.1.3.cmml" xref="S4.SS1.SSS0.Px2.p1.9.m2.*******.1.3">𝑖</ci></apply><apply id="S4.SS1.SSS0.Px2.p1.9.m*******.2.2.cmml" xref="S4.SS1.SSS0.Px2.p1.9.m*******.2.2"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px2.p1.9.m2.2.2.*******.cmml" xref="S4.SS1.SSS0.Px2.p1.9.m*******.2.2">subscript</csymbol><ci id="S4.SS1.SSS0.Px2.p1.9.m*******.2.2.2.cmml" xref="S4.SS1.SSS0.Px2.p1.9.m*******.2.2.2">𝒙</ci><ci id="S4.SS1.SSS0.Px2.p1.9.m*******.2.2.3.cmml" xref="S4.SS1.SSS0.Px2.p1.9.m*******.2.2.3">𝑗</ci></apply></interval></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px2.p1.9.m2.2c">d_{{\bm{M}}}({\bm{x}}_{i},{\bm{x}}_{j})</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px2.p1.9.m2.2d">italic_d start_POSTSUBSCRIPT bold_italic_M end_POSTSUBSCRIPT ( bold_italic_x start_POSTSUBSCRIPT italic_i end_POSTSUBSCRIPT , bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT )</annotation></semantics></math> denotes the Mahalanobis distance.
Metric learning module can better leverage the similarity and dissimilarity among samples by learning a new embedding space.
Thus, LARA adopts the <em class="ltx_emph ltx_font_italic" id="S4.SS1.SSS0.Px2.p1.9.1">metric learning module</em> to automatically learn a well-suited distance metric in the embedding space, assisting the <em class="ltx_emph ltx_font_italic" id="S4.SS1.SSS0.Px2.p1.9.2">localization module</em> in extracting potentially profitable samples.</p>
</div>
</section>
<section class="ltx_paragraph" id="S4.SS1.SSS0.Px3">
<h4 class="ltx_title ltx_title_paragraph">LA-Attention Has a Proper Estimation of <math alttext="p_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px3.1.m1.1"><semantics id="S4.SS1.SSS0.Px3.1.m1.1b"><msub id="S4.SS1.SSS0.Px3.1.m1.1.1" xref="S4.SS1.SSS0.Px3.1.m1.1.1.cmml"><mi id="S4.SS1.SSS0.Px3.1.m*******" xref="S4.SS1.SSS0.Px3.1.m*******.cmml">p</mi><msub id="S4.SS1.SSS0.Px3.1.m*******" xref="S4.SS1.SSS0.Px3.1.m*******.cmml"><mi id="S4.SS1.SSS0.Px3.1.m1.*******" xref="S4.SS1.SSS0.Px3.1.m1.*******.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px3.1.m1.*******" xref="S4.SS1.SSS0.Px3.1.m1.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px3.1.m1.1c"><apply id="S4.SS1.SSS0.Px3.1.m1.1.1.cmml" xref="S4.SS1.SSS0.Px3.1.m1.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.1.m*******.cmml" xref="S4.SS1.SSS0.Px3.1.m1.1.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px3.1.m*******.cmml" xref="S4.SS1.SSS0.Px3.1.m*******">𝑝</ci><apply id="S4.SS1.SSS0.Px3.1.m*******.cmml" xref="S4.SS1.SSS0.Px3.1.m*******"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.1.m1.*******.cmml" xref="S4.SS1.SSS0.Px3.1.m*******">subscript</csymbol><ci id="S4.SS1.SSS0.Px3.1.m1.*******.cmml" xref="S4.SS1.SSS0.Px3.1.m1.*******">𝒙</ci><ci id="S4.SS1.SSS0.Px3.1.m1.*******.cmml" xref="S4.SS1.SSS0.Px3.1.m1.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px3.1.m1.1d">p_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px3.1.m1.1e">italic_p start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math>.</h4>
<div class="ltx_para" id="S4.SS1.SSS0.Px3.p1">
<p class="ltx_p" id="S4.SS1.SSS0.Px3.p1.16">We use a running example to illustrate why LA-Attention helps extract potentially profitable samples and further understand the effectiveness of the <em class="ltx_emph ltx_font_italic" id="S4.SS1.SSS0.Px3.p1.16.3">metric learning module</em> incorporated into the LA-Attention method, as shown in Fig. <a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#S4.F3" title="Figure 3 ‣ LA-Attention Has a Proper Estimation of 𝑝_𝒙_𝑡. ‣ 4.1 Locality-Aware Attention (LA-Attention) ‣ 4 LARA: The Proposed Framework ‣ Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling"><span class="ltx_text ltx_ref_tag">3</span></a>.
We search for potentially profitable <em class="ltx_emph ltx_font_italic" id="S4.SS1.SSS0.Px3.p1.1.1">samples with high <math alttext="p_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px3.p1.1.1.m1.1"><semantics id="S4.SS1.SSS0.Px3.p1.1.1.m1.1a"><msub id="S4.SS1.SSS0.Px3.p1.1.1.m1.1.1" xref="S4.SS1.SSS0.Px3.p1.1.1.m1.1.1.cmml"><mi id="S4.SS1.SSS0.Px3.p1.1.1.m*******" xref="S4.SS1.SSS0.Px3.p1.1.1.m*******.cmml">p</mi><msub id="S4.SS1.SSS0.Px3.p1.1.1.m*******" xref="S4.SS1.SSS0.Px3.p1.1.1.m*******.cmml"><mi id="S4.SS1.SSS0.Px3.p1.1.1.m1.*******" xref="S4.SS1.SSS0.Px3.p1.1.1.m1.*******.cmml">𝐱</mi><mi id="S4.SS1.SSS0.Px3.p1.1.1.m1.*******" xref="S4.SS1.SSS0.Px3.p1.1.1.m1.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px3.p1.1.1.m1.1b"><apply id="S4.SS1.SSS0.Px3.p1.1.1.m1.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.1.1.m1.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.1.1.m*******.cmml" xref="S4.SS1.SSS0.Px3.p1.1.1.m1.1.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px3.p1.1.1.m*******.cmml" xref="S4.SS1.SSS0.Px3.p1.1.1.m*******">𝑝</ci><apply id="S4.SS1.SSS0.Px3.p1.1.1.m*******.cmml" xref="S4.SS1.SSS0.Px3.p1.1.1.m*******"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.1.1.m1.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.1.1.m*******">subscript</csymbol><ci id="S4.SS1.SSS0.Px3.p1.1.1.m1.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.1.1.m1.*******">𝐱</ci><ci id="S4.SS1.SSS0.Px3.p1.1.1.m1.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.1.1.m1.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px3.p1.1.1.m1.1c">p_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px3.p1.1.1.m1.1d">italic_p start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math></em> via the <math alttext="\hat{p}_{{\bm{x}}_{t}}&gt;thres" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px3.p1.2.m1.1"><semantics id="S4.SS1.SSS0.Px3.p1.2.m1.1a"><mrow id="S4.SS1.SSS0.Px3.p1.2.m1.1.1" xref="S4.SS1.SSS0.Px3.p1.2.m1.1.1.cmml"><msub id="S4.SS1.SSS0.Px3.p1.2.m*******" xref="S4.SS1.SSS0.Px3.p1.2.m*******.cmml"><mover accent="true" id="S4.SS1.SSS0.Px3.p1.2.m1.*******" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******.cmml"><mi id="S4.SS1.SSS0.Px3.p1.2.m1.*******.2" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******.2.cmml">p</mi><mo id="S4.SS1.SSS0.Px3.p1.2.m1.*******.1" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******.1.cmml">^</mo></mover><msub id="S4.SS1.SSS0.Px3.p1.2.m1.*******" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******.cmml"><mi id="S4.SS1.SSS0.Px3.p1.2.m1.*******.2" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******.2.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px3.p1.2.m1.*******.3" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******.3.cmml">t</mi></msub></msub><mo id="S4.SS1.SSS0.Px3.p1.2.m*******" xref="S4.SS1.SSS0.Px3.p1.2.m*******.cmml">&gt;</mo><mrow id="S4.SS1.SSS0.Px3.p1.2.m*******" xref="S4.SS1.SSS0.Px3.p1.2.m*******.cmml"><mi id="S4.SS1.SSS0.Px3.p1.2.m1.*******" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******.cmml">t</mi><mo id="S4.SS1.SSS0.Px3.p1.2.m1.*******" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.2.m1.*******" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******.cmml">h</mi><mo id="S4.SS1.SSS0.Px3.p1.2.m1.*******a" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.2.m*******.4" xref="S4.SS1.SSS0.Px3.p1.2.m*******.4.cmml">r</mi><mo id="S4.SS1.SSS0.Px3.p1.2.m1.*******b" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.2.m*******.5" xref="S4.SS1.SSS0.Px3.p1.2.m*******.5.cmml">e</mi><mo id="S4.SS1.SSS0.Px3.p1.2.m1.*******c" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.2.m*******.6" xref="S4.SS1.SSS0.Px3.p1.2.m*******.6.cmml">s</mi></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px3.p1.2.m1.1b"><apply id="S4.SS1.SSS0.Px3.p1.2.m1.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.2.m1.1.1"><gt id="S4.SS1.SSS0.Px3.p1.2.m*******.cmml" xref="S4.SS1.SSS0.Px3.p1.2.m*******"></gt><apply id="S4.SS1.SSS0.Px3.p1.2.m*******.cmml" xref="S4.SS1.SSS0.Px3.p1.2.m*******"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.2.m1.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.2.m*******">subscript</csymbol><apply id="S4.SS1.SSS0.Px3.p1.2.m1.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******"><ci id="S4.SS1.SSS0.Px3.p1.2.m1.*******.1.cmml" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******.1">^</ci><ci id="S4.SS1.SSS0.Px3.p1.2.m1.*******.2.cmml" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******.2">𝑝</ci></apply><apply id="S4.SS1.SSS0.Px3.p1.2.m1.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.2.m1.*******.1.cmml" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******">subscript</csymbol><ci id="S4.SS1.SSS0.Px3.p1.2.m1.*******.2.cmml" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******.2">𝒙</ci><ci id="S4.SS1.SSS0.Px3.p1.2.m1.*******.3.cmml" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******.3">𝑡</ci></apply></apply><apply id="S4.SS1.SSS0.Px3.p1.2.m*******.cmml" xref="S4.SS1.SSS0.Px3.p1.2.m*******"><times id="S4.SS1.SSS0.Px3.p1.2.m1.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******"></times><ci id="S4.SS1.SSS0.Px3.p1.2.m1.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******">𝑡</ci><ci id="S4.SS1.SSS0.Px3.p1.2.m1.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.2.m1.*******">ℎ</ci><ci id="S4.SS1.SSS0.Px3.p1.2.m*******.4.cmml" xref="S4.SS1.SSS0.Px3.p1.2.m*******.4">𝑟</ci><ci id="S4.SS1.SSS0.Px3.p1.2.m*******.5.cmml" xref="S4.SS1.SSS0.Px3.p1.2.m*******.5">𝑒</ci><ci id="S4.SS1.SSS0.Px3.p1.2.m*******.6.cmml" xref="S4.SS1.SSS0.Px3.p1.2.m*******.6">𝑠</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px3.p1.2.m1.1c">\hat{p}_{{\bm{x}}_{t}}&gt;thres</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px3.p1.2.m1.1d">over^ start_ARG italic_p end_ARG start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT &gt; italic_t italic_h italic_r italic_e italic_s</annotation></semantics></math> criterion (<math alttext="thres" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px3.p1.3.m2.1"><semantics id="S4.SS1.SSS0.Px3.p1.3.m2.1a"><mrow id="S4.SS1.SSS0.Px3.p1.3.m2.1.1" xref="S4.SS1.SSS0.Px3.p1.3.m2.1.1.cmml"><mi id="S4.SS1.SSS0.Px3.p1.3.m2.1.1.2" xref="S4.SS1.SSS0.Px3.p1.3.m2.1.1.2.cmml">t</mi><mo id="S4.SS1.SSS0.Px3.p1.3.m2.1.1.1" xref="S4.SS1.SSS0.Px3.p1.3.m2.1.1.1.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.3.m2.1.1.3" xref="S4.SS1.SSS0.Px3.p1.3.m2.1.1.3.cmml">h</mi><mo id="S4.SS1.SSS0.Px3.p1.3.m2.1.1.1a" xref="S4.SS1.SSS0.Px3.p1.3.m2.1.1.1.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.3.m2.1.1.4" xref="S4.SS1.SSS0.Px3.p1.3.m2.1.1.4.cmml">r</mi><mo id="S4.SS1.SSS0.Px3.p1.3.m2.1.1.1b" xref="S4.SS1.SSS0.Px3.p1.3.m2.1.1.1.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.3.m2.1.1.5" xref="S4.SS1.SSS0.Px3.p1.3.m2.1.1.5.cmml">e</mi><mo id="S4.SS1.SSS0.Px3.p1.3.m2.1.1.1c" xref="S4.SS1.SSS0.Px3.p1.3.m2.1.1.1.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.3.m2.1.1.6" xref="S4.SS1.SSS0.Px3.p1.3.m2.1.1.6.cmml">s</mi></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px3.p1.3.m2.1b"><apply id="S4.SS1.SSS0.Px3.p1.3.m2.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.3.m2.1.1"><times id="S4.SS1.SSS0.Px3.p1.3.m2.1.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.3.m2.1.1.1"></times><ci id="S4.SS1.SSS0.Px3.p1.3.m2.1.1.2.cmml" xref="S4.SS1.SSS0.Px3.p1.3.m2.1.1.2">𝑡</ci><ci id="S4.SS1.SSS0.Px3.p1.3.m2.1.1.3.cmml" xref="S4.SS1.SSS0.Px3.p1.3.m2.1.1.3">ℎ</ci><ci id="S4.SS1.SSS0.Px3.p1.3.m2.1.1.4.cmml" xref="S4.SS1.SSS0.Px3.p1.3.m2.1.1.4">𝑟</ci><ci id="S4.SS1.SSS0.Px3.p1.3.m2.1.1.5.cmml" xref="S4.SS1.SSS0.Px3.p1.3.m2.1.1.5">𝑒</ci><ci id="S4.SS1.SSS0.Px3.p1.3.m2.1.1.6.cmml" xref="S4.SS1.SSS0.Px3.p1.3.m2.1.1.6">𝑠</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px3.p1.3.m2.1c">thres</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px3.p1.3.m2.1d">italic_t italic_h italic_r italic_e italic_s</annotation></semantics></math> equals 0.5 in this demo).
We aim to verify whether <math alttext="\hat{p}_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px3.p1.4.m3.1"><semantics id="S4.SS1.SSS0.Px3.p1.4.m3.1a"><msub id="S4.SS1.SSS0.Px3.p1.4.m3.1.1" xref="S4.SS1.SSS0.Px3.p1.4.m3.1.1.cmml"><mover accent="true" id="S4.SS1.SSS0.Px3.p1.4.m3.1.1.2" xref="S4.SS1.SSS0.Px3.p1.4.m3.1.1.2.cmml"><mi id="S4.SS1.SSS0.Px3.p1.4.m3.*******" xref="S4.SS1.SSS0.Px3.p1.4.m3.*******.cmml">p</mi><mo id="S4.SS1.SSS0.Px3.p1.4.m3.*******" xref="S4.SS1.SSS0.Px3.p1.4.m3.*******.cmml">^</mo></mover><msub id="S4.SS1.SSS0.Px3.p1.4.m3.1.1.3" xref="S4.SS1.SSS0.Px3.p1.4.m3.1.1.3.cmml"><mi id="S4.SS1.SSS0.Px3.p1.4.m3.*******" xref="S4.SS1.SSS0.Px3.p1.4.m3.*******.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px3.p1.4.m3.*******" xref="S4.SS1.SSS0.Px3.p1.4.m3.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px3.p1.4.m3.1b"><apply id="S4.SS1.SSS0.Px3.p1.4.m3.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.4.m3.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.4.m3.1.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.4.m3.1.1">subscript</csymbol><apply id="S4.SS1.SSS0.Px3.p1.4.m3.1.1.2.cmml" xref="S4.SS1.SSS0.Px3.p1.4.m3.1.1.2"><ci id="S4.SS1.SSS0.Px3.p1.4.m3.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.4.m3.*******">^</ci><ci id="S4.SS1.SSS0.Px3.p1.4.m3.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.4.m3.*******">𝑝</ci></apply><apply id="S4.SS1.SSS0.Px3.p1.4.m3.1.1.3.cmml" xref="S4.SS1.SSS0.Px3.p1.4.m3.1.1.3"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.4.m3.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.4.m3.1.1.3">subscript</csymbol><ci id="S4.SS1.SSS0.Px3.p1.4.m3.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.4.m3.*******">𝒙</ci><ci id="S4.SS1.SSS0.Px3.p1.4.m3.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.4.m3.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px3.p1.4.m3.1c">\hat{p}_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px3.p1.4.m3.1d">over^ start_ARG italic_p end_ARG start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math> calculated by LA-Attention is a proper estimation of the predefined <math alttext="p_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px3.p1.5.m4.1"><semantics id="S4.SS1.SSS0.Px3.p1.5.m4.1a"><msub id="S4.SS1.SSS0.Px3.p1.5.m4.1.1" xref="S4.SS1.SSS0.Px3.p1.5.m4.1.1.cmml"><mi id="S4.SS1.SSS0.Px3.p1.5.m4.1.1.2" xref="S4.SS1.SSS0.Px3.p1.5.m4.1.1.2.cmml">p</mi><msub id="S4.SS1.SSS0.Px3.p1.5.m4.1.1.3" xref="S4.SS1.SSS0.Px3.p1.5.m4.1.1.3.cmml"><mi id="S4.SS1.SSS0.Px3.p1.5.m4.*******" xref="S4.SS1.SSS0.Px3.p1.5.m4.*******.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px3.p1.5.m4.*******" xref="S4.SS1.SSS0.Px3.p1.5.m4.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px3.p1.5.m4.1b"><apply id="S4.SS1.SSS0.Px3.p1.5.m4.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.5.m4.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.5.m4.1.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.5.m4.1.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px3.p1.5.m4.1.1.2.cmml" xref="S4.SS1.SSS0.Px3.p1.5.m4.1.1.2">𝑝</ci><apply id="S4.SS1.SSS0.Px3.p1.5.m4.1.1.3.cmml" xref="S4.SS1.SSS0.Px3.p1.5.m4.1.1.3"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.5.m4.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.5.m4.1.1.3">subscript</csymbol><ci id="S4.SS1.SSS0.Px3.p1.5.m4.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.5.m4.*******">𝒙</ci><ci id="S4.SS1.SSS0.Px3.p1.5.m4.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.5.m4.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px3.p1.5.m4.1c">p_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px3.p1.5.m4.1d">italic_p start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math>.
In Fig. <a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#S4.F3" title="Figure 3 ‣ LA-Attention Has a Proper Estimation of 𝑝_𝒙_𝑡. ‣ 4.1 Locality-Aware Attention (LA-Attention) ‣ 4 LARA: The Proposed Framework ‣ Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling"><span class="ltx_text ltx_ref_tag">3</span></a> (Left), We plot embeddings of randomly sampled 1000 points in the ETF dataset using t-SNE <cite class="ltx_cite ltx_citemacro_cite">Van der Maaten and Hinton (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib26" title="">2008</a>)</cite>.
We find that the positive and negative points are mixed together and there are only 174 positive samples.
It exhibits the return distribution with almost the zero mean, and <math alttext="\hat{p}_{{\bm{x}}_{t}}\approx 0.174" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px3.p1.6.m5.1"><semantics id="S4.SS1.SSS0.Px3.p1.6.m5.1a"><mrow id="S4.SS1.SSS0.Px3.p1.6.m5.1.1" xref="S4.SS1.SSS0.Px3.p1.6.m5.1.1.cmml"><msub id="S4.SS1.SSS0.Px3.p1.6.m5.1.1.2" xref="S4.SS1.SSS0.Px3.p1.6.m5.1.1.2.cmml"><mover accent="true" id="S4.SS1.SSS0.Px3.p1.6.m5.*******" xref="S4.SS1.SSS0.Px3.p1.6.m5.*******.cmml"><mi id="S4.SS1.SSS0.Px3.p1.6.m5.*******.2" xref="S4.SS1.SSS0.Px3.p1.6.m5.*******.2.cmml">p</mi><mo id="S4.SS1.SSS0.Px3.p1.6.m5.*******.1" xref="S4.SS1.SSS0.Px3.p1.6.m5.*******.1.cmml">^</mo></mover><msub id="S4.SS1.SSS0.Px3.p1.6.m5.*******" xref="S4.SS1.SSS0.Px3.p1.6.m5.*******.cmml"><mi id="S4.SS1.SSS0.Px3.p1.6.m5.*******.2" xref="S4.SS1.SSS0.Px3.p1.6.m5.*******.2.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px3.p1.6.m5.*******.3" xref="S4.SS1.SSS0.Px3.p1.6.m5.*******.3.cmml">t</mi></msub></msub><mo id="S4.SS1.SSS0.Px3.p1.6.m5.1.1.1" xref="S4.SS1.SSS0.Px3.p1.6.m5.1.1.1.cmml">≈</mo><mn id="S4.SS1.SSS0.Px3.p1.6.m5.1.1.3" xref="S4.SS1.SSS0.Px3.p1.6.m5.1.1.3.cmml">0.174</mn></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px3.p1.6.m5.1b"><apply id="S4.SS1.SSS0.Px3.p1.6.m5.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.6.m5.1.1"><approx id="S4.SS1.SSS0.Px3.p1.6.m5.1.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.6.m5.1.1.1"></approx><apply id="S4.SS1.SSS0.Px3.p1.6.m5.1.1.2.cmml" xref="S4.SS1.SSS0.Px3.p1.6.m5.1.1.2"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.6.m5.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.6.m5.1.1.2">subscript</csymbol><apply id="S4.SS1.SSS0.Px3.p1.6.m5.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.6.m5.*******"><ci id="S4.SS1.SSS0.Px3.p1.6.m5.*******.1.cmml" xref="S4.SS1.SSS0.Px3.p1.6.m5.*******.1">^</ci><ci id="S4.SS1.SSS0.Px3.p1.6.m5.*******.2.cmml" xref="S4.SS1.SSS0.Px3.p1.6.m5.*******.2">𝑝</ci></apply><apply id="S4.SS1.SSS0.Px3.p1.6.m5.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.6.m5.*******"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.6.m5.*******.1.cmml" xref="S4.SS1.SSS0.Px3.p1.6.m5.*******">subscript</csymbol><ci id="S4.SS1.SSS0.Px3.p1.6.m5.*******.2.cmml" xref="S4.SS1.SSS0.Px3.p1.6.m5.*******.2">𝒙</ci><ci id="S4.SS1.SSS0.Px3.p1.6.m5.*******.3.cmml" xref="S4.SS1.SSS0.Px3.p1.6.m5.*******.3">𝑡</ci></apply></apply><cn id="S4.SS1.SSS0.Px3.p1.6.m5.1.1.3.cmml" type="float" xref="S4.SS1.SSS0.Px3.p1.6.m5.1.1.3">0.174</cn></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px3.p1.6.m5.1c">\hat{p}_{{\bm{x}}_{t}}\approx 0.174</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px3.p1.6.m5.1d">over^ start_ARG italic_p end_ARG start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT ≈ 0.174</annotation></semantics></math> is far away from the desired <math alttext="p_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px3.p1.7.m6.1"><semantics id="S4.SS1.SSS0.Px3.p1.7.m6.1a"><msub id="S4.SS1.SSS0.Px3.p1.7.m6.1.1" xref="S4.SS1.SSS0.Px3.p1.7.m6.1.1.cmml"><mi id="S4.SS1.SSS0.Px3.p1.7.m6.1.1.2" xref="S4.SS1.SSS0.Px3.p1.7.m6.1.1.2.cmml">p</mi><msub id="S4.SS1.SSS0.Px3.p1.7.m6.1.1.3" xref="S4.SS1.SSS0.Px3.p1.7.m6.1.1.3.cmml"><mi id="S4.SS1.SSS0.Px3.p1.7.m6.*******" xref="S4.SS1.SSS0.Px3.p1.7.m6.*******.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px3.p1.7.m6.*******" xref="S4.SS1.SSS0.Px3.p1.7.m6.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px3.p1.7.m6.1b"><apply id="S4.SS1.SSS0.Px3.p1.7.m6.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.7.m6.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.7.m6.1.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.7.m6.1.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px3.p1.7.m6.1.1.2.cmml" xref="S4.SS1.SSS0.Px3.p1.7.m6.1.1.2">𝑝</ci><apply id="S4.SS1.SSS0.Px3.p1.7.m6.1.1.3.cmml" xref="S4.SS1.SSS0.Px3.p1.7.m6.1.1.3"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.7.m6.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.7.m6.1.1.3">subscript</csymbol><ci id="S4.SS1.SSS0.Px3.p1.7.m6.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.7.m6.*******">𝒙</ci><ci id="S4.SS1.SSS0.Px3.p1.7.m6.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.7.m6.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px3.p1.7.m6.1c">p_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px3.p1.7.m6.1d">italic_p start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math>.
After introducing the <em class="ltx_emph ltx_font_italic" id="S4.SS1.SSS0.Px3.p1.16.4">localization module</em> (Fig. <a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#S4.F3" title="Figure 3 ‣ LA-Attention Has a Proper Estimation of 𝑝_𝒙_𝑡. ‣ 4.1 Locality-Aware Attention (LA-Attention) ‣ 4 LARA: The Proposed Framework ‣ Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling"><span class="ltx_text ltx_ref_tag">3</span></a> (Middle)), we also randomly sample 1000 points with <math alttext="\hat{p}_{{\bm{x}}_{t}}&gt;thres" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px3.p1.8.m7.1"><semantics id="S4.SS1.SSS0.Px3.p1.8.m7.1a"><mrow id="S4.SS1.SSS0.Px3.p1.8.m7.1.1" xref="S4.SS1.SSS0.Px3.p1.8.m7.1.1.cmml"><msub id="S4.SS1.SSS0.Px3.p1.8.m7.1.1.2" xref="S4.SS1.SSS0.Px3.p1.8.m7.1.1.2.cmml"><mover accent="true" id="S4.SS1.SSS0.Px3.p1.8.m7.*******" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml"><mi id="S4.SS1.SSS0.Px3.p1.8.m7.*******.2" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******.2.cmml">p</mi><mo id="S4.SS1.SSS0.Px3.p1.8.m7.*******.1" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******.1.cmml">^</mo></mover><msub id="S4.SS1.SSS0.Px3.p1.8.m7.*******" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml"><mi id="S4.SS1.SSS0.Px3.p1.8.m7.*******.2" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******.2.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px3.p1.8.m7.*******.3" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******.3.cmml">t</mi></msub></msub><mo id="S4.SS1.SSS0.Px3.p1.8.m7.1.1.1" xref="S4.SS1.SSS0.Px3.p1.8.m7.1.1.1.cmml">&gt;</mo><mrow id="S4.SS1.SSS0.Px3.p1.8.m7.1.1.3" xref="S4.SS1.SSS0.Px3.p1.8.m7.1.1.3.cmml"><mi id="S4.SS1.SSS0.Px3.p1.8.m7.*******" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml">t</mi><mo id="S4.SS1.SSS0.Px3.p1.8.m7.*******" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.8.m7.*******" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml">h</mi><mo id="S4.SS1.SSS0.Px3.p1.8.m7.*******a" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.8.m7.*******" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml">r</mi><mo id="S4.SS1.SSS0.Px3.p1.8.m7.*******b" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.8.m7.*******" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml">e</mi><mo id="S4.SS1.SSS0.Px3.p1.8.m7.*******c" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.8.m7.*******" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml">s</mi></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px3.p1.8.m7.1b"><apply id="S4.SS1.SSS0.Px3.p1.8.m7.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.8.m7.1.1"><gt id="S4.SS1.SSS0.Px3.p1.8.m7.1.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.8.m7.1.1.1"></gt><apply id="S4.SS1.SSS0.Px3.p1.8.m7.1.1.2.cmml" xref="S4.SS1.SSS0.Px3.p1.8.m7.1.1.2"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.8.m7.1.1.2">subscript</csymbol><apply id="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******"><ci id="S4.SS1.SSS0.Px3.p1.8.m7.*******.1.cmml" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******.1">^</ci><ci id="S4.SS1.SSS0.Px3.p1.8.m7.*******.2.cmml" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******.2">𝑝</ci></apply><apply id="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.8.m7.*******.1.cmml" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******">subscript</csymbol><ci id="S4.SS1.SSS0.Px3.p1.8.m7.*******.2.cmml" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******.2">𝒙</ci><ci id="S4.SS1.SSS0.Px3.p1.8.m7.*******.3.cmml" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******.3">𝑡</ci></apply></apply><apply id="S4.SS1.SSS0.Px3.p1.8.m7.1.1.3.cmml" xref="S4.SS1.SSS0.Px3.p1.8.m7.1.1.3"><times id="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******"></times><ci id="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******">𝑡</ci><ci id="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******">ℎ</ci><ci id="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******">𝑟</ci><ci id="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******">𝑒</ci><ci id="S4.SS1.SSS0.Px3.p1.8.m7.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.8.m7.*******">𝑠</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px3.p1.8.m7.1c">\hat{p}_{{\bm{x}}_{t}}&gt;thres</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px3.p1.8.m7.1d">over^ start_ARG italic_p end_ARG start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT &gt; italic_t italic_h italic_r italic_e italic_s</annotation></semantics></math> criteria, and find that there are 279 positive samples. The mean return of potentially profitable samples is obviously larger than zero, which indicates the effectiveness of the <em class="ltx_emph ltx_font_italic" id="S4.SS1.SSS0.Px3.p1.16.5">localization module</em>.
However, the negative ones still account for a large part of the samples and the estimated <math alttext="\hat{p}_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px3.p1.9.m8.1"><semantics id="S4.SS1.SSS0.Px3.p1.9.m8.1a"><msub id="S4.SS1.SSS0.Px3.p1.9.m8.1.1" xref="S4.SS1.SSS0.Px3.p1.9.m8.1.1.cmml"><mover accent="true" id="S4.SS1.SSS0.Px3.p1.9.m8.1.1.2" xref="S4.SS1.SSS0.Px3.p1.9.m8.1.1.2.cmml"><mi id="S4.SS1.SSS0.Px3.p1.9.m8.*******" xref="S4.SS1.SSS0.Px3.p1.9.m8.*******.cmml">p</mi><mo id="S4.SS1.SSS0.Px3.p1.9.m8.*******" xref="S4.SS1.SSS0.Px3.p1.9.m8.*******.cmml">^</mo></mover><msub id="S4.SS1.SSS0.Px3.p1.9.m8.1.1.3" xref="S4.SS1.SSS0.Px3.p1.9.m8.1.1.3.cmml"><mi id="S4.SS1.SSS0.Px3.p1.9.m8.*******" xref="S4.SS1.SSS0.Px3.p1.9.m8.*******.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px3.p1.9.m8.*******" xref="S4.SS1.SSS0.Px3.p1.9.m8.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px3.p1.9.m8.1b"><apply id="S4.SS1.SSS0.Px3.p1.9.m8.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.9.m8.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.9.m8.1.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.9.m8.1.1">subscript</csymbol><apply id="S4.SS1.SSS0.Px3.p1.9.m8.1.1.2.cmml" xref="S4.SS1.SSS0.Px3.p1.9.m8.1.1.2"><ci id="S4.SS1.SSS0.Px3.p1.9.m8.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.9.m8.*******">^</ci><ci id="S4.SS1.SSS0.Px3.p1.9.m8.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.9.m8.*******">𝑝</ci></apply><apply id="S4.SS1.SSS0.Px3.p1.9.m8.1.1.3.cmml" xref="S4.SS1.SSS0.Px3.p1.9.m8.1.1.3"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.9.m8.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.9.m8.1.1.3">subscript</csymbol><ci id="S4.SS1.SSS0.Px3.p1.9.m8.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.9.m8.*******">𝒙</ci><ci id="S4.SS1.SSS0.Px3.p1.9.m8.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.9.m8.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px3.p1.9.m8.1c">\hat{p}_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px3.p1.9.m8.1d">over^ start_ARG italic_p end_ARG start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math> (0.279) is much lower than desired <math alttext="thresh=0.5" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px3.p1.10.m9.1"><semantics id="S4.SS1.SSS0.Px3.p1.10.m9.1a"><mrow id="S4.SS1.SSS0.Px3.p1.10.m9.1.1" xref="S4.SS1.SSS0.Px3.p1.10.m9.1.1.cmml"><mrow id="S4.SS1.SSS0.Px3.p1.10.m9.1.1.2" xref="S4.SS1.SSS0.Px3.p1.10.m9.1.1.2.cmml"><mi id="S4.SS1.SSS0.Px3.p1.10.m9.*******" xref="S4.SS1.SSS0.Px3.p1.10.m9.*******.cmml">t</mi><mo id="S4.SS1.SSS0.Px3.p1.10.m9.*******" xref="S4.SS1.SSS0.Px3.p1.10.m9.*******.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.10.m9.*******" xref="S4.SS1.SSS0.Px3.p1.10.m9.*******.cmml">h</mi><mo id="S4.SS1.SSS0.Px3.p1.10.m9.*******a" xref="S4.SS1.SSS0.Px3.p1.10.m9.*******.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.10.m9.*******" xref="S4.SS1.SSS0.Px3.p1.10.m9.*******.cmml">r</mi><mo id="S4.SS1.SSS0.Px3.p1.10.m9.*******b" xref="S4.SS1.SSS0.Px3.p1.10.m9.*******.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.10.m9.*******" xref="S4.SS1.SSS0.Px3.p1.10.m9.*******.cmml">e</mi><mo id="S4.SS1.SSS0.Px3.p1.10.m9.*******c" xref="S4.SS1.SSS0.Px3.p1.10.m9.*******.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.10.m9.1.1.2.6" xref="S4.SS1.SSS0.Px3.p1.10.m9.1.1.2.6.cmml">s</mi><mo id="S4.SS1.SSS0.Px3.p1.10.m9.*******d" xref="S4.SS1.SSS0.Px3.p1.10.m9.*******.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.10.m9.1.1.2.7" xref="S4.SS1.SSS0.Px3.p1.10.m9.1.1.2.7.cmml">h</mi></mrow><mo id="S4.SS1.SSS0.Px3.p1.10.m9.1.1.1" xref="S4.SS1.SSS0.Px3.p1.10.m9.1.1.1.cmml">=</mo><mn id="S4.SS1.SSS0.Px3.p1.10.m9.1.1.3" xref="S4.SS1.SSS0.Px3.p1.10.m9.1.1.3.cmml">0.5</mn></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px3.p1.10.m9.1b"><apply id="S4.SS1.SSS0.Px3.p1.10.m9.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.10.m9.1.1"><eq id="S4.SS1.SSS0.Px3.p1.10.m9.1.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.10.m9.1.1.1"></eq><apply id="S4.SS1.SSS0.Px3.p1.10.m9.1.1.2.cmml" xref="S4.SS1.SSS0.Px3.p1.10.m9.1.1.2"><times id="S4.SS1.SSS0.Px3.p1.10.m9.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.10.m9.*******"></times><ci id="S4.SS1.SSS0.Px3.p1.10.m9.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.10.m9.*******">𝑡</ci><ci id="S4.SS1.SSS0.Px3.p1.10.m9.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.10.m9.*******">ℎ</ci><ci id="S4.SS1.SSS0.Px3.p1.10.m9.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.10.m9.*******">𝑟</ci><ci id="S4.SS1.SSS0.Px3.p1.10.m9.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.10.m9.*******">𝑒</ci><ci id="S4.SS1.SSS0.Px3.p1.10.m9.1.1.2.6.cmml" xref="S4.SS1.SSS0.Px3.p1.10.m9.1.1.2.6">𝑠</ci><ci id="S4.SS1.SSS0.Px3.p1.10.m9.1.1.2.7.cmml" xref="S4.SS1.SSS0.Px3.p1.10.m9.1.1.2.7">ℎ</ci></apply><cn id="S4.SS1.SSS0.Px3.p1.10.m9.1.1.3.cmml" type="float" xref="S4.SS1.SSS0.Px3.p1.10.m9.1.1.3">0.5</cn></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px3.p1.10.m9.1c">thresh=0.5</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px3.p1.10.m9.1d">italic_t italic_h italic_r italic_e italic_s italic_h = 0.5</annotation></semantics></math>.
In Fig. <a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#S4.F3" title="Figure 3 ‣ LA-Attention Has a Proper Estimation of 𝑝_𝒙_𝑡. ‣ 4.1 Locality-Aware Attention (LA-Attention) ‣ 4 LARA: The Proposed Framework ‣ Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling"><span class="ltx_text ltx_ref_tag">3</span></a> (Right), further incorporated into the <em class="ltx_emph ltx_font_italic" id="S4.SS1.SSS0.Px3.p1.16.6">metric learning module</em>, the number of the positive samples (424) accounts for almost half of all samples, which is in line with expectations that <math alttext="\hat{p}_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px3.p1.11.m10.1"><semantics id="S4.SS1.SSS0.Px3.p1.11.m10.1a"><msub id="S4.SS1.SSS0.Px3.p1.11.m10.1.1" xref="S4.SS1.SSS0.Px3.p1.11.m10.1.1.cmml"><mover accent="true" id="S4.SS1.SSS0.Px3.p1.11.m10.1.1.2" xref="S4.SS1.SSS0.Px3.p1.11.m10.1.1.2.cmml"><mi id="S4.SS1.SSS0.Px3.p1.11.m10.*******" xref="S4.SS1.SSS0.Px3.p1.11.m10.*******.cmml">p</mi><mo id="S4.SS1.SSS0.Px3.p1.11.m10.*******" xref="S4.SS1.SSS0.Px3.p1.11.m10.*******.cmml">^</mo></mover><msub id="S4.SS1.SSS0.Px3.p1.11.m10.1.1.3" xref="S4.SS1.SSS0.Px3.p1.11.m10.1.1.3.cmml"><mi id="S4.SS1.SSS0.Px3.p1.11.m10.*******" xref="S4.SS1.SSS0.Px3.p1.11.m10.*******.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px3.p1.11.m10.*******" xref="S4.SS1.SSS0.Px3.p1.11.m10.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px3.p1.11.m10.1b"><apply id="S4.SS1.SSS0.Px3.p1.11.m10.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.11.m10.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.11.m10.1.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.11.m10.1.1">subscript</csymbol><apply id="S4.SS1.SSS0.Px3.p1.11.m10.1.1.2.cmml" xref="S4.SS1.SSS0.Px3.p1.11.m10.1.1.2"><ci id="S4.SS1.SSS0.Px3.p1.11.m10.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.11.m10.*******">^</ci><ci id="S4.SS1.SSS0.Px3.p1.11.m10.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.11.m10.*******">𝑝</ci></apply><apply id="S4.SS1.SSS0.Px3.p1.11.m10.1.1.3.cmml" xref="S4.SS1.SSS0.Px3.p1.11.m10.1.1.3"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.11.m10.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.11.m10.1.1.3">subscript</csymbol><ci id="S4.SS1.SSS0.Px3.p1.11.m10.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.11.m10.*******">𝒙</ci><ci id="S4.SS1.SSS0.Px3.p1.11.m10.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.11.m10.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px3.p1.11.m10.1c">\hat{p}_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px3.p1.11.m10.1d">over^ start_ARG italic_p end_ARG start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math> equals 0.5 and is a proper estimation of <math alttext="p_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px3.p1.12.m11.1"><semantics id="S4.SS1.SSS0.Px3.p1.12.m11.1a"><msub id="S4.SS1.SSS0.Px3.p1.12.m11.1.1" xref="S4.SS1.SSS0.Px3.p1.12.m11.1.1.cmml"><mi id="S4.SS1.SSS0.Px3.p1.12.m1*******" xref="S4.SS1.SSS0.Px3.p1.12.m1*******.cmml">p</mi><msub id="S4.SS1.SSS0.Px3.p1.12.m1*******" xref="S4.SS1.SSS0.Px3.p1.12.m1*******.cmml"><mi id="S4.SS1.SSS0.Px3.p1.12.m11.*******" xref="S4.SS1.SSS0.Px3.p1.12.m11.*******.cmml">𝒙</mi><mi id="S4.SS1.SSS0.Px3.p1.12.m11.*******" xref="S4.SS1.SSS0.Px3.p1.12.m11.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px3.p1.12.m11.1b"><apply id="S4.SS1.SSS0.Px3.p1.12.m11.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.12.m11.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.12.m1*******.cmml" xref="S4.SS1.SSS0.Px3.p1.12.m11.1.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px3.p1.12.m1*******.cmml" xref="S4.SS1.SSS0.Px3.p1.12.m1*******">𝑝</ci><apply id="S4.SS1.SSS0.Px3.p1.12.m1*******.cmml" xref="S4.SS1.SSS0.Px3.p1.12.m1*******"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.12.m11.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.12.m1*******">subscript</csymbol><ci id="S4.SS1.SSS0.Px3.p1.12.m11.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.12.m11.*******">𝒙</ci><ci id="S4.SS1.SSS0.Px3.p1.12.m11.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.12.m11.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px3.p1.12.m11.1c">p_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px3.p1.12.m11.1d">italic_p start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math>.
We also find that the learned embedding has a more compact structure and clearer boundaries.
It achieves the <math alttext="8.42E\text{-}4" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px3.p1.13.m12.1"><semantics id="S4.SS1.SSS0.Px3.p1.13.m12.1a"><mrow id="S4.SS1.SSS0.Px3.p1.13.m12.1.1" xref="S4.SS1.SSS0.Px3.p1.13.m12.1.1.cmml"><mn id="S4.SS1.SSS0.Px3.p1.13.m12.1.1.2" xref="S4.SS1.SSS0.Px3.p1.13.m12.1.1.2.cmml">8.42</mn><mo id="S4.SS1.SSS0.Px3.p1.13.m12.1.1.1" xref="S4.SS1.SSS0.Px3.p1.13.m12.1.1.1.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.13.m12.1.1.3" xref="S4.SS1.SSS0.Px3.p1.13.m12.1.1.3.cmml">E</mi><mo id="S4.SS1.SSS0.Px3.p1.13.m12.1.1.1a" xref="S4.SS1.SSS0.Px3.p1.13.m12.1.1.1.cmml">⁢</mo><mtext id="S4.SS1.SSS0.Px3.p1.13.m12.1.1.4" xref="S4.SS1.SSS0.Px3.p1.13.m12.1.1.4a.cmml">-</mtext><mo id="S4.SS1.SSS0.Px3.p1.13.m12.1.1.1b" xref="S4.SS1.SSS0.Px3.p1.13.m12.1.1.1.cmml">⁢</mo><mn id="S4.SS1.SSS0.Px3.p1.13.m12.1.1.5" xref="S4.SS1.SSS0.Px3.p1.13.m12.1.1.5.cmml">4</mn></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px3.p1.13.m12.1b"><apply id="S4.SS1.SSS0.Px3.p1.13.m12.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.13.m12.1.1"><times id="S4.SS1.SSS0.Px3.p1.13.m12.1.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.13.m12.1.1.1"></times><cn id="S4.SS1.SSS0.Px3.p1.13.m12.1.1.2.cmml" type="float" xref="S4.SS1.SSS0.Px3.p1.13.m12.1.1.2">8.42</cn><ci id="S4.SS1.SSS0.Px3.p1.13.m12.1.1.3.cmml" xref="S4.SS1.SSS0.Px3.p1.13.m12.1.1.3">𝐸</ci><ci id="S4.SS1.SSS0.Px3.p1.13.m12.1.1.4a.cmml" xref="S4.SS1.SSS0.Px3.p1.13.m12.1.1.4"><mtext id="S4.SS1.SSS0.Px3.p1.13.m12.1.1.4.cmml" xref="S4.SS1.SSS0.Px3.p1.13.m12.1.1.4">-</mtext></ci><cn id="S4.SS1.SSS0.Px3.p1.13.m12.1.1.5.cmml" type="integer" xref="S4.SS1.SSS0.Px3.p1.13.m12.1.1.5">4</cn></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px3.p1.13.m12.1c">8.42E\text{-}4</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px3.p1.13.m12.1d">8.42 italic_E - 4</annotation></semantics></math> mean return, which is much larger than zero with a greater margin compared with the other two methods (note that <math alttext="8.42E\text{-}4" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px3.p1.14.m13.1"><semantics id="S4.SS1.SSS0.Px3.p1.14.m13.1a"><mrow id="S4.SS1.SSS0.Px3.p1.14.m13.1.1" xref="S4.SS1.SSS0.Px3.p1.14.m13.1.1.cmml"><mn id="S4.SS1.SSS0.Px3.p1.14.m13.1.1.2" xref="S4.SS1.SSS0.Px3.p1.14.m13.1.1.2.cmml">8.42</mn><mo id="S4.SS1.SSS0.Px3.p1.14.m13.1.1.1" xref="S4.SS1.SSS0.Px3.p1.14.m13.1.1.1.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.14.m13.1.1.3" xref="S4.SS1.SSS0.Px3.p1.14.m13.1.1.3.cmml">E</mi><mo id="S4.SS1.SSS0.Px3.p1.14.m13.1.1.1a" xref="S4.SS1.SSS0.Px3.p1.14.m13.1.1.1.cmml">⁢</mo><mtext id="S4.SS1.SSS0.Px3.p1.14.m13.1.1.4" xref="S4.SS1.SSS0.Px3.p1.14.m13.1.1.4a.cmml">-</mtext><mo id="S4.SS1.SSS0.Px3.p1.14.m13.1.1.1b" xref="S4.SS1.SSS0.Px3.p1.14.m13.1.1.1.cmml">⁢</mo><mn id="S4.SS1.SSS0.Px3.p1.14.m13.1.1.5" xref="S4.SS1.SSS0.Px3.p1.14.m13.1.1.5.cmml">4</mn></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px3.p1.14.m13.1b"><apply id="S4.SS1.SSS0.Px3.p1.14.m13.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.14.m13.1.1"><times id="S4.SS1.SSS0.Px3.p1.14.m13.1.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.14.m13.1.1.1"></times><cn id="S4.SS1.SSS0.Px3.p1.14.m13.1.1.2.cmml" type="float" xref="S4.SS1.SSS0.Px3.p1.14.m13.1.1.2">8.42</cn><ci id="S4.SS1.SSS0.Px3.p1.14.m13.1.1.3.cmml" xref="S4.SS1.SSS0.Px3.p1.14.m13.1.1.3">𝐸</ci><ci id="S4.SS1.SSS0.Px3.p1.14.m13.1.1.4a.cmml" xref="S4.SS1.SSS0.Px3.p1.14.m13.1.1.4"><mtext id="S4.SS1.SSS0.Px3.p1.14.m13.1.1.4.cmml" xref="S4.SS1.SSS0.Px3.p1.14.m13.1.1.4">-</mtext></ci><cn id="S4.SS1.SSS0.Px3.p1.14.m13.1.1.5.cmml" type="integer" xref="S4.SS1.SSS0.Px3.p1.14.m13.1.1.5">4</cn></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px3.p1.14.m13.1c">8.42E\text{-}4</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px3.p1.14.m13.1d">8.42 italic_E - 4</annotation></semantics></math> is a reasonable and effective return in high-frequency trading and it is close to our target return of <math alttext="\lambda=1E\text{-}3" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px3.p1.15.m14.1"><semantics id="S4.SS1.SSS0.Px3.p1.15.m14.1a"><mrow id="S4.SS1.SSS0.Px3.p1.15.m14.1.1" xref="S4.SS1.SSS0.Px3.p1.15.m14.1.1.cmml"><mi id="S4.SS1.SSS0.Px3.p1.15.m14.1.1.2" xref="S4.SS1.SSS0.Px3.p1.15.m14.1.1.2.cmml">λ</mi><mo id="S4.SS1.SSS0.Px3.p1.15.m14.1.1.1" xref="S4.SS1.SSS0.Px3.p1.15.m14.1.1.1.cmml">=</mo><mrow id="S4.SS1.SSS0.Px3.p1.15.m14.1.1.3" xref="S4.SS1.SSS0.Px3.p1.15.m14.1.1.3.cmml"><mn id="S4.SS1.SSS0.Px3.p1.15.m14.*******" xref="S4.SS1.SSS0.Px3.p1.15.m14.*******.cmml">1</mn><mo id="S4.SS1.SSS0.Px3.p1.15.m14.*******" xref="S4.SS1.SSS0.Px3.p1.15.m14.*******.cmml">⁢</mo><mi id="S4.SS1.SSS0.Px3.p1.15.m14.*******" xref="S4.SS1.SSS0.Px3.p1.15.m14.*******.cmml">E</mi><mo id="S4.SS1.SSS0.Px3.p1.15.m14.*******a" xref="S4.SS1.SSS0.Px3.p1.15.m14.*******.cmml">⁢</mo><mtext id="S4.SS1.SSS0.Px3.p1.15.m14.*******" xref="S4.SS1.SSS0.Px3.p1.15.m14.*******a.cmml">-</mtext><mo id="S4.SS1.SSS0.Px3.p1.15.m14.*******b" xref="S4.SS1.SSS0.Px3.p1.15.m14.*******.cmml">⁢</mo><mn id="S4.SS1.SSS0.Px3.p1.15.m14.*******" xref="S4.SS1.SSS0.Px3.p1.15.m14.*******.cmml">3</mn></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px3.p1.15.m14.1b"><apply id="S4.SS1.SSS0.Px3.p1.15.m14.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.15.m14.1.1"><eq id="S4.SS1.SSS0.Px3.p1.15.m14.1.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.15.m14.1.1.1"></eq><ci id="S4.SS1.SSS0.Px3.p1.15.m14.1.1.2.cmml" xref="S4.SS1.SSS0.Px3.p1.15.m14.1.1.2">𝜆</ci><apply id="S4.SS1.SSS0.Px3.p1.15.m14.1.1.3.cmml" xref="S4.SS1.SSS0.Px3.p1.15.m14.1.1.3"><times id="S4.SS1.SSS0.Px3.p1.15.m14.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.15.m14.*******"></times><cn id="S4.SS1.SSS0.Px3.p1.15.m14.*******.cmml" type="integer" xref="S4.SS1.SSS0.Px3.p1.15.m14.*******">1</cn><ci id="S4.SS1.SSS0.Px3.p1.15.m14.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.15.m14.*******">𝐸</ci><ci id="S4.SS1.SSS0.Px3.p1.15.m14.*******a.cmml" xref="S4.SS1.SSS0.Px3.p1.15.m14.*******"><mtext id="S4.SS1.SSS0.Px3.p1.15.m14.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.15.m14.*******">-</mtext></ci><cn id="S4.SS1.SSS0.Px3.p1.15.m14.*******.cmml" type="integer" xref="S4.SS1.SSS0.Px3.p1.15.m14.*******">3</cn></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px3.p1.15.m14.1c">\lambda=1E\text{-}3</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px3.p1.15.m14.1d">italic_λ = 1 italic_E - 3</annotation></semantics></math>).
Hence, this learned distance metric can assist the LA-Attention method in distinguishing potentially profitable <em class="ltx_emph ltx_font_italic" id="S4.SS1.SSS0.Px3.p1.16.2">samples with high <math alttext="p_{{\bm{x}}_{t}}" class="ltx_Math" display="inline" id="S4.SS1.SSS0.Px3.p1.16.2.m1.1"><semantics id="S4.SS1.SSS0.Px3.p1.16.2.m1.1a"><msub id="S4.SS1.SSS0.Px3.p1.16.2.m1.1.1" xref="S4.SS1.SSS0.Px3.p1.16.2.m1.1.1.cmml"><mi id="S4.SS1.SSS0.Px3.p1.16.2.m*******" xref="S4.SS1.SSS0.Px3.p1.16.2.m*******.cmml">p</mi><msub id="S4.SS1.SSS0.Px3.p1.16.2.m*******" xref="S4.SS1.SSS0.Px3.p1.16.2.m*******.cmml"><mi id="S4.SS1.SSS0.Px3.p1.16.2.m1.*******" xref="S4.SS1.SSS0.Px3.p1.16.2.m1.*******.cmml">𝐱</mi><mi id="S4.SS1.SSS0.Px3.p1.16.2.m1.*******" xref="S4.SS1.SSS0.Px3.p1.16.2.m1.*******.cmml">t</mi></msub></msub><annotation-xml encoding="MathML-Content" id="S4.SS1.SSS0.Px3.p1.16.2.m1.1b"><apply id="S4.SS1.SSS0.Px3.p1.16.2.m1.1.1.cmml" xref="S4.SS1.SSS0.Px3.p1.16.2.m1.1.1"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.16.2.m*******.cmml" xref="S4.SS1.SSS0.Px3.p1.16.2.m1.1.1">subscript</csymbol><ci id="S4.SS1.SSS0.Px3.p1.16.2.m*******.cmml" xref="S4.SS1.SSS0.Px3.p1.16.2.m*******">𝑝</ci><apply id="S4.SS1.SSS0.Px3.p1.16.2.m*******.cmml" xref="S4.SS1.SSS0.Px3.p1.16.2.m*******"><csymbol cd="ambiguous" id="S4.SS1.SSS0.Px3.p1.16.2.m1.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.16.2.m*******">subscript</csymbol><ci id="S4.SS1.SSS0.Px3.p1.16.2.m1.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.16.2.m1.*******">𝐱</ci><ci id="S4.SS1.SSS0.Px3.p1.16.2.m1.*******.cmml" xref="S4.SS1.SSS0.Px3.p1.16.2.m1.*******">𝑡</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS1.SSS0.Px3.p1.16.2.m1.1c">p_{{\bm{x}}_{t}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS1.SSS0.Px3.p1.16.2.m1.1d">italic_p start_POSTSUBSCRIPT bold_italic_x start_POSTSUBSCRIPT italic_t end_POSTSUBSCRIPT end_POSTSUBSCRIPT</annotation></semantics></math></em>.</p>
</div>
</section>
</section>
<section class="ltx_subsection" id="S4.SS2">
<h3 class="ltx_title ltx_title_subsection">
<span class="ltx_tag ltx_tag_subsection">4.2 </span>Iterative <span class="ltx_text ltx_framed ltx_framed_underline" id="S4.SS2.1.1">R</span>efinement L<span class="ltx_text ltx_framed ltx_framed_underline" id="S4.SS2.2.2">a</span>beling (RA-Labeling)</h3>
<div class="ltx_para" id="S4.SS2.p1">
<p class="ltx_p" id="S4.SS2.p1.5">Price movement is notoriously difficult to forecast because financial markets are complex dynamic systems <cite class="ltx_cite ltx_citemacro_cite">De Prado (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib7" title="">2018</a>); Lo and MacKinlay (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib20" title="">2011</a>)</cite>.
Indeed, we observe that the complicated financial markets sometimes mislead ML systems to produce opposite prediction results with extremely high confidence.
For example, some training samples get a high (low) probability of the positive prediction <math alttext="Pr(y=1)" class="ltx_Math" display="inline" id="S4.SS2.p1.1.m1.1"><semantics id="S4.SS2.p1.1.m1.1a"><mrow id="S4.SS2.p1.1.m1.1.1" xref="S4.SS2.p1.1.m1.1.1.cmml"><mi id="S4.SS2.p1.1.m*******" xref="S4.SS2.p1.1.m*******.cmml">P</mi><mo id="S4.SS2.p1.1.m*******" xref="S4.SS2.p1.1.m*******.cmml">⁢</mo><mi id="S4.SS2.p1.1.m1.1.1.4" xref="S4.SS2.p1.1.m1.1.1.4.cmml">r</mi><mo id="S4.SS2.p1.1.m*******a" xref="S4.SS2.p1.1.m*******.cmml">⁢</mo><mrow id="S4.SS2.p1.1.m*******.1" xref="S4.SS2.p1.1.m*******.1.1.cmml"><mo id="S4.SS2.p1.1.m*******.1.2" stretchy="false" xref="S4.SS2.p1.1.m*******.1.1.cmml">(</mo><mrow id="S4.SS2.p1.1.m*******.1.1" xref="S4.SS2.p1.1.m*******.1.1.cmml"><mi id="S4.SS2.p1.1.m*******.1.1.2" xref="S4.SS2.p1.1.m*******.1.1.2.cmml">y</mi><mo id="S4.SS2.p1.1.m*******.1.1.1" xref="S4.SS2.p1.1.m*******.1.1.1.cmml">=</mo><mn id="S4.SS2.p1.1.m*******.1.1.3" xref="S4.SS2.p1.1.m*******.1.1.3.cmml">1</mn></mrow><mo id="S4.SS2.p1.1.m*******.1.3" stretchy="false" xref="S4.SS2.p1.1.m*******.1.1.cmml">)</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p1.1.m1.1b"><apply id="S4.SS2.p1.1.m1.1.1.cmml" xref="S4.SS2.p1.1.m1.1.1"><times id="S4.SS2.p1.1.m*******.cmml" xref="S4.SS2.p1.1.m*******"></times><ci id="S4.SS2.p1.1.m*******.cmml" xref="S4.SS2.p1.1.m*******">𝑃</ci><ci id="S4.SS2.p1.1.m1.1.1.4.cmml" xref="S4.SS2.p1.1.m1.1.1.4">𝑟</ci><apply id="S4.SS2.p1.1.m*******.1.1.cmml" xref="S4.SS2.p1.1.m*******.1"><eq id="S4.SS2.p1.1.m*******.1.1.1.cmml" xref="S4.SS2.p1.1.m*******.1.1.1"></eq><ci id="S4.SS2.p1.1.m*******.1.1.2.cmml" xref="S4.SS2.p1.1.m*******.1.1.2">𝑦</ci><cn id="S4.SS2.p1.1.m*******.1.1.3.cmml" type="integer" xref="S4.SS2.p1.1.m*******.1.1.3">1</cn></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p1.1.m1.1c">Pr(y=1)</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p1.1.m1.1d">italic_P italic_r ( italic_y = 1 )</annotation></semantics></math>, but their labels are negative (positive).
Such <em class="ltx_emph ltx_font_italic" id="S4.SS2.p1.5.1">noisy samples</em> hamper the model generalization in price movement forecasting <cite class="ltx_cite ltx_citemacro_cite">Li <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib17" title="">2018</a>)</cite>.
Thus, LARA should be robust to noisy labels of potentially profitable samples, and we adopt the label refurbishment technique introduced by <cite class="ltx_cite ltx_citemacro_cite">Song <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib25" title="">2022</a>)</cite> to learn a noise-robust prediction model.
Formally, given the training data <math alttext="{\bm{X}}\in\mathbb{R}^{N\times d}" class="ltx_Math" display="inline" id="S4.SS2.p1.2.m2.1"><semantics id="S4.SS2.p1.2.m2.1a"><mrow id="S4.SS2.p1.2.m2.1.1" xref="S4.SS2.p1.2.m2.1.1.cmml"><mi id="S4.SS2.p1.2.m2.1.1.2" xref="S4.SS2.p1.2.m2.1.1.2.cmml">𝑿</mi><mo id="S4.SS2.p1.2.m2.1.1.1" xref="S4.SS2.p1.2.m2.1.1.1.cmml">∈</mo><msup id="S4.SS2.p1.2.m2.1.1.3" xref="S4.SS2.p1.2.m2.1.1.3.cmml"><mi id="S4.SS2.p1.2.m2.*******" xref="S4.SS2.p1.2.m2.*******.cmml">ℝ</mi><mrow id="S4.SS2.p1.2.m2.*******" xref="S4.SS2.p1.2.m2.*******.cmml"><mi id="S4.SS2.p1.2.m2.*******.2" xref="S4.SS2.p1.2.m2.*******.2.cmml">N</mi><mo id="S4.SS2.p1.2.m2.*******.1" lspace="0.222em" rspace="0.222em" xref="S4.SS2.p1.2.m2.*******.1.cmml">×</mo><mi id="S4.SS2.p1.2.m2.*******.3" xref="S4.SS2.p1.2.m2.*******.3.cmml">d</mi></mrow></msup></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p1.2.m2.1b"><apply id="S4.SS2.p1.2.m2.1.1.cmml" xref="S4.SS2.p1.2.m2.1.1"><in id="S4.SS2.p1.2.m2.1.1.1.cmml" xref="S4.SS2.p1.2.m2.1.1.1"></in><ci id="S4.SS2.p1.2.m2.1.1.2.cmml" xref="S4.SS2.p1.2.m2.1.1.2">𝑿</ci><apply id="S4.SS2.p1.2.m2.1.1.3.cmml" xref="S4.SS2.p1.2.m2.1.1.3"><csymbol cd="ambiguous" id="S4.SS2.p1.2.m2.*******.cmml" xref="S4.SS2.p1.2.m2.1.1.3">superscript</csymbol><ci id="S4.SS2.p1.2.m2.*******.cmml" xref="S4.SS2.p1.2.m2.*******">ℝ</ci><apply id="S4.SS2.p1.2.m2.*******.cmml" xref="S4.SS2.p1.2.m2.*******"><times id="S4.SS2.p1.2.m2.*******.1.cmml" xref="S4.SS2.p1.2.m2.*******.1"></times><ci id="S4.SS2.p1.2.m2.*******.2.cmml" xref="S4.SS2.p1.2.m2.*******.2">𝑁</ci><ci id="S4.SS2.p1.2.m2.*******.3.cmml" xref="S4.SS2.p1.2.m2.*******.3">𝑑</ci></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p1.2.m2.1c">{\bm{X}}\in\mathbb{R}^{N\times d}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p1.2.m2.1d">bold_italic_X ∈ blackboard_R start_POSTSUPERSCRIPT italic_N × italic_d end_POSTSUPERSCRIPT</annotation></semantics></math>, our method runs in an iterative manner to obtain adaptively refined labels <math alttext="\{{\bm{y}}^{k}\}_{0\leq k\leq K}" class="ltx_Math" display="inline" id="S4.SS2.p1.3.m3.1"><semantics id="S4.SS2.p1.3.m3.1a"><msub id="S4.SS2.p1.3.m3.1.1" xref="S4.SS2.p1.3.m3.1.1.cmml"><mrow id="S4.SS2.p1.3.m3.*******" xref="S4.SS2.p1.3.m3.*******.cmml"><mo id="S4.SS2.p1.3.m3.*******.2" stretchy="false" xref="S4.SS2.p1.3.m3.*******.cmml">{</mo><msup id="S4.SS2.p1.3.m3.*******.1" xref="S4.SS2.p1.3.m3.*******.1.cmml"><mi id="S4.SS2.p1.3.m3.*******.1.2" xref="S4.SS2.p1.3.m3.*******.1.2.cmml">𝒚</mi><mi id="S4.SS2.p1.3.m3.*******.1.3" xref="S4.SS2.p1.3.m3.*******.1.3.cmml">k</mi></msup><mo id="S4.SS2.p1.3.m3.*******.3" stretchy="false" xref="S4.SS2.p1.3.m3.*******.cmml">}</mo></mrow><mrow id="S4.SS2.p1.3.m3.1.1.3" xref="S4.SS2.p1.3.m3.1.1.3.cmml"><mn id="S4.SS2.p1.3.m3.*******" xref="S4.SS2.p1.3.m3.*******.cmml">0</mn><mo id="S4.SS2.p1.3.m3.*******" xref="S4.SS2.p1.3.m3.*******.cmml">≤</mo><mi id="S4.SS2.p1.3.m3.*******" xref="S4.SS2.p1.3.m3.*******.cmml">k</mi><mo id="S4.SS2.p1.3.m3.*******" xref="S4.SS2.p1.3.m3.*******.cmml">≤</mo><mi id="S4.SS2.p1.3.m3.*******" xref="S4.SS2.p1.3.m3.*******.cmml">K</mi></mrow></msub><annotation-xml encoding="MathML-Content" id="S4.SS2.p1.3.m3.1b"><apply id="S4.SS2.p1.3.m3.1.1.cmml" xref="S4.SS2.p1.3.m3.1.1"><csymbol cd="ambiguous" id="S4.SS2.p1.3.m3.1.1.2.cmml" xref="S4.SS2.p1.3.m3.1.1">subscript</csymbol><set id="S4.SS2.p1.3.m3.*******.cmml" xref="S4.SS2.p1.3.m3.*******"><apply id="S4.SS2.p1.3.m3.*******.1.cmml" xref="S4.SS2.p1.3.m3.*******.1"><csymbol cd="ambiguous" id="S4.SS2.p1.3.m3.*******.1.1.cmml" xref="S4.SS2.p1.3.m3.*******.1">superscript</csymbol><ci id="S4.SS2.p1.3.m3.*******.1.2.cmml" xref="S4.SS2.p1.3.m3.*******.1.2">𝒚</ci><ci id="S4.SS2.p1.3.m3.*******.1.3.cmml" xref="S4.SS2.p1.3.m3.*******.1.3">𝑘</ci></apply></set><apply id="S4.SS2.p1.3.m3.1.1.3.cmml" xref="S4.SS2.p1.3.m3.1.1.3"><and id="S4.SS2.p1.3.m3.1.1.3a.cmml" xref="S4.SS2.p1.3.m3.1.1.3"></and><apply id="S4.SS2.p1.3.m3.1.1.3b.cmml" xref="S4.SS2.p1.3.m3.1.1.3"><leq id="S4.SS2.p1.3.m3.*******.cmml" xref="S4.SS2.p1.3.m3.*******"></leq><cn id="S4.SS2.p1.3.m3.*******.cmml" type="integer" xref="S4.SS2.p1.3.m3.*******">0</cn><ci id="S4.SS2.p1.3.m3.*******.cmml" xref="S4.SS2.p1.3.m3.*******">𝑘</ci></apply><apply id="S4.SS2.p1.3.m3.1.1.3c.cmml" xref="S4.SS2.p1.3.m3.1.1.3"><leq id="S4.SS2.p1.3.m3.*******.cmml" xref="S4.SS2.p1.3.m3.*******"></leq><share href="https://arxiv.org/html/2107.11972v4#S4.SS2.p1.3.m3.*******.cmml" id="S4.SS2.p1.3.m3.1.1.3d.cmml" xref="S4.SS2.p1.3.m3.1.1.3"></share><ci id="S4.SS2.p1.3.m3.*******.cmml" xref="S4.SS2.p1.3.m3.*******">𝐾</ci></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p1.3.m3.1c">\{{\bm{y}}^{k}\}_{0\leq k\leq K}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p1.3.m3.1d">{ bold_italic_y start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT } start_POSTSUBSCRIPT 0 ≤ italic_k ≤ italic_K end_POSTSUBSCRIPT</annotation></semantics></math>, where <math alttext="N" class="ltx_Math" display="inline" id="S4.SS2.p1.4.m4.1"><semantics id="S4.SS2.p1.4.m4.1a"><mi id="S4.SS2.p1.4.m4.1.1" xref="S4.SS2.p1.4.m4.1.1.cmml">N</mi><annotation-xml encoding="MathML-Content" id="S4.SS2.p1.4.m4.1b"><ci id="S4.SS2.p1.4.m4.1.1.cmml" xref="S4.SS2.p1.4.m4.1.1">𝑁</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p1.4.m4.1c">N</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p1.4.m4.1d">italic_N</annotation></semantics></math> is the number of training data and <math alttext="K" class="ltx_Math" display="inline" id="S4.SS2.p1.5.m5.1"><semantics id="S4.SS2.p1.5.m5.1a"><mi id="S4.SS2.p1.5.m5.1.1" xref="S4.SS2.p1.5.m5.1.1.cmml">K</mi><annotation-xml encoding="MathML-Content" id="S4.SS2.p1.5.m5.1b"><ci id="S4.SS2.p1.5.m5.1.1.cmml" xref="S4.SS2.p1.5.m5.1.1">𝐾</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p1.5.m5.1c">K</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p1.5.m5.1d">italic_K</annotation></semantics></math> is the number of rounds in iterative refinement labeling.</p>
</div>
<div class="ltx_para" id="S4.SS2.p2">
<p class="ltx_p" id="S4.SS2.p2.8">We denote the trained predictor at the <math alttext="k" class="ltx_Math" display="inline" id="S4.SS2.p2.1.m1.1"><semantics id="S4.SS2.p2.1.m1.1a"><mi id="S4.SS2.p2.1.m1.1.1" xref="S4.SS2.p2.1.m1.1.1.cmml">k</mi><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.1.m1.1b"><ci id="S4.SS2.p2.1.m1.1.1.cmml" xref="S4.SS2.p2.1.m1.1.1">𝑘</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.1.m1.1c">k</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.1.m1.1d">italic_k</annotation></semantics></math>-th round and the current prediction of the sample <math alttext="j" class="ltx_Math" display="inline" id="S4.SS2.p2.2.m2.1"><semantics id="S4.SS2.p2.2.m2.1a"><mi id="S4.SS2.p2.2.m2.1.1" xref="S4.SS2.p2.2.m2.1.1.cmml">j</mi><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.2.m2.1b"><ci id="S4.SS2.p2.2.m2.1.1.cmml" xref="S4.SS2.p2.2.m2.1.1">𝑗</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.2.m2.1c">j</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.2.m2.1d">italic_j</annotation></semantics></math> at the <math alttext="k" class="ltx_Math" display="inline" id="S4.SS2.p2.3.m3.1"><semantics id="S4.SS2.p2.3.m3.1a"><mi id="S4.SS2.p2.3.m3.1.1" xref="S4.SS2.p2.3.m3.1.1.cmml">k</mi><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.3.m3.1b"><ci id="S4.SS2.p2.3.m3.1.1.cmml" xref="S4.SS2.p2.3.m3.1.1">𝑘</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.3.m3.1c">k</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.3.m3.1d">italic_k</annotation></semantics></math>-th round as <math alttext="\{f_{\theta^{k}}\}_{0\leq k\leq K}" class="ltx_Math" display="inline" id="S4.SS2.p2.4.m4.1"><semantics id="S4.SS2.p2.4.m4.1a"><msub id="S4.SS2.p2.4.m4.1.1" xref="S4.SS2.p2.4.m4.1.1.cmml"><mrow id="S4.SS2.p2.4.m4.*******" xref="S4.SS2.p2.4.m4.*******.cmml"><mo id="S4.SS2.p2.4.m4.*******.2" stretchy="false" xref="S4.SS2.p2.4.m4.*******.cmml">{</mo><msub id="S4.SS2.p2.4.m4.*******.1" xref="S4.SS2.p2.4.m4.*******.1.cmml"><mi id="S4.SS2.p2.4.m4.*******.1.2" xref="S4.SS2.p2.4.m4.*******.1.2.cmml">f</mi><msup id="S4.SS2.p2.4.m4.*******.1.3" xref="S4.SS2.p2.4.m4.*******.1.3.cmml"><mi id="S4.SS2.p2.4.m4.1.1.1.*******" xref="S4.SS2.p2.4.m4.1.1.1.*******.cmml">θ</mi><mi id="S4.SS2.p2.4.m4.*******.1.3.3" xref="S4.SS2.p2.4.m4.*******.1.3.3.cmml">k</mi></msup></msub><mo id="S4.SS2.p2.4.m4.*******.3" stretchy="false" xref="S4.SS2.p2.4.m4.*******.cmml">}</mo></mrow><mrow id="S4.SS2.p2.4.m4.1.1.3" xref="S4.SS2.p2.4.m4.1.1.3.cmml"><mn id="S4.SS2.p2.4.m4.*******" xref="S4.SS2.p2.4.m4.*******.cmml">0</mn><mo id="S4.SS2.p2.4.m4.*******" xref="S4.SS2.p2.4.m4.*******.cmml">≤</mo><mi id="S4.SS2.p2.4.m4.*******" xref="S4.SS2.p2.4.m4.*******.cmml">k</mi><mo id="S4.SS2.p2.4.m4.*******" xref="S4.SS2.p2.4.m4.*******.cmml">≤</mo><mi id="S4.SS2.p2.4.m4.*******" xref="S4.SS2.p2.4.m4.*******.cmml">K</mi></mrow></msub><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.4.m4.1b"><apply id="S4.SS2.p2.4.m4.1.1.cmml" xref="S4.SS2.p2.4.m4.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.4.m4.1.1.2.cmml" xref="S4.SS2.p2.4.m4.1.1">subscript</csymbol><set id="S4.SS2.p2.4.m4.*******.cmml" xref="S4.SS2.p2.4.m4.*******"><apply id="S4.SS2.p2.4.m4.*******.1.cmml" xref="S4.SS2.p2.4.m4.*******.1"><csymbol cd="ambiguous" id="S4.SS2.p2.4.m4.*******.1.1.cmml" xref="S4.SS2.p2.4.m4.*******.1">subscript</csymbol><ci id="S4.SS2.p2.4.m4.*******.1.2.cmml" xref="S4.SS2.p2.4.m4.*******.1.2">𝑓</ci><apply id="S4.SS2.p2.4.m4.*******.1.3.cmml" xref="S4.SS2.p2.4.m4.*******.1.3"><csymbol cd="ambiguous" id="S4.SS2.p2.4.m4.*******.1.3.1.cmml" xref="S4.SS2.p2.4.m4.*******.1.3">superscript</csymbol><ci id="S4.SS2.p2.4.m4.1.1.1.*******.cmml" xref="S4.SS2.p2.4.m4.1.1.1.*******">𝜃</ci><ci id="S4.SS2.p2.4.m4.*******.1.3.3.cmml" xref="S4.SS2.p2.4.m4.*******.1.3.3">𝑘</ci></apply></apply></set><apply id="S4.SS2.p2.4.m4.1.1.3.cmml" xref="S4.SS2.p2.4.m4.1.1.3"><and id="S4.SS2.p2.4.m4.1.1.3a.cmml" xref="S4.SS2.p2.4.m4.1.1.3"></and><apply id="S4.SS2.p2.4.m4.1.1.3b.cmml" xref="S4.SS2.p2.4.m4.1.1.3"><leq id="S4.SS2.p2.4.m4.*******.cmml" xref="S4.SS2.p2.4.m4.*******"></leq><cn id="S4.SS2.p2.4.m4.*******.cmml" type="integer" xref="S4.SS2.p2.4.m4.*******">0</cn><ci id="S4.SS2.p2.4.m4.*******.cmml" xref="S4.SS2.p2.4.m4.*******">𝑘</ci></apply><apply id="S4.SS2.p2.4.m4.1.1.3c.cmml" xref="S4.SS2.p2.4.m4.1.1.3"><leq id="S4.SS2.p2.4.m4.*******.cmml" xref="S4.SS2.p2.4.m4.*******"></leq><share href="https://arxiv.org/html/2107.11972v4#S4.SS2.p2.4.m4.*******.cmml" id="S4.SS2.p2.4.m4.1.1.3d.cmml" xref="S4.SS2.p2.4.m4.1.1.3"></share><ci id="S4.SS2.p2.4.m4.*******.cmml" xref="S4.SS2.p2.4.m4.*******">𝐾</ci></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.4.m4.1c">\{f_{\theta^{k}}\}_{0\leq k\leq K}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.4.m4.1d">{ italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT end_POSTSUBSCRIPT } start_POSTSUBSCRIPT 0 ≤ italic_k ≤ italic_K end_POSTSUBSCRIPT</annotation></semantics></math> and <math alttext="f_{\theta^{k}}({\bm{x}}_{j})" class="ltx_Math" display="inline" id="S4.SS2.p2.5.m5.1"><semantics id="S4.SS2.p2.5.m5.1a"><mrow id="S4.SS2.p2.5.m5.1.1" xref="S4.SS2.p2.5.m5.1.1.cmml"><msub id="S4.SS2.p2.5.m5.1.1.3" xref="S4.SS2.p2.5.m5.1.1.3.cmml"><mi id="S4.SS2.p2.5.m5.*******" xref="S4.SS2.p2.5.m5.*******.cmml">f</mi><msup id="S4.SS2.p2.5.m5.*******" xref="S4.SS2.p2.5.m5.*******.cmml"><mi id="S4.SS2.p2.5.m5.*******.2" xref="S4.SS2.p2.5.m5.*******.2.cmml">θ</mi><mi id="S4.SS2.p2.5.m5.*******.3" xref="S4.SS2.p2.5.m5.*******.3.cmml">k</mi></msup></msub><mo id="S4.SS2.p2.5.m5.1.1.2" xref="S4.SS2.p2.5.m5.1.1.2.cmml">⁢</mo><mrow id="S4.SS2.p2.5.m5.*******" xref="S4.SS2.p2.5.m5.*******.1.cmml"><mo id="S4.SS2.p2.5.m5.*******.2" stretchy="false" xref="S4.SS2.p2.5.m5.*******.1.cmml">(</mo><msub id="S4.SS2.p2.5.m5.*******.1" xref="S4.SS2.p2.5.m5.*******.1.cmml"><mi id="S4.SS2.p2.5.m5.*******.1.2" xref="S4.SS2.p2.5.m5.*******.1.2.cmml">𝒙</mi><mi id="S4.SS2.p2.5.m5.*******.1.3" xref="S4.SS2.p2.5.m5.*******.1.3.cmml">j</mi></msub><mo id="S4.SS2.p2.5.m5.*******.3" stretchy="false" xref="S4.SS2.p2.5.m5.*******.1.cmml">)</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.5.m5.1b"><apply id="S4.SS2.p2.5.m5.1.1.cmml" xref="S4.SS2.p2.5.m5.1.1"><times id="S4.SS2.p2.5.m5.1.1.2.cmml" xref="S4.SS2.p2.5.m5.1.1.2"></times><apply id="S4.SS2.p2.5.m5.1.1.3.cmml" xref="S4.SS2.p2.5.m5.1.1.3"><csymbol cd="ambiguous" id="S4.SS2.p2.5.m5.*******.cmml" xref="S4.SS2.p2.5.m5.1.1.3">subscript</csymbol><ci id="S4.SS2.p2.5.m5.*******.cmml" xref="S4.SS2.p2.5.m5.*******">𝑓</ci><apply id="S4.SS2.p2.5.m5.*******.cmml" xref="S4.SS2.p2.5.m5.*******"><csymbol cd="ambiguous" id="S4.SS2.p2.5.m5.*******.1.cmml" xref="S4.SS2.p2.5.m5.*******">superscript</csymbol><ci id="S4.SS2.p2.5.m5.*******.2.cmml" xref="S4.SS2.p2.5.m5.*******.2">𝜃</ci><ci id="S4.SS2.p2.5.m5.*******.3.cmml" xref="S4.SS2.p2.5.m5.*******.3">𝑘</ci></apply></apply><apply id="S4.SS2.p2.5.m5.*******.1.cmml" xref="S4.SS2.p2.5.m5.*******"><csymbol cd="ambiguous" id="S4.SS2.p2.5.m5.*******.1.1.cmml" xref="S4.SS2.p2.5.m5.*******">subscript</csymbol><ci id="S4.SS2.p2.5.m5.*******.1.2.cmml" xref="S4.SS2.p2.5.m5.*******.1.2">𝒙</ci><ci id="S4.SS2.p2.5.m5.*******.1.3.cmml" xref="S4.SS2.p2.5.m5.*******.1.3">𝑗</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.5.m5.1c">f_{\theta^{k}}({\bm{x}}_{j})</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.5.m5.1d">italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT end_POSTSUBSCRIPT ( bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT )</annotation></semantics></math>, respectively.
The refurbished label <math alttext="y_{j}^{k+1}" class="ltx_Math" display="inline" id="S4.SS2.p2.6.m6.1"><semantics id="S4.SS2.p2.6.m6.1a"><msubsup id="S4.SS2.p2.6.m6.1.1" xref="S4.SS2.p2.6.m6.1.1.cmml"><mi id="S4.SS2.p2.6.m6.*******" xref="S4.SS2.p2.6.m6.*******.cmml">y</mi><mi id="S4.SS2.p2.6.m6.*******" xref="S4.SS2.p2.6.m6.*******.cmml">j</mi><mrow id="S4.SS2.p2.6.m6.1.1.3" xref="S4.SS2.p2.6.m6.1.1.3.cmml"><mi id="S4.SS2.p2.6.m6.*******" xref="S4.SS2.p2.6.m6.*******.cmml">k</mi><mo id="S4.SS2.p2.6.m6.*******" xref="S4.SS2.p2.6.m6.*******.cmml">+</mo><mn id="S4.SS2.p2.6.m6.*******" xref="S4.SS2.p2.6.m6.*******.cmml">1</mn></mrow></msubsup><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.6.m6.1b"><apply id="S4.SS2.p2.6.m6.1.1.cmml" xref="S4.SS2.p2.6.m6.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.6.m6.1.1.1.cmml" xref="S4.SS2.p2.6.m6.1.1">superscript</csymbol><apply id="S4.SS2.p2.6.m6.1.1.2.cmml" xref="S4.SS2.p2.6.m6.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.6.m6.*******.cmml" xref="S4.SS2.p2.6.m6.1.1">subscript</csymbol><ci id="S4.SS2.p2.6.m6.*******.cmml" xref="S4.SS2.p2.6.m6.*******">𝑦</ci><ci id="S4.SS2.p2.6.m6.*******.cmml" xref="S4.SS2.p2.6.m6.*******">𝑗</ci></apply><apply id="S4.SS2.p2.6.m6.1.1.3.cmml" xref="S4.SS2.p2.6.m6.1.1.3"><plus id="S4.SS2.p2.6.m6.*******.cmml" xref="S4.SS2.p2.6.m6.*******"></plus><ci id="S4.SS2.p2.6.m6.*******.cmml" xref="S4.SS2.p2.6.m6.*******">𝑘</ci><cn id="S4.SS2.p2.6.m6.*******.cmml" type="integer" xref="S4.SS2.p2.6.m6.*******">1</cn></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.6.m6.1c">y_{j}^{k+1}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.6.m6.1d">italic_y start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k + 1 end_POSTSUPERSCRIPT</annotation></semantics></math> can be obtained by a convex combination of the current label <math alttext="y_{j}^{k}" class="ltx_Math" display="inline" id="S4.SS2.p2.7.m7.1"><semantics id="S4.SS2.p2.7.m7.1a"><msubsup id="S4.SS2.p2.7.m7.1.1" xref="S4.SS2.p2.7.m7.1.1.cmml"><mi id="S4.SS2.p2.7.m7.*******" xref="S4.SS2.p2.7.m7.*******.cmml">y</mi><mi id="S4.SS2.p2.7.m7.*******" xref="S4.SS2.p2.7.m7.*******.cmml">j</mi><mi id="S4.SS2.p2.7.m7.1.1.3" xref="S4.SS2.p2.7.m7.1.1.3.cmml">k</mi></msubsup><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.7.m7.1b"><apply id="S4.SS2.p2.7.m7.1.1.cmml" xref="S4.SS2.p2.7.m7.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.7.m7.1.1.1.cmml" xref="S4.SS2.p2.7.m7.1.1">superscript</csymbol><apply id="S4.SS2.p2.7.m7.1.1.2.cmml" xref="S4.SS2.p2.7.m7.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.7.m7.*******.cmml" xref="S4.SS2.p2.7.m7.1.1">subscript</csymbol><ci id="S4.SS2.p2.7.m7.*******.cmml" xref="S4.SS2.p2.7.m7.*******">𝑦</ci><ci id="S4.SS2.p2.7.m7.*******.cmml" xref="S4.SS2.p2.7.m7.*******">𝑗</ci></apply><ci id="S4.SS2.p2.7.m7.1.1.3.cmml" xref="S4.SS2.p2.7.m7.1.1.3">𝑘</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.7.m7.1c">y_{j}^{k}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.7.m7.1d">italic_y start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT</annotation></semantics></math> and the associated prediction <math alttext="f_{\theta^{k}}({\bm{x}}_{j})" class="ltx_Math" display="inline" id="S4.SS2.p2.8.m8.1"><semantics id="S4.SS2.p2.8.m8.1a"><mrow id="S4.SS2.p2.8.m8.1.1" xref="S4.SS2.p2.8.m8.1.1.cmml"><msub id="S4.SS2.p2.8.m8.1.1.3" xref="S4.SS2.p2.8.m8.1.1.3.cmml"><mi id="S4.SS2.p2.8.m8.*******" xref="S4.SS2.p2.8.m8.*******.cmml">f</mi><msup id="S4.SS2.p2.8.m8.*******" xref="S4.SS2.p2.8.m8.*******.cmml"><mi id="S4.SS2.p2.8.m8.*******.2" xref="S4.SS2.p2.8.m8.*******.2.cmml">θ</mi><mi id="S4.SS2.p2.8.m8.*******.3" xref="S4.SS2.p2.8.m8.*******.3.cmml">k</mi></msup></msub><mo id="S4.SS2.p2.8.m8.1.1.2" xref="S4.SS2.p2.8.m8.1.1.2.cmml">⁢</mo><mrow id="S4.SS2.p2.8.m8.*******" xref="S4.SS2.p2.8.m8.*******.1.cmml"><mo id="S4.SS2.p2.8.m8.*******.2" stretchy="false" xref="S4.SS2.p2.8.m8.*******.1.cmml">(</mo><msub id="S4.SS2.p2.8.m8.*******.1" xref="S4.SS2.p2.8.m8.*******.1.cmml"><mi id="S4.SS2.p2.8.m8.*******.1.2" xref="S4.SS2.p2.8.m8.*******.1.2.cmml">𝒙</mi><mi id="S4.SS2.p2.8.m8.*******.1.3" xref="S4.SS2.p2.8.m8.*******.1.3.cmml">j</mi></msub><mo id="S4.SS2.p2.8.m8.*******.3" stretchy="false" xref="S4.SS2.p2.8.m8.*******.1.cmml">)</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.8.m8.1b"><apply id="S4.SS2.p2.8.m8.1.1.cmml" xref="S4.SS2.p2.8.m8.1.1"><times id="S4.SS2.p2.8.m8.1.1.2.cmml" xref="S4.SS2.p2.8.m8.1.1.2"></times><apply id="S4.SS2.p2.8.m8.1.1.3.cmml" xref="S4.SS2.p2.8.m8.1.1.3"><csymbol cd="ambiguous" id="S4.SS2.p2.8.m8.*******.cmml" xref="S4.SS2.p2.8.m8.1.1.3">subscript</csymbol><ci id="S4.SS2.p2.8.m8.*******.cmml" xref="S4.SS2.p2.8.m8.*******">𝑓</ci><apply id="S4.SS2.p2.8.m8.*******.cmml" xref="S4.SS2.p2.8.m8.*******"><csymbol cd="ambiguous" id="S4.SS2.p2.8.m8.*******.1.cmml" xref="S4.SS2.p2.8.m8.*******">superscript</csymbol><ci id="S4.SS2.p2.8.m8.*******.2.cmml" xref="S4.SS2.p2.8.m8.*******.2">𝜃</ci><ci id="S4.SS2.p2.8.m8.*******.3.cmml" xref="S4.SS2.p2.8.m8.*******.3">𝑘</ci></apply></apply><apply id="S4.SS2.p2.8.m8.*******.1.cmml" xref="S4.SS2.p2.8.m8.*******"><csymbol cd="ambiguous" id="S4.SS2.p2.8.m8.*******.1.1.cmml" xref="S4.SS2.p2.8.m8.*******">subscript</csymbol><ci id="S4.SS2.p2.8.m8.*******.1.2.cmml" xref="S4.SS2.p2.8.m8.*******.1.2">𝒙</ci><ci id="S4.SS2.p2.8.m8.*******.1.3.cmml" xref="S4.SS2.p2.8.m8.*******.1.3">𝑗</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.8.m8.1c">f_{\theta^{k}}({\bm{x}}_{j})</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.8.m8.1d">italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT end_POSTSUBSCRIPT ( bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT )</annotation></semantics></math>:</p>
<table class="ltx_equation ltx_eqn_table" id="S4.E6">
<tbody><tr class="ltx_equation ltx_eqn_row ltx_align_baseline">
<td class="ltx_eqn_cell ltx_eqn_center_padleft"></td>
<td class="ltx_eqn_cell ltx_align_center"><math alttext="y_{j}^{k+1}=\alpha_{j}^{k}y_{j}^{k}+(1-\alpha_{j}^{k})f_{\theta^{k}}({\bm{x}}_%
{j})," class="ltx_Math" display="block" id="S4.E6.m1.1"><semantics id="S4.E6.m1.1a"><mrow id="S4.E6.m*******" xref="S4.E6.m*******.1.cmml"><mrow id="S4.E6.m*******.1" xref="S4.E6.m*******.1.cmml"><msubsup id="S4.E6.m*******.1.4" xref="S4.E6.m*******.1.4.cmml"><mi id="S4.E6.m*******.1.4.2.2" xref="S4.E6.m*******.1.4.2.2.cmml">y</mi><mi id="S4.E6.m*******.1.4.2.3" xref="S4.E6.m*******.1.4.2.3.cmml">j</mi><mrow id="S4.E6.m*******.1.4.3" xref="S4.E6.m*******.1.4.3.cmml"><mi id="S4.E6.m*******.1.4.3.2" xref="S4.E6.m*******.1.4.3.2.cmml">k</mi><mo id="S4.E6.m*******.1.4.3.1" xref="S4.E6.m*******.1.4.3.1.cmml">+</mo><mn id="S4.E6.m*******.1.4.3.3" xref="S4.E6.m*******.1.4.3.3.cmml">1</mn></mrow></msubsup><mo id="S4.E6.m*******.1.3" xref="S4.E6.m*******.1.3.cmml">=</mo><mrow id="S4.E6.m*******.1.2" xref="S4.E6.m*******.1.2.cmml"><mrow id="S4.E6.m*******.1.2.4" xref="S4.E6.m*******.1.2.4.cmml"><msubsup id="S4.E6.m*******.1.2.4.2" xref="S4.E6.m*******.1.2.4.2.cmml"><mi id="S4.E6.m*******.1.*******.2" xref="S4.E6.m*******.1.*******.2.cmml">α</mi><mi id="S4.E6.m*******.1.*******.3" xref="S4.E6.m*******.1.*******.3.cmml">j</mi><mi id="S4.E6.m*******.1.*******" xref="S4.E6.m*******.1.*******.cmml">k</mi></msubsup><mo id="S4.E6.m*******.1.2.4.1" xref="S4.E6.m*******.1.2.4.1.cmml">⁢</mo><msubsup id="S4.E6.m*******.1.2.4.3" xref="S4.E6.m*******.1.2.4.3.cmml"><mi id="S4.E6.m*******.1.*******.2" xref="S4.E6.m*******.1.*******.2.cmml">y</mi><mi id="S4.E6.m*******.1.*******.3" xref="S4.E6.m*******.1.*******.3.cmml">j</mi><mi id="S4.E6.m*******.1.*******" xref="S4.E6.m*******.1.*******.cmml">k</mi></msubsup></mrow><mo id="S4.E6.m*******.1.2.3" xref="S4.E6.m*******.1.2.3.cmml">+</mo><mrow id="S4.E6.m*******.1.2.2" xref="S4.E6.m*******.1.2.2.cmml"><mrow id="S4.E6.m*******.*******.1" xref="S4.E6.m*******.*******.1.1.cmml"><mo id="S4.E6.m*******.*******.1.2" stretchy="false" xref="S4.E6.m*******.*******.1.1.cmml">(</mo><mrow id="S4.E6.m*******.*******.1.1" xref="S4.E6.m*******.*******.1.1.cmml"><mn id="S4.E6.m*******.*******.1.1.2" xref="S4.E6.m*******.*******.1.1.2.cmml">1</mn><mo id="S4.E6.m*******.*******.1.1.1" xref="S4.E6.m*******.*******.1.1.1.cmml">−</mo><msubsup id="S4.E6.m*******.*******.1.1.3" xref="S4.E6.m*******.*******.1.1.3.cmml"><mi id="S4.E6.m*******.*******.*******.2" xref="S4.E6.m*******.*******.*******.2.cmml">α</mi><mi id="S4.E6.m*******.*******.*******.3" xref="S4.E6.m*******.*******.*******.3.cmml">j</mi><mi id="S4.E6.m*******.*******.*******" xref="S4.E6.m*******.*******.*******.cmml">k</mi></msubsup></mrow><mo id="S4.E6.m*******.*******.1.3" stretchy="false" xref="S4.E6.m*******.*******.1.1.cmml">)</mo></mrow><mo id="S4.E6.m*******.*******" xref="S4.E6.m*******.*******.cmml">⁢</mo><msub id="S4.E6.m*******.1.2.2.4" xref="S4.E6.m*******.1.2.2.4.cmml"><mi id="S4.E6.m*******.1.*******" xref="S4.E6.m*******.1.*******.cmml">f</mi><msup id="S4.E6.m*******.1.*******" xref="S4.E6.m*******.1.*******.cmml"><mi id="S4.E6.m*******.1.*******.2" xref="S4.E6.m*******.1.*******.2.cmml">θ</mi><mi id="S4.E6.m*******.1.*******.3" xref="S4.E6.m*******.1.*******.3.cmml">k</mi></msup></msub><mo id="S4.E6.m*******.*******a" xref="S4.E6.m*******.*******.cmml">⁢</mo><mrow id="S4.E6.m*******.1.*******" xref="S4.E6.m*******.1.2.*******.cmml"><mo id="S4.E6.m*******.1.*******.2" stretchy="false" xref="S4.E6.m*******.1.2.*******.cmml">(</mo><msub id="S4.E6.m*******.1.2.*******" xref="S4.E6.m*******.1.2.*******.cmml"><mi id="S4.E6.m*******.1.2.*******.2" xref="S4.E6.m*******.1.2.*******.2.cmml">𝒙</mi><mi id="S4.E6.m*******.1.2.*******.3" xref="S4.E6.m*******.1.2.*******.3.cmml">j</mi></msub><mo id="S4.E6.m*******.1.*******.3" stretchy="false" xref="S4.E6.m*******.1.2.*******.cmml">)</mo></mrow></mrow></mrow></mrow><mo id="S4.E6.m*******.2" xref="S4.E6.m*******.1.cmml">,</mo></mrow><annotation-xml encoding="MathML-Content" id="S4.E6.m1.1b"><apply id="S4.E6.m*******.1.cmml" xref="S4.E6.m*******"><eq id="S4.E6.m*******.1.3.cmml" xref="S4.E6.m*******.1.3"></eq><apply id="S4.E6.m*******.1.4.cmml" xref="S4.E6.m*******.1.4"><csymbol cd="ambiguous" id="S4.E6.m*******.1.4.1.cmml" xref="S4.E6.m*******.1.4">superscript</csymbol><apply id="S4.E6.m*******.1.4.2.cmml" xref="S4.E6.m*******.1.4"><csymbol cd="ambiguous" id="S4.E6.m*******.1.4.2.1.cmml" xref="S4.E6.m*******.1.4">subscript</csymbol><ci id="S4.E6.m*******.1.4.2.2.cmml" xref="S4.E6.m*******.1.4.2.2">𝑦</ci><ci id="S4.E6.m*******.1.4.2.3.cmml" xref="S4.E6.m*******.1.4.2.3">𝑗</ci></apply><apply id="S4.E6.m*******.1.4.3.cmml" xref="S4.E6.m*******.1.4.3"><plus id="S4.E6.m*******.1.4.3.1.cmml" xref="S4.E6.m*******.1.4.3.1"></plus><ci id="S4.E6.m*******.1.4.3.2.cmml" xref="S4.E6.m*******.1.4.3.2">𝑘</ci><cn id="S4.E6.m*******.1.4.3.3.cmml" type="integer" xref="S4.E6.m*******.1.4.3.3">1</cn></apply></apply><apply id="S4.E6.m*******.1.2.cmml" xref="S4.E6.m*******.1.2"><plus id="S4.E6.m*******.1.2.3.cmml" xref="S4.E6.m*******.1.2.3"></plus><apply id="S4.E6.m*******.1.2.4.cmml" xref="S4.E6.m*******.1.2.4"><times id="S4.E6.m*******.1.2.4.1.cmml" xref="S4.E6.m*******.1.2.4.1"></times><apply id="S4.E6.m*******.1.2.4.2.cmml" xref="S4.E6.m*******.1.2.4.2"><csymbol cd="ambiguous" id="S4.E6.m*******.1.*******.cmml" xref="S4.E6.m*******.1.2.4.2">superscript</csymbol><apply id="S4.E6.m*******.1.*******.cmml" xref="S4.E6.m*******.1.2.4.2"><csymbol cd="ambiguous" id="S4.E6.m*******.1.*******.1.cmml" xref="S4.E6.m*******.1.2.4.2">subscript</csymbol><ci id="S4.E6.m*******.1.*******.2.cmml" xref="S4.E6.m*******.1.*******.2">𝛼</ci><ci id="S4.E6.m*******.1.*******.3.cmml" xref="S4.E6.m*******.1.*******.3">𝑗</ci></apply><ci id="S4.E6.m*******.1.*******.cmml" xref="S4.E6.m*******.1.*******">𝑘</ci></apply><apply id="S4.E6.m*******.1.2.4.3.cmml" xref="S4.E6.m*******.1.2.4.3"><csymbol cd="ambiguous" id="S4.E6.m*******.1.*******.cmml" xref="S4.E6.m*******.1.2.4.3">superscript</csymbol><apply id="S4.E6.m*******.1.*******.cmml" xref="S4.E6.m*******.1.2.4.3"><csymbol cd="ambiguous" id="S4.E6.m*******.1.*******.1.cmml" xref="S4.E6.m*******.1.2.4.3">subscript</csymbol><ci id="S4.E6.m*******.1.*******.2.cmml" xref="S4.E6.m*******.1.*******.2">𝑦</ci><ci id="S4.E6.m*******.1.*******.3.cmml" xref="S4.E6.m*******.1.*******.3">𝑗</ci></apply><ci id="S4.E6.m*******.1.*******.cmml" xref="S4.E6.m*******.1.*******">𝑘</ci></apply></apply><apply id="S4.E6.m*******.1.2.2.cmml" xref="S4.E6.m*******.1.2.2"><times id="S4.E6.m*******.*******.cmml" xref="S4.E6.m*******.*******"></times><apply id="S4.E6.m*******.*******.1.1.cmml" xref="S4.E6.m*******.*******.1"><minus id="S4.E6.m*******.*******.1.1.1.cmml" xref="S4.E6.m*******.*******.1.1.1"></minus><cn id="S4.E6.m*******.*******.1.1.2.cmml" type="integer" xref="S4.E6.m*******.*******.1.1.2">1</cn><apply id="S4.E6.m*******.*******.1.1.3.cmml" xref="S4.E6.m*******.*******.1.1.3"><csymbol cd="ambiguous" id="S4.E6.m*******.*******.*******.cmml" xref="S4.E6.m*******.*******.1.1.3">superscript</csymbol><apply id="S4.E6.m*******.*******.*******.cmml" xref="S4.E6.m*******.*******.1.1.3"><csymbol cd="ambiguous" id="S4.E6.m*******.*******.*******.1.cmml" xref="S4.E6.m*******.*******.1.1.3">subscript</csymbol><ci id="S4.E6.m*******.*******.*******.2.cmml" xref="S4.E6.m*******.*******.*******.2">𝛼</ci><ci id="S4.E6.m*******.*******.*******.3.cmml" xref="S4.E6.m*******.*******.*******.3">𝑗</ci></apply><ci id="S4.E6.m*******.*******.*******.cmml" xref="S4.E6.m*******.*******.*******">𝑘</ci></apply></apply><apply id="S4.E6.m*******.1.2.2.4.cmml" xref="S4.E6.m*******.1.2.2.4"><csymbol cd="ambiguous" id="S4.E6.m*******.1.*******.cmml" xref="S4.E6.m*******.1.2.2.4">subscript</csymbol><ci id="S4.E6.m*******.1.*******.cmml" xref="S4.E6.m*******.1.*******">𝑓</ci><apply id="S4.E6.m*******.1.*******.cmml" xref="S4.E6.m*******.1.*******"><csymbol cd="ambiguous" id="S4.E6.m*******.1.*******.1.cmml" xref="S4.E6.m*******.1.*******">superscript</csymbol><ci id="S4.E6.m*******.1.*******.2.cmml" xref="S4.E6.m*******.1.*******.2">𝜃</ci><ci id="S4.E6.m*******.1.*******.3.cmml" xref="S4.E6.m*******.1.*******.3">𝑘</ci></apply></apply><apply id="S4.E6.m*******.1.2.*******.cmml" xref="S4.E6.m*******.1.*******"><csymbol cd="ambiguous" id="S4.E6.m*******.1.2.*******.1.cmml" xref="S4.E6.m*******.1.*******">subscript</csymbol><ci id="S4.E6.m*******.1.2.*******.2.cmml" xref="S4.E6.m*******.1.2.*******.2">𝒙</ci><ci id="S4.E6.m*******.1.2.*******.3.cmml" xref="S4.E6.m*******.1.2.*******.3">𝑗</ci></apply></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.E6.m1.1c">y_{j}^{k+1}=\alpha_{j}^{k}y_{j}^{k}+(1-\alpha_{j}^{k})f_{\theta^{k}}({\bm{x}}_%
{j}),</annotation><annotation encoding="application/x-llamapun" id="S4.E6.m1.1d">italic_y start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k + 1 end_POSTSUPERSCRIPT = italic_α start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT italic_y start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT + ( 1 - italic_α start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT ) italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT end_POSTSUBSCRIPT ( bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT ) ,</annotation></semantics></math></td>
<td class="ltx_eqn_cell ltx_eqn_center_padright"></td>
<td class="ltx_eqn_cell ltx_eqn_eqno ltx_align_middle ltx_align_right" rowspan="1"><span class="ltx_tag ltx_tag_equation ltx_align_right">(6)</span></td>
</tr></tbody>
</table>
<p class="ltx_p" id="S4.SS2.p2.15">where <math alttext="\alpha_{j}^{k}\in[0,1]" class="ltx_Math" display="inline" id="S4.SS2.p2.9.m1.2"><semantics id="S4.SS2.p2.9.m1.2a"><mrow id="S4.SS2.p2.9.m1.2.3" xref="S4.SS2.p2.9.m1.2.3.cmml"><msubsup id="S4.SS2.p2.9.m1.2.3.2" xref="S4.SS2.p2.9.m1.2.3.2.cmml"><mi id="S4.SS2.p2.9.m1.*******.2" xref="S4.SS2.p2.9.m1.*******.2.cmml">α</mi><mi id="S4.SS2.p2.9.m1.*******.3" xref="S4.SS2.p2.9.m1.*******.3.cmml">j</mi><mi id="S4.SS2.p2.9.m1.*******" xref="S4.SS2.p2.9.m1.*******.cmml">k</mi></msubsup><mo id="S4.SS2.p2.9.m1.2.3.1" xref="S4.SS2.p2.9.m1.2.3.1.cmml">∈</mo><mrow id="S4.SS2.p2.9.m1.*******" xref="S4.SS2.p2.9.m1.*******.cmml"><mo id="S4.SS2.p2.9.m1.*******.1" stretchy="false" xref="S4.SS2.p2.9.m1.*******.cmml">[</mo><mn id="S4.SS2.p2.9.m1.1.1" xref="S4.SS2.p2.9.m1.1.1.cmml">0</mn><mo id="S4.SS2.p2.9.m1.2.*******" xref="S4.SS2.p2.9.m1.*******.cmml">,</mo><mn id="S4.SS2.p2.9.m1.2.2" xref="S4.SS2.p2.9.m1.2.2.cmml">1</mn><mo id="S4.SS2.p2.9.m1.2.*******" stretchy="false" xref="S4.SS2.p2.9.m1.*******.cmml">]</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.9.m1.2b"><apply id="S4.SS2.p2.9.m1.2.3.cmml" xref="S4.SS2.p2.9.m1.2.3"><in id="S4.SS2.p2.9.m1.2.3.1.cmml" xref="S4.SS2.p2.9.m1.2.3.1"></in><apply id="S4.SS2.p2.9.m1.2.3.2.cmml" xref="S4.SS2.p2.9.m1.2.3.2"><csymbol cd="ambiguous" id="S4.SS2.p2.9.m1.*******.cmml" xref="S4.SS2.p2.9.m1.2.3.2">superscript</csymbol><apply id="S4.SS2.p2.9.m1.*******.cmml" xref="S4.SS2.p2.9.m1.2.3.2"><csymbol cd="ambiguous" id="S4.SS2.p2.9.m1.*******.1.cmml" xref="S4.SS2.p2.9.m1.2.3.2">subscript</csymbol><ci id="S4.SS2.p2.9.m1.*******.2.cmml" xref="S4.SS2.p2.9.m1.*******.2">𝛼</ci><ci id="S4.SS2.p2.9.m1.*******.3.cmml" xref="S4.SS2.p2.9.m1.*******.3">𝑗</ci></apply><ci id="S4.SS2.p2.9.m1.*******.cmml" xref="S4.SS2.p2.9.m1.*******">𝑘</ci></apply><interval closure="closed" id="S4.SS2.p2.9.m1.*******.cmml" xref="S4.SS2.p2.9.m1.*******"><cn id="S4.SS2.p2.9.m1.1.1.cmml" type="integer" xref="S4.SS2.p2.9.m1.1.1">0</cn><cn id="S4.SS2.p2.9.m1.2.2.cmml" type="integer" xref="S4.SS2.p2.9.m1.2.2">1</cn></interval></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.9.m1.2c">\alpha_{j}^{k}\in[0,1]</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.9.m1.2d">italic_α start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT ∈ [ 0 , 1 ]</annotation></semantics></math> is the label confidence and <math alttext="y_{j}^{0}" class="ltx_Math" display="inline" id="S4.SS2.p2.10.m2.1"><semantics id="S4.SS2.p2.10.m2.1a"><msubsup id="S4.SS2.p2.10.m2.1.1" xref="S4.SS2.p2.10.m2.1.1.cmml"><mi id="S4.SS2.p2.10.m2.*******" xref="S4.SS2.p2.10.m2.*******.cmml">y</mi><mi id="S4.SS2.p2.10.m2.*******" xref="S4.SS2.p2.10.m2.*******.cmml">j</mi><mn id="S4.SS2.p2.10.m2.1.1.3" xref="S4.SS2.p2.10.m2.1.1.3.cmml">0</mn></msubsup><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.10.m2.1b"><apply id="S4.SS2.p2.10.m2.1.1.cmml" xref="S4.SS2.p2.10.m2.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.10.m2.1.1.1.cmml" xref="S4.SS2.p2.10.m2.1.1">superscript</csymbol><apply id="S4.SS2.p2.10.m2.1.1.2.cmml" xref="S4.SS2.p2.10.m2.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.10.m2.*******.cmml" xref="S4.SS2.p2.10.m2.1.1">subscript</csymbol><ci id="S4.SS2.p2.10.m2.*******.cmml" xref="S4.SS2.p2.10.m2.*******">𝑦</ci><ci id="S4.SS2.p2.10.m2.*******.cmml" xref="S4.SS2.p2.10.m2.*******">𝑗</ci></apply><cn id="S4.SS2.p2.10.m2.1.1.3.cmml" type="integer" xref="S4.SS2.p2.10.m2.1.1.3">0</cn></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.10.m2.1c">y_{j}^{0}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.10.m2.1d">italic_y start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT 0 end_POSTSUPERSCRIPT</annotation></semantics></math> is the initial noisy label of training data. As shown in Eq. (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#S4.E6" title="Equation 6 ‣ 4.2 Iterative Refinement Labeling (RA-Labeling) ‣ 4 LARA: The Proposed Framework ‣ Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling"><span class="ltx_text ltx_ref_tag">6</span></a>), we apply the exponential moving average to mitigate the damage of under-predicted labels of <em class="ltx_emph ltx_font_italic" id="S4.SS2.p2.15.1">noisy samples</em> and thus the confidence score <math alttext="\alpha_{j}^{k}" class="ltx_Math" display="inline" id="S4.SS2.p2.11.m3.1"><semantics id="S4.SS2.p2.11.m3.1a"><msubsup id="S4.SS2.p2.11.m3.1.1" xref="S4.SS2.p2.11.m3.1.1.cmml"><mi id="S4.SS2.p2.11.m3.*******" xref="S4.SS2.p2.11.m3.*******.cmml">α</mi><mi id="S4.SS2.p2.11.m3.*******" xref="S4.SS2.p2.11.m3.*******.cmml">j</mi><mi id="S4.SS2.p2.11.m3.1.1.3" xref="S4.SS2.p2.11.m3.1.1.3.cmml">k</mi></msubsup><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.11.m3.1b"><apply id="S4.SS2.p2.11.m3.1.1.cmml" xref="S4.SS2.p2.11.m3.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.11.m3.1.1.1.cmml" xref="S4.SS2.p2.11.m3.1.1">superscript</csymbol><apply id="S4.SS2.p2.11.m3.1.1.2.cmml" xref="S4.SS2.p2.11.m3.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.11.m3.*******.cmml" xref="S4.SS2.p2.11.m3.1.1">subscript</csymbol><ci id="S4.SS2.p2.11.m3.*******.cmml" xref="S4.SS2.p2.11.m3.*******">𝛼</ci><ci id="S4.SS2.p2.11.m3.*******.cmml" xref="S4.SS2.p2.11.m3.*******">𝑗</ci></apply><ci id="S4.SS2.p2.11.m3.1.1.3.cmml" xref="S4.SS2.p2.11.m3.1.1.3">𝑘</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.11.m3.1c">\alpha_{j}^{k}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.11.m3.1d">italic_α start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT</annotation></semantics></math> is a crucial parameter in this self-adaptive training scheme. We propose to update <math alttext="\alpha_{j}^{k}" class="ltx_Math" display="inline" id="S4.SS2.p2.12.m4.1"><semantics id="S4.SS2.p2.12.m4.1a"><msubsup id="S4.SS2.p2.12.m4.1.1" xref="S4.SS2.p2.12.m4.1.1.cmml"><mi id="S4.SS2.p2.12.m4.*******" xref="S4.SS2.p2.12.m4.*******.cmml">α</mi><mi id="S4.SS2.p2.12.m4.*******" xref="S4.SS2.p2.12.m4.*******.cmml">j</mi><mi id="S4.SS2.p2.12.m4.1.1.3" xref="S4.SS2.p2.12.m4.1.1.3.cmml">k</mi></msubsup><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.12.m4.1b"><apply id="S4.SS2.p2.12.m4.1.1.cmml" xref="S4.SS2.p2.12.m4.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.12.m4.1.1.1.cmml" xref="S4.SS2.p2.12.m4.1.1">superscript</csymbol><apply id="S4.SS2.p2.12.m4.1.1.2.cmml" xref="S4.SS2.p2.12.m4.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.12.m4.*******.cmml" xref="S4.SS2.p2.12.m4.1.1">subscript</csymbol><ci id="S4.SS2.p2.12.m4.*******.cmml" xref="S4.SS2.p2.12.m4.*******">𝛼</ci><ci id="S4.SS2.p2.12.m4.*******.cmml" xref="S4.SS2.p2.12.m4.*******">𝑗</ci></apply><ci id="S4.SS2.p2.12.m4.1.1.3.cmml" xref="S4.SS2.p2.12.m4.1.1.3">𝑘</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.12.m4.1c">\alpha_{j}^{k}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.12.m4.1d">italic_α start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT</annotation></semantics></math> according to the training loss at the current step to alleviate the instability issue of using instantaneous prediction under <em class="ltx_emph ltx_font_italic" id="S4.SS2.p2.15.2">noisy samples</em>. <math alttext="\alpha_{j}^{k}" class="ltx_Math" display="inline" id="S4.SS2.p2.13.m5.1"><semantics id="S4.SS2.p2.13.m5.1a"><msubsup id="S4.SS2.p2.13.m5.1.1" xref="S4.SS2.p2.13.m5.1.1.cmml"><mi id="S4.SS2.p2.13.m5.*******" xref="S4.SS2.p2.13.m5.*******.cmml">α</mi><mi id="S4.SS2.p2.13.m5.*******" xref="S4.SS2.p2.13.m5.*******.cmml">j</mi><mi id="S4.SS2.p2.13.m5.1.1.3" xref="S4.SS2.p2.13.m5.1.1.3.cmml">k</mi></msubsup><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.13.m5.1b"><apply id="S4.SS2.p2.13.m5.1.1.cmml" xref="S4.SS2.p2.13.m5.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.13.m5.1.1.1.cmml" xref="S4.SS2.p2.13.m5.1.1">superscript</csymbol><apply id="S4.SS2.p2.13.m5.1.1.2.cmml" xref="S4.SS2.p2.13.m5.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.13.m5.*******.cmml" xref="S4.SS2.p2.13.m5.1.1">subscript</csymbol><ci id="S4.SS2.p2.13.m5.*******.cmml" xref="S4.SS2.p2.13.m5.*******">𝛼</ci><ci id="S4.SS2.p2.13.m5.*******.cmml" xref="S4.SS2.p2.13.m5.*******">𝑗</ci></apply><ci id="S4.SS2.p2.13.m5.1.1.3.cmml" xref="S4.SS2.p2.13.m5.1.1.3">𝑘</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.13.m5.1c">\alpha_{j}^{k}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.13.m5.1d">italic_α start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT</annotation></semantics></math> of the sample <math alttext="{\bm{x}}_{j}" class="ltx_Math" display="inline" id="S4.SS2.p2.14.m6.1"><semantics id="S4.SS2.p2.14.m6.1a"><msub id="S4.SS2.p2.14.m6.1.1" xref="S4.SS2.p2.14.m6.1.1.cmml"><mi id="S4.SS2.p2.14.m6.1.1.2" xref="S4.SS2.p2.14.m6.1.1.2.cmml">𝒙</mi><mi id="S4.SS2.p2.14.m6.1.1.3" xref="S4.SS2.p2.14.m6.1.1.3.cmml">j</mi></msub><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.14.m6.1b"><apply id="S4.SS2.p2.14.m6.1.1.cmml" xref="S4.SS2.p2.14.m6.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.14.m6.1.1.1.cmml" xref="S4.SS2.p2.14.m6.1.1">subscript</csymbol><ci id="S4.SS2.p2.14.m6.1.1.2.cmml" xref="S4.SS2.p2.14.m6.1.1.2">𝒙</ci><ci id="S4.SS2.p2.14.m6.1.1.3.cmml" xref="S4.SS2.p2.14.m6.1.1.3">𝑗</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.14.m6.1c">{\bm{x}}_{j}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.14.m6.1d">bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT</annotation></semantics></math> at the <math alttext="k" class="ltx_Math" display="inline" id="S4.SS2.p2.15.m7.1"><semantics id="S4.SS2.p2.15.m7.1a"><mi id="S4.SS2.p2.15.m7.1.1" xref="S4.SS2.p2.15.m7.1.1.cmml">k</mi><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.15.m7.1b"><ci id="S4.SS2.p2.15.m7.1.1.cmml" xref="S4.SS2.p2.15.m7.1.1">𝑘</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.15.m7.1c">k</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.15.m7.1d">italic_k</annotation></semantics></math>-th step is calculated by:</p>
<table class="ltx_equation ltx_eqn_table" id="S4.E7">
<tbody><tr class="ltx_equation ltx_eqn_row ltx_align_baseline">
<td class="ltx_eqn_cell ltx_eqn_center_padleft"></td>
<td class="ltx_eqn_cell ltx_align_center"><math alttext="\alpha_{j}^{k}=1\left\{\mathcal{L}\left(f_{\theta^{k}}({\bm{x}}_{j}),y_{j}^{k}%
\right)\leq\epsilon^{k}\right\}," class="ltx_Math" display="block" id="S4.E7.m1.1"><semantics id="S4.E7.m1.1a"><mrow id="S4.E7.m*******" xref="S4.E7.m*******.1.cmml"><mrow id="S4.E7.m*******.1" xref="S4.E7.m*******.1.cmml"><msubsup id="S4.E7.m*******.1.3" xref="S4.E7.m*******.1.3.cmml"><mi id="S4.E7.m1.1.1.*******.2" xref="S4.E7.m1.1.1.*******.2.cmml">α</mi><mi id="S4.E7.m1.1.1.*******.3" xref="S4.E7.m1.1.1.*******.3.cmml">j</mi><mi id="S4.E7.m*******.1.3.3" xref="S4.E7.m*******.1.3.3.cmml">k</mi></msubsup><mo id="S4.E7.m*******.1.2" xref="S4.E7.m*******.1.2.cmml">=</mo><mrow id="S4.E7.m*******.1.1" xref="S4.E7.m*******.1.1.cmml"><mn id="S4.E7.m*******.1.1.3" xref="S4.E7.m*******.1.1.3.cmml">1</mn><mo id="S4.E7.m*******.1.1.2" xref="S4.E7.m*******.1.1.2.cmml">⁢</mo><mrow id="S4.E7.m*******.*******" xref="S4.E7.m*******.*******.cmml"><mo id="S4.E7.m*******.*******.2" xref="S4.E7.m*******.*******.cmml">{</mo><mrow id="S4.E7.m*******.*******.1" xref="S4.E7.m*******.*******.1.cmml"><mrow id="S4.E7.m*******.*******.1.2" xref="S4.E7.m*******.*******.1.2.cmml"><mi class="ltx_font_mathcaligraphic" id="S4.E7.m*******.*******.1.2.4" xref="S4.E7.m*******.*******.1.2.4.cmml">ℒ</mi><mo id="S4.E7.m*******.*******.1.2.3" xref="S4.E7.m*******.*******.1.2.3.cmml">⁢</mo><mrow id="S4.E7.m*******.*******.*******" xref="S4.E7.m*******.*******.*******.cmml"><mo id="S4.E7.m*******.*******.1.*******" xref="S4.E7.m*******.*******.*******.cmml">(</mo><mrow id="S4.E7.m*******.*******.*******.1" xref="S4.E7.m*******.*******.*******.1.cmml"><msub id="S4.E7.m*******.*******.*******.1.3" xref="S4.E7.m*******.*******.*******.1.3.cmml"><mi id="S4.E7.m*******.*******.1.1.1.*******" xref="S4.E7.m*******.*******.1.1.1.*******.cmml">f</mi><msup id="S4.E7.m*******.*******.*******.1.3.3" xref="S4.E7.m*******.*******.*******.1.3.3.cmml"><mi id="S4.E7.m*******.*******.*******.1.3.3.2" xref="S4.E7.m*******.*******.*******.1.3.3.2.cmml">θ</mi><mi id="S4.E7.m*******.*******.*******.1.3.3.3" xref="S4.E7.m*******.*******.*******.1.3.3.3.cmml">k</mi></msup></msub><mo id="S4.E7.m*******.*******.*******.1.2" xref="S4.E7.m*******.*******.*******.1.2.cmml">⁢</mo><mrow id="S4.E7.m*******.*******.*******.1.1.1" xref="S4.E7.m*******.*******.*******.*******.cmml"><mo id="S4.E7.m*******.*******.*******.*******" stretchy="false" xref="S4.E7.m*******.*******.*******.*******.cmml">(</mo><msub id="S4.E7.m*******.*******.*******.*******" xref="S4.E7.m*******.*******.*******.*******.cmml"><mi id="S4.E7.m*******.*******.*******.*******.2" xref="S4.E7.m*******.*******.*******.*******.2.cmml">𝒙</mi><mi id="S4.E7.m*******.*******.*******.*******.3" xref="S4.E7.m*******.*******.*******.*******.3.cmml">j</mi></msub><mo id="S4.E7.m*******.*******.*******.*******" stretchy="false" xref="S4.E7.m*******.*******.*******.*******.cmml">)</mo></mrow></mrow><mo id="S4.E7.m*******.*******.1.*******" xref="S4.E7.m*******.*******.*******.cmml">,</mo><msubsup id="S4.E7.m*******.*******.1.*******" xref="S4.E7.m*******.*******.1.*******.cmml"><mi id="S4.E7.m*******.*******.1.*******.2.2" xref="S4.E7.m*******.*******.1.*******.2.2.cmml">y</mi><mi id="S4.E7.m*******.*******.1.*******.2.3" xref="S4.E7.m*******.*******.1.*******.2.3.cmml">j</mi><mi id="S4.E7.m*******.*******.1.*******.3" xref="S4.E7.m*******.*******.1.*******.3.cmml">k</mi></msubsup><mo id="S4.E7.m*******.*******.1.*******" xref="S4.E7.m*******.*******.*******.cmml">)</mo></mrow></mrow><mo id="S4.E7.m*******.*******.1.3" xref="S4.E7.m*******.*******.1.3.cmml">≤</mo><msup id="S4.E7.m*******.*******.1.4" xref="S4.E7.m*******.*******.1.4.cmml"><mi id="S4.E7.m*******.*******.1.4.2" xref="S4.E7.m*******.*******.1.4.2.cmml">ϵ</mi><mi id="S4.E7.m*******.*******.1.4.3" xref="S4.E7.m*******.*******.1.4.3.cmml">k</mi></msup></mrow><mo id="S4.E7.m*******.*******.3" xref="S4.E7.m*******.*******.cmml">}</mo></mrow></mrow></mrow><mo id="S4.E7.m*******.2" xref="S4.E7.m*******.1.cmml">,</mo></mrow><annotation-xml encoding="MathML-Content" id="S4.E7.m1.1b"><apply id="S4.E7.m*******.1.cmml" xref="S4.E7.m*******"><eq id="S4.E7.m*******.1.2.cmml" xref="S4.E7.m*******.1.2"></eq><apply id="S4.E7.m*******.1.3.cmml" xref="S4.E7.m*******.1.3"><csymbol cd="ambiguous" id="S4.E7.m*******.1.3.1.cmml" xref="S4.E7.m*******.1.3">superscript</csymbol><apply id="S4.E7.m1.1.1.*******.cmml" xref="S4.E7.m*******.1.3"><csymbol cd="ambiguous" id="S4.E7.m1.1.1.*******.1.cmml" xref="S4.E7.m*******.1.3">subscript</csymbol><ci id="S4.E7.m1.1.1.*******.2.cmml" xref="S4.E7.m1.1.1.*******.2">𝛼</ci><ci id="S4.E7.m1.1.1.*******.3.cmml" xref="S4.E7.m1.1.1.*******.3">𝑗</ci></apply><ci id="S4.E7.m*******.1.3.3.cmml" xref="S4.E7.m*******.1.3.3">𝑘</ci></apply><apply id="S4.E7.m*******.1.1.cmml" xref="S4.E7.m*******.1.1"><times id="S4.E7.m*******.1.1.2.cmml" xref="S4.E7.m*******.1.1.2"></times><cn id="S4.E7.m*******.1.1.3.cmml" type="integer" xref="S4.E7.m*******.1.1.3">1</cn><set id="S4.E7.m*******.*******.cmml" xref="S4.E7.m*******.*******"><apply id="S4.E7.m*******.*******.1.cmml" xref="S4.E7.m*******.*******.1"><leq id="S4.E7.m*******.*******.1.3.cmml" xref="S4.E7.m*******.*******.1.3"></leq><apply id="S4.E7.m*******.*******.1.2.cmml" xref="S4.E7.m*******.*******.1.2"><times id="S4.E7.m*******.*******.1.2.3.cmml" xref="S4.E7.m*******.*******.1.2.3"></times><ci id="S4.E7.m*******.*******.1.2.4.cmml" xref="S4.E7.m*******.*******.1.2.4">ℒ</ci><interval closure="open" id="S4.E7.m*******.*******.*******.cmml" xref="S4.E7.m*******.*******.*******"><apply id="S4.E7.m*******.*******.*******.1.cmml" xref="S4.E7.m*******.*******.*******.1"><times id="S4.E7.m*******.*******.*******.1.2.cmml" xref="S4.E7.m*******.*******.*******.1.2"></times><apply id="S4.E7.m*******.*******.*******.1.3.cmml" xref="S4.E7.m*******.*******.*******.1.3"><csymbol cd="ambiguous" id="S4.E7.m*******.*******.*******.1.3.1.cmml" xref="S4.E7.m*******.*******.*******.1.3">subscript</csymbol><ci id="S4.E7.m*******.*******.1.1.1.*******.cmml" xref="S4.E7.m*******.*******.1.1.1.*******">𝑓</ci><apply id="S4.E7.m*******.*******.*******.1.3.3.cmml" xref="S4.E7.m*******.*******.*******.1.3.3"><csymbol cd="ambiguous" id="S4.E7.m*******.*******.*******.1.3.3.1.cmml" xref="S4.E7.m*******.*******.*******.1.3.3">superscript</csymbol><ci id="S4.E7.m*******.*******.*******.1.3.3.2.cmml" xref="S4.E7.m*******.*******.*******.1.3.3.2">𝜃</ci><ci id="S4.E7.m*******.*******.*******.1.3.3.3.cmml" xref="S4.E7.m*******.*******.*******.1.3.3.3">𝑘</ci></apply></apply><apply id="S4.E7.m*******.*******.*******.*******.cmml" xref="S4.E7.m*******.*******.*******.1.1.1"><csymbol cd="ambiguous" id="S4.E7.m*******.*******.*******.*******.1.cmml" xref="S4.E7.m*******.*******.*******.1.1.1">subscript</csymbol><ci id="S4.E7.m*******.*******.*******.*******.2.cmml" xref="S4.E7.m*******.*******.*******.*******.2">𝒙</ci><ci id="S4.E7.m*******.*******.*******.*******.3.cmml" xref="S4.E7.m*******.*******.*******.*******.3">𝑗</ci></apply></apply><apply id="S4.E7.m*******.*******.1.*******.cmml" xref="S4.E7.m*******.*******.1.*******"><csymbol cd="ambiguous" id="S4.E7.m*******.*******.1.2.*******.cmml" xref="S4.E7.m*******.*******.1.*******">superscript</csymbol><apply id="S4.E7.m*******.*******.1.*******.2.cmml" xref="S4.E7.m*******.*******.1.*******"><csymbol cd="ambiguous" id="S4.E7.m*******.*******.1.2.2.*******.cmml" xref="S4.E7.m*******.*******.1.*******">subscript</csymbol><ci id="S4.E7.m*******.*******.1.*******.2.2.cmml" xref="S4.E7.m*******.*******.1.*******.2.2">𝑦</ci><ci id="S4.E7.m*******.*******.1.*******.2.3.cmml" xref="S4.E7.m*******.*******.1.*******.2.3">𝑗</ci></apply><ci id="S4.E7.m*******.*******.1.*******.3.cmml" xref="S4.E7.m*******.*******.1.*******.3">𝑘</ci></apply></interval></apply><apply id="S4.E7.m*******.*******.1.4.cmml" xref="S4.E7.m*******.*******.1.4"><csymbol cd="ambiguous" id="S4.E7.m*******.*******.1.4.1.cmml" xref="S4.E7.m*******.*******.1.4">superscript</csymbol><ci id="S4.E7.m*******.*******.1.4.2.cmml" xref="S4.E7.m*******.*******.1.4.2">italic-ϵ</ci><ci id="S4.E7.m*******.*******.1.4.3.cmml" xref="S4.E7.m*******.*******.1.4.3">𝑘</ci></apply></apply></set></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.E7.m1.1c">\alpha_{j}^{k}=1\left\{\mathcal{L}\left(f_{\theta^{k}}({\bm{x}}_{j}),y_{j}^{k}%
\right)\leq\epsilon^{k}\right\},</annotation><annotation encoding="application/x-llamapun" id="S4.E7.m1.1d">italic_α start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT = 1 { caligraphic_L ( italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT end_POSTSUBSCRIPT ( bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT ) , italic_y start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT ) ≤ italic_ϵ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT } ,</annotation></semantics></math></td>
<td class="ltx_eqn_cell ltx_eqn_center_padright"></td>
<td class="ltx_eqn_cell ltx_eqn_eqno ltx_align_middle ltx_align_right" rowspan="1"><span class="ltx_tag ltx_tag_equation ltx_align_right">(7)</span></td>
</tr></tbody>
</table>
<p class="ltx_p" id="S4.SS2.p2.21">where <math alttext="1\left\{\cdot\right\}" class="ltx_Math" display="inline" id="S4.SS2.p2.16.m1.1"><semantics id="S4.SS2.p2.16.m1.1a"><mrow id="S4.SS2.p2.16.m1.1.2" xref="S4.SS2.p2.16.m1.1.2.cmml"><mn id="S4.SS2.p2.16.m*******" xref="S4.SS2.p2.16.m*******.cmml">1</mn><mo id="S4.SS2.p2.16.m*******" xref="S4.SS2.p2.16.m*******.cmml">⁢</mo><mrow id="S4.SS2.p2.16.m*******.2" xref="S4.SS2.p2.16.m*******.1.cmml"><mo id="S4.SS2.p2.16.m*******.2.1" xref="S4.SS2.p2.16.m*******.1.cmml">{</mo><mo id="S4.SS2.p2.16.m1.1.1" lspace="0em" rspace="0em" xref="S4.SS2.p2.16.m1.1.1.cmml">⋅</mo><mo id="S4.SS2.p2.16.m*******.2.2" xref="S4.SS2.p2.16.m*******.1.cmml">}</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.16.m1.1b"><apply id="S4.SS2.p2.16.m1.1.2.cmml" xref="S4.SS2.p2.16.m1.1.2"><times id="S4.SS2.p2.16.m*******.cmml" xref="S4.SS2.p2.16.m*******"></times><cn id="S4.SS2.p2.16.m*******.cmml" type="integer" xref="S4.SS2.p2.16.m*******">1</cn><set id="S4.SS2.p2.16.m*******.1.cmml" xref="S4.SS2.p2.16.m*******.2"><ci id="S4.SS2.p2.16.m1.1.1.cmml" xref="S4.SS2.p2.16.m1.1.1">⋅</ci></set></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.16.m1.1c">1\left\{\cdot\right\}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.16.m1.1d">1 { ⋅ }</annotation></semantics></math> denotes the indicator function, <math alttext="\epsilon^{k}" class="ltx_Math" display="inline" id="S4.SS2.p2.17.m2.1"><semantics id="S4.SS2.p2.17.m2.1a"><msup id="S4.SS2.p2.17.m2.1.1" xref="S4.SS2.p2.17.m2.1.1.cmml"><mi id="S4.SS2.p2.17.m2.1.1.2" xref="S4.SS2.p2.17.m2.1.1.2.cmml">ϵ</mi><mi id="S4.SS2.p2.17.m2.1.1.3" xref="S4.SS2.p2.17.m2.1.1.3.cmml">k</mi></msup><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.17.m2.1b"><apply id="S4.SS2.p2.17.m2.1.1.cmml" xref="S4.SS2.p2.17.m2.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.17.m2.1.1.1.cmml" xref="S4.SS2.p2.17.m2.1.1">superscript</csymbol><ci id="S4.SS2.p2.17.m2.1.1.2.cmml" xref="S4.SS2.p2.17.m2.1.1.2">italic-ϵ</ci><ci id="S4.SS2.p2.17.m2.1.1.3.cmml" xref="S4.SS2.p2.17.m2.1.1.3">𝑘</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.17.m2.1c">\epsilon^{k}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.17.m2.1d">italic_ϵ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT</annotation></semantics></math> is a hyper-parameter to control label confidence, and <math alttext="\mathcal{L}(\cdot,\cdot)" class="ltx_Math" display="inline" id="S4.SS2.p2.18.m3.2"><semantics id="S4.SS2.p2.18.m3.2a"><mrow id="S4.SS2.p2.18.m3.2.3" xref="S4.SS2.p2.18.m3.2.3.cmml"><mi class="ltx_font_mathcaligraphic" id="S4.SS2.p2.18.m3.2.3.2" xref="S4.SS2.p2.18.m3.2.3.2.cmml">ℒ</mi><mo id="S4.SS2.p2.18.m3.2.3.1" xref="S4.SS2.p2.18.m3.2.3.1.cmml">⁢</mo><mrow id="S4.SS2.p2.18.m3.*******" xref="S4.SS2.p2.18.m3.*******.cmml"><mo id="S4.SS2.p2.18.m3.*******.1" stretchy="false" xref="S4.SS2.p2.18.m3.*******.cmml">(</mo><mo id="S4.SS2.p2.18.m3.1.1" lspace="0em" rspace="0em" xref="S4.SS2.p2.18.m3.1.1.cmml">⋅</mo><mo id="S4.SS2.p2.18.m3.2.*******" rspace="0em" xref="S4.SS2.p2.18.m3.*******.cmml">,</mo><mo id="S4.SS2.p2.18.m3.2.2" lspace="0em" rspace="0em" xref="S4.SS2.p2.18.m3.2.2.cmml">⋅</mo><mo id="S4.SS2.p2.18.m3.2.*******" stretchy="false" xref="S4.SS2.p2.18.m3.*******.cmml">)</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.18.m3.2b"><apply id="S4.SS2.p2.18.m3.2.3.cmml" xref="S4.SS2.p2.18.m3.2.3"><times id="S4.SS2.p2.18.m3.2.3.1.cmml" xref="S4.SS2.p2.18.m3.2.3.1"></times><ci id="S4.SS2.p2.18.m3.2.3.2.cmml" xref="S4.SS2.p2.18.m3.2.3.2">ℒ</ci><interval closure="open" id="S4.SS2.p2.18.m3.*******.cmml" xref="S4.SS2.p2.18.m3.*******"><ci id="S4.SS2.p2.18.m3.1.1.cmml" xref="S4.SS2.p2.18.m3.1.1">⋅</ci><ci id="S4.SS2.p2.18.m3.2.2.cmml" xref="S4.SS2.p2.18.m3.2.2">⋅</ci></interval></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.18.m3.2c">\mathcal{L}(\cdot,\cdot)</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.18.m3.2d">caligraphic_L ( ⋅ , ⋅ )</annotation></semantics></math> denotes the supervised loss function, <em class="ltx_emph ltx_font_italic" id="S4.SS2.p2.21.1">e.g.</em>, the cross-entropy loss.
In order to develop a more coherent predictor that improves its ability to evaluate the consistency of <em class="ltx_emph ltx_font_italic" id="S4.SS2.p2.21.2">noisy samples</em>, we adaptively set the value of <math alttext="\epsilon^{k}" class="ltx_Math" display="inline" id="S4.SS2.p2.19.m4.1"><semantics id="S4.SS2.p2.19.m4.1a"><msup id="S4.SS2.p2.19.m4.1.1" xref="S4.SS2.p2.19.m4.1.1.cmml"><mi id="S4.SS2.p2.19.m4.1.1.2" xref="S4.SS2.p2.19.m4.1.1.2.cmml">ϵ</mi><mi id="S4.SS2.p2.19.m4.1.1.3" xref="S4.SS2.p2.19.m4.1.1.3.cmml">k</mi></msup><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.19.m4.1b"><apply id="S4.SS2.p2.19.m4.1.1.cmml" xref="S4.SS2.p2.19.m4.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.19.m4.1.1.1.cmml" xref="S4.SS2.p2.19.m4.1.1">superscript</csymbol><ci id="S4.SS2.p2.19.m4.1.1.2.cmml" xref="S4.SS2.p2.19.m4.1.1.2">italic-ϵ</ci><ci id="S4.SS2.p2.19.m4.1.1.3.cmml" xref="S4.SS2.p2.19.m4.1.1.3">𝑘</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.19.m4.1c">\epsilon^{k}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.19.m4.1d">italic_ϵ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT</annotation></semantics></math> to roughly adjust the ratio of labels in training data, denoted by <math alttext="r" class="ltx_Math" display="inline" id="S4.SS2.p2.20.m5.1"><semantics id="S4.SS2.p2.20.m5.1a"><mi id="S4.SS2.p2.20.m5.1.1" xref="S4.SS2.p2.20.m5.1.1.cmml">r</mi><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.20.m5.1b"><ci id="S4.SS2.p2.20.m5.1.1.cmml" xref="S4.SS2.p2.20.m5.1.1">𝑟</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.20.m5.1c">r</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.20.m5.1d">italic_r</annotation></semantics></math>.
We also perform the hyperparameter study in Sec. <a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#S5.SS3" title="5.3 Experimental Analysis ‣ 5 Experiments ‣ Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling"><span class="ltx_text ltx_ref_tag">5.3</span></a> and empirically identify that RA-Labeling achieves a relatively stable performance when <math alttext="r&lt;10\%" class="ltx_Math" display="inline" id="S4.SS2.p2.21.m6.1"><semantics id="S4.SS2.p2.21.m6.1a"><mrow id="S4.SS2.p2.21.m6.1.1" xref="S4.SS2.p2.21.m6.1.1.cmml"><mi id="S4.SS2.p2.21.m6.1.1.2" xref="S4.SS2.p2.21.m6.1.1.2.cmml">r</mi><mo id="S4.SS2.p2.21.m6.1.1.1" xref="S4.SS2.p2.21.m6.1.1.1.cmml">&lt;</mo><mrow id="S4.SS2.p2.21.m6.1.1.3" xref="S4.SS2.p2.21.m6.1.1.3.cmml"><mn id="S4.SS2.p2.21.m6.*******" xref="S4.SS2.p2.21.m6.*******.cmml">10</mn><mo id="S4.SS2.p2.21.m6.*******" xref="S4.SS2.p2.21.m6.*******.cmml">%</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.21.m6.1b"><apply id="S4.SS2.p2.21.m6.1.1.cmml" xref="S4.SS2.p2.21.m6.1.1"><lt id="S4.SS2.p2.21.m6.1.1.1.cmml" xref="S4.SS2.p2.21.m6.1.1.1"></lt><ci id="S4.SS2.p2.21.m6.1.1.2.cmml" xref="S4.SS2.p2.21.m6.1.1.2">𝑟</ci><apply id="S4.SS2.p2.21.m6.1.1.3.cmml" xref="S4.SS2.p2.21.m6.1.1.3"><csymbol cd="latexml" id="S4.SS2.p2.21.m6.*******.cmml" xref="S4.SS2.p2.21.m6.*******">percent</csymbol><cn id="S4.SS2.p2.21.m6.*******.cmml" type="integer" xref="S4.SS2.p2.21.m6.*******">10</cn></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.21.m6.1c">r&lt;10\%</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.21.m6.1d">italic_r &lt; 10 %</annotation></semantics></math>.</p>
</div>
<div class="ltx_para" id="S4.SS2.p3">
<p class="ltx_p" id="S4.SS2.p3.6">Different from the conventional noisy labeling methods <cite class="ltx_cite ltx_citemacro_cite">Song <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib25" title="">2022</a>)</cite>, we further propose a combination method to integrate the learned predictor. Specifically, after running <math alttext="K" class="ltx_Math" display="inline" id="S4.SS2.p3.1.m1.1"><semantics id="S4.SS2.p3.1.m1.1a"><mi id="S4.SS2.p3.1.m1.1.1" xref="S4.SS2.p3.1.m1.1.1.cmml">K</mi><annotation-xml encoding="MathML-Content" id="S4.SS2.p3.1.m1.1b"><ci id="S4.SS2.p3.1.m1.1.1.cmml" xref="S4.SS2.p3.1.m1.1.1">𝐾</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p3.1.m1.1c">K</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p3.1.m1.1d">italic_K</annotation></semantics></math> iterations of iterative refinement labeling, we obtain <math alttext="K+1" class="ltx_Math" display="inline" id="S4.SS2.p3.2.m2.1"><semantics id="S4.SS2.p3.2.m2.1a"><mrow id="S4.SS2.p3.2.m2.1.1" xref="S4.SS2.p3.2.m2.1.1.cmml"><mi id="S4.SS2.p3.2.m2.1.1.2" xref="S4.SS2.p3.2.m2.1.1.2.cmml">K</mi><mo id="S4.SS2.p3.2.m2.1.1.1" xref="S4.SS2.p3.2.m2.1.1.1.cmml">+</mo><mn id="S4.SS2.p3.2.m2.1.1.3" xref="S4.SS2.p3.2.m2.1.1.3.cmml">1</mn></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p3.2.m2.1b"><apply id="S4.SS2.p3.2.m2.1.1.cmml" xref="S4.SS2.p3.2.m2.1.1"><plus id="S4.SS2.p3.2.m2.1.1.1.cmml" xref="S4.SS2.p3.2.m2.1.1.1"></plus><ci id="S4.SS2.p3.2.m2.1.1.2.cmml" xref="S4.SS2.p3.2.m2.1.1.2">𝐾</ci><cn id="S4.SS2.p3.2.m2.1.1.3.cmml" type="integer" xref="S4.SS2.p3.2.m2.1.1.3">1</cn></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p3.2.m2.1c">K+1</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p3.2.m2.1d">italic_K + 1</annotation></semantics></math> iterated predictors <math alttext="\{f_{\theta_{0}},\cdot\cdot\cdot,f_{\theta_{K}}\}" class="ltx_Math" display="inline" id="S4.SS2.p3.3.m3.3"><semantics id="S4.SS2.p3.3.m3.3a"><mrow id="S4.SS2.p3.3.m*******" xref="S4.SS2.p3.3.m*******.cmml"><mo id="S4.SS2.p3.3.m3.*******" stretchy="false" xref="S4.SS2.p3.3.m*******.cmml">{</mo><msub id="S4.SS2.p3.3.m3.*******" xref="S4.SS2.p3.3.m3.*******.cmml"><mi id="S4.SS2.p3.3.m3.*******.2" xref="S4.SS2.p3.3.m3.*******.2.cmml">f</mi><msub id="S4.SS2.p3.3.m3.*******.3" xref="S4.SS2.p3.3.m3.*******.3.cmml"><mi id="S4.SS2.p3.3.m3.*******.3.2" xref="S4.SS2.p3.3.m3.*******.3.2.cmml">θ</mi><mn id="S4.SS2.p3.3.m3.*******.3.3" xref="S4.SS2.p3.3.m3.*******.3.3.cmml">0</mn></msub></msub><mo id="S4.SS2.p3.3.m3.*******" xref="S4.SS2.p3.3.m*******.cmml">,</mo><mi id="S4.SS2.p3.3.m3.1.1" mathvariant="normal" xref="S4.SS2.p3.3.m3.1.1.cmml">⋯</mi><mo id="S4.SS2.p3.3.m*******.5" xref="S4.SS2.p3.3.m*******.cmml">,</mo><msub id="S4.SS2.p3.3.m3.*******" xref="S4.SS2.p3.3.m3.*******.cmml"><mi id="S4.SS2.p3.3.m3.*******.2" xref="S4.SS2.p3.3.m3.*******.2.cmml">f</mi><msub id="S4.SS2.p3.3.m3.*******.3" xref="S4.SS2.p3.3.m3.*******.3.cmml"><mi id="S4.SS2.p3.3.m3.*******.3.2" xref="S4.SS2.p3.3.m3.*******.3.2.cmml">θ</mi><mi id="S4.SS2.p3.3.m3.*******.3.3" xref="S4.SS2.p3.3.m3.*******.3.3.cmml">K</mi></msub></msub><mo id="S4.SS2.p3.3.m3.*******" stretchy="false" xref="S4.SS2.p3.3.m*******.cmml">}</mo></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p3.3.m3.3b"><set id="S4.SS2.p3.3.m*******.cmml" xref="S4.SS2.p3.3.m*******"><apply id="S4.SS2.p3.3.m3.*******.cmml" xref="S4.SS2.p3.3.m3.*******"><csymbol cd="ambiguous" id="S4.SS2.p3.3.m3.*******.1.cmml" xref="S4.SS2.p3.3.m3.*******">subscript</csymbol><ci id="S4.SS2.p3.3.m3.*******.2.cmml" xref="S4.SS2.p3.3.m3.*******.2">𝑓</ci><apply id="S4.SS2.p3.3.m3.*******.3.cmml" xref="S4.SS2.p3.3.m3.*******.3"><csymbol cd="ambiguous" id="S4.SS2.p3.3.m3.*******.3.1.cmml" xref="S4.SS2.p3.3.m3.*******.3">subscript</csymbol><ci id="S4.SS2.p3.3.m3.*******.3.2.cmml" xref="S4.SS2.p3.3.m3.*******.3.2">𝜃</ci><cn id="S4.SS2.p3.3.m3.*******.3.3.cmml" type="integer" xref="S4.SS2.p3.3.m3.*******.3.3">0</cn></apply></apply><ci id="S4.SS2.p3.3.m3.1.1.cmml" xref="S4.SS2.p3.3.m3.1.1">⋯</ci><apply id="S4.SS2.p3.3.m3.*******.cmml" xref="S4.SS2.p3.3.m3.*******"><csymbol cd="ambiguous" id="S4.SS2.p3.3.m3.*******.1.cmml" xref="S4.SS2.p3.3.m3.*******">subscript</csymbol><ci id="S4.SS2.p3.3.m3.*******.2.cmml" xref="S4.SS2.p3.3.m3.*******.2">𝑓</ci><apply id="S4.SS2.p3.3.m3.*******.3.cmml" xref="S4.SS2.p3.3.m3.*******.3"><csymbol cd="ambiguous" id="S4.SS2.p3.3.m3.*******.3.1.cmml" xref="S4.SS2.p3.3.m3.*******.3">subscript</csymbol><ci id="S4.SS2.p3.3.m3.*******.3.2.cmml" xref="S4.SS2.p3.3.m3.*******.3.2">𝜃</ci><ci id="S4.SS2.p3.3.m3.*******.3.3.cmml" xref="S4.SS2.p3.3.m3.*******.3.3">𝐾</ci></apply></apply></set></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p3.3.m3.3c">\{f_{\theta_{0}},\cdot\cdot\cdot,f_{\theta_{K}}\}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p3.3.m3.3d">{ italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUBSCRIPT 0 end_POSTSUBSCRIPT end_POSTSUBSCRIPT , ⋯ , italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUBSCRIPT italic_K end_POSTSUBSCRIPT end_POSTSUBSCRIPT }</annotation></semantics></math>. We integrate all predictors using the following two different combination schemes: (1) <math alttext="C=C_{\text{Last}}" class="ltx_Math" display="inline" id="S4.SS2.p3.4.m4.1"><semantics id="S4.SS2.p3.4.m4.1a"><mrow id="S4.SS2.p3.4.m4.1.1" xref="S4.SS2.p3.4.m4.1.1.cmml"><mi id="S4.SS2.p3.4.m4.1.1.2" xref="S4.SS2.p3.4.m4.1.1.2.cmml">C</mi><mo id="S4.SS2.p3.4.m4.1.1.1" xref="S4.SS2.p3.4.m4.1.1.1.cmml">=</mo><msub id="S4.SS2.p3.4.m4.1.1.3" xref="S4.SS2.p3.4.m4.1.1.3.cmml"><mi id="S4.SS2.p3.4.m4.*******" xref="S4.SS2.p3.4.m4.*******.cmml">C</mi><mtext id="S4.SS2.p3.4.m4.*******" xref="S4.SS2.p3.4.m4.*******a.cmml">Last</mtext></msub></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p3.4.m4.1b"><apply id="S4.SS2.p3.4.m4.1.1.cmml" xref="S4.SS2.p3.4.m4.1.1"><eq id="S4.SS2.p3.4.m4.1.1.1.cmml" xref="S4.SS2.p3.4.m4.1.1.1"></eq><ci id="S4.SS2.p3.4.m4.1.1.2.cmml" xref="S4.SS2.p3.4.m4.1.1.2">𝐶</ci><apply id="S4.SS2.p3.4.m4.1.1.3.cmml" xref="S4.SS2.p3.4.m4.1.1.3"><csymbol cd="ambiguous" id="S4.SS2.p3.4.m4.*******.cmml" xref="S4.SS2.p3.4.m4.1.1.3">subscript</csymbol><ci id="S4.SS2.p3.4.m4.*******.cmml" xref="S4.SS2.p3.4.m4.*******">𝐶</ci><ci id="S4.SS2.p3.4.m4.*******a.cmml" xref="S4.SS2.p3.4.m4.*******"><mtext id="S4.SS2.p3.4.m4.*******.cmml" mathsize="70%" xref="S4.SS2.p3.4.m4.*******">Last</mtext></ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p3.4.m4.1c">C=C_{\text{Last}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p3.4.m4.1d">italic_C = italic_C start_POSTSUBSCRIPT Last end_POSTSUBSCRIPT</annotation></semantics></math>: making a prediction according to the last iterated predictor. (2) <math alttext="C=C_{\text{Vote}}" class="ltx_Math" display="inline" id="S4.SS2.p3.5.m5.1"><semantics id="S4.SS2.p3.5.m5.1a"><mrow id="S4.SS2.p3.5.m5.1.1" xref="S4.SS2.p3.5.m5.1.1.cmml"><mi id="S4.SS2.p3.5.m5.1.1.2" xref="S4.SS2.p3.5.m5.1.1.2.cmml">C</mi><mo id="S4.SS2.p3.5.m5.1.1.1" xref="S4.SS2.p3.5.m5.1.1.1.cmml">=</mo><msub id="S4.SS2.p3.5.m5.1.1.3" xref="S4.SS2.p3.5.m5.1.1.3.cmml"><mi id="S4.SS2.p3.5.m5.*******" xref="S4.SS2.p3.5.m5.*******.cmml">C</mi><mtext id="S4.SS2.p3.5.m5.*******" xref="S4.SS2.p3.5.m5.*******a.cmml">Vote</mtext></msub></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p3.5.m5.1b"><apply id="S4.SS2.p3.5.m5.1.1.cmml" xref="S4.SS2.p3.5.m5.1.1"><eq id="S4.SS2.p3.5.m5.1.1.1.cmml" xref="S4.SS2.p3.5.m5.1.1.1"></eq><ci id="S4.SS2.p3.5.m5.1.1.2.cmml" xref="S4.SS2.p3.5.m5.1.1.2">𝐶</ci><apply id="S4.SS2.p3.5.m5.1.1.3.cmml" xref="S4.SS2.p3.5.m5.1.1.3"><csymbol cd="ambiguous" id="S4.SS2.p3.5.m5.*******.cmml" xref="S4.SS2.p3.5.m5.1.1.3">subscript</csymbol><ci id="S4.SS2.p3.5.m5.*******.cmml" xref="S4.SS2.p3.5.m5.*******">𝐶</ci><ci id="S4.SS2.p3.5.m5.*******a.cmml" xref="S4.SS2.p3.5.m5.*******"><mtext id="S4.SS2.p3.5.m5.*******.cmml" mathsize="70%" xref="S4.SS2.p3.5.m5.*******">Vote</mtext></ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p3.5.m5.1c">C=C_{\text{Vote}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p3.5.m5.1d">italic_C = italic_C start_POSTSUBSCRIPT Vote end_POSTSUBSCRIPT</annotation></semantics></math>: making a prediction that averages all iterated predictors. Then the output prediction model <math alttext="F" class="ltx_Math" display="inline" id="S4.SS2.p3.6.m6.1"><semantics id="S4.SS2.p3.6.m6.1a"><mi id="S4.SS2.p3.6.m6.1.1" xref="S4.SS2.p3.6.m6.1.1.cmml">F</mi><annotation-xml encoding="MathML-Content" id="S4.SS2.p3.6.m6.1b"><ci id="S4.SS2.p3.6.m6.1.1.cmml" xref="S4.SS2.p3.6.m6.1.1">𝐹</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p3.6.m6.1c">F</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p3.6.m6.1d">italic_F</annotation></semantics></math> is defined as follows:</p>
<table class="ltx_equationgroup ltx_eqn_table" id="S4.E8">
<tbody>
<tr class="ltx_equation ltx_eqn_row ltx_align_baseline" id="S4.E8X">
<td class="ltx_eqn_cell ltx_eqn_center_padleft"></td>
<td class="ltx_td ltx_eqn_cell"></td>
<td class="ltx_td ltx_align_left ltx_eqn_cell"><math alttext="\displaystyle\quad F({\bm{x}}):=" class="ltx_Math" display="inline" id="S4.E8X.2.1.1.m1.1"><semantics id="S4.E8X.2.1.1.m1.1a"><mrow id="S4.E8X.2.1.1.m1.1.2" xref="S4.E8X.2.1.1.m1.1.2.cmml"><mrow id="S4.E8X.2.1.1.m*******" xref="S4.E8X.2.1.1.m*******.cmml"><mi id="S4.E8X.2.1.1.m*******.2" xref="S4.E8X.2.1.1.m*******.2.cmml">F</mi><mo id="S4.E8X.2.1.1.m*******.1" xref="S4.E8X.2.1.1.m*******.1.cmml">⁢</mo><mrow id="S4.E8X.2.1.1.m*******.3.2" xref="S4.E8X.2.1.1.m*******.cmml"><mo id="S4.E8X.2.1.1.m*******.3.2.1" stretchy="false" xref="S4.E8X.2.1.1.m*******.cmml">(</mo><mi id="S4.E8X.2.1.1.m1.1.1" xref="S4.E8X.2.1.1.m1.1.1.cmml">𝒙</mi><mo id="S4.E8X.2.1.1.m*******.3.2.2" rspace="0.278em" stretchy="false" xref="S4.E8X.2.1.1.m*******.cmml">)</mo></mrow></mrow><mo id="S4.E8X.2.1.1.m*******" rspace="0.278em" xref="S4.E8X.2.1.1.m*******.cmml">:=</mo><mi id="S4.E8X.2.1.1.m*******" xref="S4.E8X.2.1.1.m*******.cmml"></mi></mrow><annotation-xml encoding="MathML-Content" id="S4.E8X.2.1.1.m1.1b"><apply id="S4.E8X.2.1.1.m1.1.2.cmml" xref="S4.E8X.2.1.1.m1.1.2"><csymbol cd="latexml" id="S4.E8X.2.1.1.m*******.cmml" xref="S4.E8X.2.1.1.m*******">assign</csymbol><apply id="S4.E8X.2.1.1.m*******.cmml" xref="S4.E8X.2.1.1.m*******"><times id="S4.E8X.2.1.1.m*******.1.cmml" xref="S4.E8X.2.1.1.m*******.1"></times><ci id="S4.E8X.2.1.1.m*******.2.cmml" xref="S4.E8X.2.1.1.m*******.2">𝐹</ci><ci id="S4.E8X.2.1.1.m1.1.1.cmml" xref="S4.E8X.2.1.1.m1.1.1">𝒙</ci></apply><csymbol cd="latexml" id="S4.E8X.2.1.1.m*******.cmml" xref="S4.E8X.2.1.1.m*******">absent</csymbol></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.E8X.2.1.1.m1.1c">\displaystyle\quad F({\bm{x}}):=</annotation><annotation encoding="application/x-llamapun" id="S4.E8X.2.1.1.m1.1d">italic_F ( bold_italic_x ) :=</annotation></semantics></math></td>
<td class="ltx_eqn_cell ltx_eqn_center_padright"></td>
<td class="ltx_eqn_cell ltx_eqn_eqno ltx_align_middle ltx_align_right" rowspan="2"><span class="ltx_tag ltx_tag_equationgroup ltx_align_right">(8)</span></td>
</tr>
<tr class="ltx_equation ltx_eqn_row ltx_align_baseline" id="S4.E8Xa">
<td class="ltx_eqn_cell ltx_eqn_center_padleft"></td>
<td class="ltx_td ltx_eqn_cell"></td>
<td class="ltx_td ltx_align_left ltx_eqn_cell"><math alttext="\displaystyle\left\{\begin{aligned} C_{\text{Last}}\left(\{f_{\theta^{k}}\}_{0%
\leq k\leq K}\right)({\bm{x}})&amp;=f_{\theta^{K}}({\bm{x}}),\qquad\quad&amp;C=C_{%
\text{Last}}\\
C_{\text{Vote}}\left(\{f_{\theta^{k}}\}_{0\leq k\leq K}\right)({\bm{x}})&amp;=%
\mathbb{E}_{k}\left[f_{\theta^{k}}({\bm{x}})\right],&amp;C=C_{\text{Vote}}\end{%
aligned}\right.." class="ltx_math_unparsed" display="inline" id="S4.E8Xa.2.1.1.m1.8"><semantics id="S4.E8Xa.2.1.1.m1.8a"><mrow id="S4.E8Xa.2.1.1.m1.8b"><mo id="S4.E8Xa.2.1.1.m1.8.9">{</mo><mtable columnspacing="0pt" id="S4.E8Xa.2.1.1.m1.8.8" rowspacing="0pt"><mtr id="S4.E8Xa.2.1.1.m1.8.8a"><mtd class="ltx_align_right" columnalign="right" id="S4.E8Xa.2.1.1.m1.8.8b"><mrow id="S4.E8Xa.2.1.1.m1.*******.2"><msub id="S4.E8Xa.2.1.1.m1.*******.2.4"><mi id="S4.E8Xa.2.1.1.m1.*******.2.4.2">C</mi><mtext id="S4.E8Xa.2.1.1.m1.*******.2.4.3">Last</mtext></msub><mo id="S4.E8Xa.2.1.1.m1.*******.2.3">⁢</mo><mrow id="S4.E8Xa.2.1.1.m*******.*******"><mo id="S4.E8Xa.2.1.1.m*******.*******.2">(</mo><msub id="S4.E8Xa.2.1.1.m1.*******.*******"><mrow id="S4.E8Xa.2.1.1.m1.*******.*******.1.1"><mo id="S4.E8Xa.2.1.1.m1.*******.*******.1.1.2" stretchy="false">{</mo><msub id="S4.E8Xa.2.1.1.m1.*******.*******.1.1.1"><mi id="S4.E8Xa.2.1.1.m1.*******.*******.*******">f</mi><msup id="S4.E8Xa.2.1.1.m1.*******.*******.*******"><mi id="S4.E8Xa.2.1.1.m1.*******.*******.1.*******">θ</mi><mi id="S4.E8Xa.2.1.1.m1.*******.*******.1.*******">k</mi></msup></msub><mo id="S4.E8Xa.2.1.1.m1.*******.*******.1.1.3" stretchy="false">}</mo></mrow><mrow id="S4.E8Xa.2.1.1.m1.*******.*******.3"><mn id="S4.E8Xa.2.1.1.m1.*******.*******.3.2">0</mn><mo id="S4.E8Xa.2.1.1.m1.*******.*******.3.3">≤</mo><mi id="S4.E8Xa.2.1.1.m1.*******.*******.3.4">k</mi><mo id="S4.E8Xa.2.1.1.m1.*******.*******.3.5">≤</mo><mi id="S4.E8Xa.2.1.1.m1.*******.*******.3.6">K</mi></mrow></msub><mo id="S4.E8Xa.2.1.1.m*******.*******.3">)</mo></mrow><mo id="S4.E8Xa.2.1.1.m1.*******.2.3a">⁢</mo><mrow id="S4.E8Xa.2.1.1.m1.*******.2.5.2"><mo id="S4.E8Xa.2.1.1.m1.*******.2.5.2.1" stretchy="false">(</mo><mi id="S4.E8Xa.2.1.1.m*******.1.1.1">𝒙</mi><mo id="S4.E8Xa.2.1.1.m1.*******.2.5.2.2" stretchy="false">)</mo></mrow></mrow></mtd><mtd class="ltx_align_left" columnalign="left" id="S4.E8Xa.2.1.1.m1.8.8c"><mrow id="S4.E8Xa.2.1.1.m1.4.4.*******"><mrow id="S4.E8Xa.2.1.1.m1.4.4.*******.1"><mi id="S4.E8Xa.2.1.1.m1.4.4.4.4.*******"></mi><mo id="S4.E8Xa.2.1.1.m1.4.4.4.4.*******">=</mo><mrow id="S4.E8Xa.2.1.1.m1.4.4.*******.1.3"><msub id="S4.E8Xa.2.1.1.m1.4.4.*******.1.3.2"><mi id="S4.E8Xa.2.1.1.m1.4.4.*******.1.3.2.2">f</mi><msup id="S4.E8Xa.2.1.1.m1.4.4.*******.1.3.2.3"><mi id="S4.E8Xa.2.1.1.m1.4.4.*******.1.3.2.3.2">θ</mi><mi id="S4.E8Xa.2.1.1.m1.4.4.*******.1.3.2.3.3">K</mi></msup></msub><mo id="S4.E8Xa.2.1.1.m1.4.4.*******.1.3.1">⁢</mo><mrow id="S4.E8Xa.2.1.1.m1.4.4.*******.1.3.3.2"><mo id="S4.E8Xa.2.1.1.m1.4.4.*******.1.3.3.2.1" stretchy="false">(</mo><mi id="S4.E8Xa.2.1.1.m1.3.*******.1">𝒙</mi><mo id="S4.E8Xa.2.1.1.m1.4.4.*******.1.*******" stretchy="false">)</mo></mrow></mrow></mrow><mo id="S4.E8Xa.2.1.1.m1.4.4.*******.2">,</mo></mrow></mtd><mtd class="ltx_align_right" columnalign="right" id="S4.E8Xa.2.1.1.m1.8.8d"><mrow id="S4.E8Xa.2.1.1.m1.4.4.4.5.1"><mi id="S4.E8Xa.2.1.1.m1.4.4.4.5.1.2">C</mi><mo id="S4.E8Xa.2.1.1.m1.4.4.4.5.1.1">=</mo><msub id="S4.E8Xa.2.1.1.m1.4.4.4.5.1.3"><mi id="S4.E8Xa.2.1.1.m1.4.4.4.5.1.3.2">C</mi><mtext id="S4.E8Xa.2.1.1.m1.4.4.4.5.1.3.3">Last</mtext></msub></mrow></mtd></mtr><mtr id="S4.E8Xa.2.1.1.m1.8.8e"><mtd class="ltx_align_right" columnalign="right" id="S4.E8Xa.2.1.1.m1.8.8f"><mrow id="S4.E8Xa.2.1.1.m1.6.*******"><msub id="S4.E8Xa.2.1.1.m1.6.*******.4"><mi id="S4.E8Xa.2.1.1.m1.6.*******.4.2">C</mi><mtext id="S4.E8Xa.2.1.1.m1.6.*******.4.3">Vote</mtext></msub><mo id="S4.E8Xa.2.1.1.m1.6.*******.3">⁢</mo><mrow id="S4.E8Xa.2.1.1.m1.6.*******.2.1"><mo id="S4.E8Xa.2.1.1.m1.6.*******.2.1.2">(</mo><msub id="S4.E8Xa.2.1.1.m1.6.*******.2.1.1"><mrow id="S4.E8Xa.2.1.1.m1.6.*******.2.*******"><mo id="S4.E8Xa.2.1.1.m1.6.*******.2.*******.2" stretchy="false">{</mo><msub id="S4.E8Xa.2.1.1.m1.6.*******.2.*******.1"><mi id="S4.E8Xa.2.1.1.m1.6.*******.2.*******.1.2">f</mi><msup id="S4.E8Xa.2.1.1.m1.6.*******.2.*******.1.3"><mi id="S4.E8Xa.2.1.1.m1.6.*******.2.1.1.1.*******">θ</mi><mi id="S4.E8Xa.2.1.1.m1.6.*******.2.*******.1.3.3">k</mi></msup></msub><mo id="S4.E8Xa.2.1.1.m1.6.*******.2.*******.3" stretchy="false">}</mo></mrow><mrow id="S4.E8Xa.2.1.1.m1.6.*******.2.1.1.3"><mn id="S4.E8Xa.2.1.1.m1.6.*******.2.*******">0</mn><mo id="S4.E8Xa.2.1.1.m1.6.*******.2.*******">≤</mo><mi id="S4.E8Xa.2.1.1.m1.6.*******.2.*******">k</mi><mo id="S4.E8Xa.2.1.1.m1.6.*******.2.*******">≤</mo><mi id="S4.E8Xa.2.1.1.m1.6.*******.2.*******">K</mi></mrow></msub><mo id="S4.E8Xa.2.1.1.m1.6.*******.2.1.3">)</mo></mrow><mo id="S4.E8Xa.2.1.1.m1.6.*******.3a">⁢</mo><mrow id="S4.E8Xa.2.1.1.m1.6.*******.5.2"><mo id="S4.E8Xa.2.1.1.m1.6.*******.5.2.1" stretchy="false">(</mo><mi id="S4.E8Xa.2.1.1.m1.5.*******.1">𝒙</mi><mo id="S4.E8Xa.2.1.1.m1.6.*******.5.2.2" stretchy="false">)</mo></mrow></mrow></mtd><mtd class="ltx_align_left" columnalign="left" id="S4.E8Xa.2.1.1.m1.8.8g"><mrow id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2"><mrow id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2.1"><mi id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2.1.3"></mi><mo id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******">=</mo><mrow id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******"><msub id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.3"><mi id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.3.2">𝔼</mi><mi id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.3.3">k</mi></msub><mo id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.2">⁢</mo><mrow id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.1.1"><mo id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.1.1.2">[</mo><mrow id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.1.1.1"><msub id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.*******"><mi id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.1.*******">f</mi><msup id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.1.*******"><mi id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.1.*******.2">θ</mi><mi id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.1.*******.3">k</mi></msup></msub><mo id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.*******">⁢</mo><mrow id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.1.*******"><mo id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.1.*******.1" stretchy="false">(</mo><mi id="S4.E8Xa.2.1.1.m1.7.7.7.3.1.1">𝒙</mi><mo id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.1.*******.2" stretchy="false">)</mo></mrow></mrow><mo id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.1.1.3">]</mo></mrow></mrow></mrow><mo id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2.2">,</mo></mrow></mtd><mtd class="ltx_align_right" columnalign="right" id="S4.E8Xa.2.1.1.m1.8.8h"><mrow id="S4.E8Xa.2.1.1.m1.8.8.8.5.1"><mi id="S4.E8Xa.2.1.1.m1.8.8.8.5.1.2">C</mi><mo id="S4.E8Xa.2.1.1.m1.8.8.8.5.1.1">=</mo><msub id="S4.E8Xa.2.1.1.m1.8.8.8.5.1.3"><mi id="S4.E8Xa.2.1.1.m1.8.8.8.5.1.3.2">C</mi><mtext id="S4.E8Xa.2.1.1.m1.8.8.8.5.1.3.3">Vote</mtext></msub></mrow></mtd></mtr></mtable><mo id="S4.E8Xa.2.1.1.m1.8.10" lspace="0em">.</mo></mrow><annotation encoding="application/x-tex" id="S4.E8Xa.2.1.1.m1.8c">\displaystyle\left\{\begin{aligned} C_{\text{Last}}\left(\{f_{\theta^{k}}\}_{0%
\leq k\leq K}\right)({\bm{x}})&amp;=f_{\theta^{K}}({\bm{x}}),\qquad\quad&amp;C=C_{%
\text{Last}}\\
C_{\text{Vote}}\left(\{f_{\theta^{k}}\}_{0\leq k\leq K}\right)({\bm{x}})&amp;=%
\mathbb{E}_{k}\left[f_{\theta^{k}}({\bm{x}})\right],&amp;C=C_{\text{Vote}}\end{%
aligned}\right..</annotation><annotation encoding="application/x-llamapun" id="S4.E8Xa.2.1.1.m1.8d">{ start_ROW start_CELL italic_C start_POSTSUBSCRIPT Last end_POSTSUBSCRIPT ( { italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT end_POSTSUBSCRIPT } start_POSTSUBSCRIPT 0 ≤ italic_k ≤ italic_K end_POSTSUBSCRIPT ) ( bold_italic_x ) end_CELL start_CELL = italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUPERSCRIPT italic_K end_POSTSUPERSCRIPT end_POSTSUBSCRIPT ( bold_italic_x ) , end_CELL start_CELL italic_C = italic_C start_POSTSUBSCRIPT Last end_POSTSUBSCRIPT end_CELL end_ROW start_ROW start_CELL italic_C start_POSTSUBSCRIPT Vote end_POSTSUBSCRIPT ( { italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT end_POSTSUBSCRIPT } start_POSTSUBSCRIPT 0 ≤ italic_k ≤ italic_K end_POSTSUBSCRIPT ) ( bold_italic_x ) end_CELL start_CELL = blackboard_E start_POSTSUBSCRIPT italic_k end_POSTSUBSCRIPT [ italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT end_POSTSUBSCRIPT ( bold_italic_x ) ] , end_CELL start_CELL italic_C = italic_C start_POSTSUBSCRIPT Vote end_POSTSUBSCRIPT end_CELL end_ROW .</annotation></semantics></math></td>
<td class="ltx_eqn_cell ltx_eqn_center_padright"></td>
</tr>
</tbody>
</table>
<p class="ltx_p" id="S4.SS2.p3.7">Note that RA-Labeling can be seamlessly incorporated into existing machine learning methods to mitigate the negative consequences of learning from noisy labels. The pseudocode of RA-Labeling is shown in Algorithm <a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#alg2" title="Algorithm 2 ‣ 4.2 Iterative Refinement Labeling (RA-Labeling) ‣ 4 LARA: The Proposed Framework ‣ Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling"><span class="ltx_text ltx_ref_tag">2</span></a>.</p>
</div>
</section>
</section>
<section class="ltx_section" id="S6">
<h2 class="ltx_title ltx_title_section">
<span class="ltx_tag ltx_tag_section">6 </span>Conclusion</h2>
<div class="ltx_para" id="S6.p1">
<p class="ltx_p" id="S6.p1.1">We study the problem of price movement forecasting and propose the LARA (<span class="ltx_text ltx_framed ltx_framed_underline" id="S6.p1.1.1">L</span>ocality-Aware <span class="ltx_text ltx_framed ltx_framed_underline" id="S6.p1.1.2">A</span>ttention and Iterative <span class="ltx_text ltx_framed ltx_framed_underline" id="S6.p1.1.3">R</span>efinement L<span class="ltx_text ltx_framed ltx_framed_underline" id="S6.p1.1.4">a</span>beling) framework. In LARA, we introduce LA-Attention to extract potentially profitable samples and RA-Labeling to adaptively refine the noisy labels of potentially profitable samples.
Extensive experiments on three real-world financial markets showcase its superior performance over time-series analysis methods, machine learning based models and noisy labels methods.
Besides, we also illustrate how each component works by comprehensive ablation studies, which indicates that LARA indeed captures more reliable trading opportunities.
We expect our work to pave the way for price movement forecasting in more realistic quantitative trading scenarios.</p>
</div>
</section>
<section class="ltx_section" id="Sx1">
<h2 class="ltx_title ltx_title_section">Acknowledgements</h2>
<div class="ltx_para" id="Sx1.p1">
<p class="ltx_p" id="Sx1.p1.1">Jian Li, Liang Zeng, Lei Wang and Hui Niu were supported in part by the National Natural Science Foundation of China Grant 62161146004.</p>
</div>
</section>
<section class="ltx_section" id="Sx2">
<h2 class="ltx_title ltx_title_section">Contribution Statement</h2>
<div class="ltx_para" id="Sx2.p1">
<p class="ltx_p" id="Sx2.p1.1">Liang Zeng and Lei Wang have made equal and significant contributions to this work, including the methods, implementation, and paper writing.
Hui Niu conducted the literature review and implemented the evaluation framework.
Ruchen Zhang and Ling Wang provided the source data to support this work.
Jian Li, as the corresponding author, contributed to the idea and paper writing, as well as providing computing resources.</p>
</div>
</section>