"""
示例文件：model_all_utils_eth.py
主要放置了一些工具函数，包括 sample_equal、set_seed、以及 dual_label_one 等。
与原始CSI300版本方法几乎相同，只在注释和细微处提及了面向eth的场景。
"""

import numpy as np
import pandas as pd
import random
import os
import lightgbm as lgb


def set_seed(num, params):
    np.random.seed(num)
    random.seed(num)
    params['random_state'] = num
    params['seed'] = num
    params['feature_fraction_seed'] = num
    params['bagging_seed'] = num
    params['deterministic'] = True
    os.environ['PYTHONHASHSEED'] = str(num)


def sample_equal(wdf, metric_learning_label, pcteject, out):
    """
    从 wdf 中筛选掉 |out| < pcteject 的部分(或做其他平衡措施)，
    并且把接近边缘的样本保存在ping集做进一步处理
    """
    # 大于一定幅度的留在main
    traindf = wdf.loc[(abs(wdf[out]) >= pcteject), :]
    # 小于一定幅度的放到ping
    traindf_mid = wdf.loc[(abs(wdf[out]) < pcteject), :]

    label0_num = traindf[traindf['label'] == 0].shape[0]
    label1_num = traindf[traindf['label'] == 1].shape[0]
    label2_num = traindf_mid.shape[0]

    if label0_num > label1_num:
        traindf_bal = pd.concat([
            traindf[traindf['label'] == 0].sample(label1_num),
            traindf[traindf['label'] == 1]
        ]).sort_index()
    else:
        traindf_bal = pd.concat([
            traindf[traindf['label'] == 0],
            traindf[traindf['label'] == 1].sample(label0_num)
        ]).sort_index()

    # ping集取和较小一方数量相当
    traindf_ping = traindf_mid.sample(min(min(label0_num, label1_num), label2_num)).sort_index()

    # 同时合并这两部分在 metric_learning_label 中的索引
    train_metric_learning_label_df = pd.concat([
        metric_learning_label.loc[traindf_bal.index],
        metric_learning_label.loc[traindf_ping.index]
    ], axis=0)

    return traindf_bal, traindf_ping, train_metric_learning_label_df


def dual_label_one(
    x_train_transform, 
    y_train_transform, 
    x_test_transform, 
    y_test_transform, 
    init_gbm, 
    self_model, 
    params, 
    usefv
):
    """
    dual label流程，用于在训练时多次交互迭代来矫正label(正/负样本)。
    """
    # 初始模型
    gbm = init_gbm
    changes_list = []
    gbm_list = []

    for i in range(self_model.dual_numbers):
        # 用当前模型做推断
        y_train_transform['proba'] = gbm.predict(x_train_transform)
        y_train_transform['label1'] = 0
        y_train_transform['label2'] = 0

        ratio = self_model.dual_ratio
        proba_series = y_train_transform['proba']

        # 获取要翻转的一部分
        num_data = proba_series.shape[0]
        sorted_indices = proba_series.sort_values(ascending=False).index
        num1 = int((1-ratio)*num_data)
        num2 = int(ratio*num_data)

        # 例如上部分维持 label1=1，下部分 label2=1
        idx1 = sorted_indices[:num1]
        idx2 = sorted_indices[-num2:]

        y_train_transform.loc[idx1, 'label1'] = 1
        y_train_transform.loc[idx2, 'label2'] = 1

        # temp_label：如果样本原标签是 1，则和 label1=1 时才继续1，否则翻转；0同理
        tmp_label = ((y_train_transform['label'] & y_train_transform['label1']) 
                     | ((1 - y_train_transform['label']) & y_train_transform['label2']))

        change_number = (y_train_transform['label'] ^ tmp_label).sum()  # 统计变化的标签数
        changes_list.append(change_number)

        # 更新新的 label
        y_train_transform['label'] = tmp_label.astype(int)

        gbm_list.append(gbm)
        # 用新的 label 再训练一个模型
        new_data = lgb.Dataset(x_train_transform, label=y_train_transform['label'])
        gbm = lgb.train(params, new_data)

    # 在测试集上用 gbm_list 做推断，最后ensemble
    predicted_df = pd.DataFrame()
    for idx, _gbm in enumerate(gbm_list):
        predicted_df[f"model{idx}"] = _gbm.predict(x_test_transform)

    return np.mean(changes_list), gbm_list, predicted_df