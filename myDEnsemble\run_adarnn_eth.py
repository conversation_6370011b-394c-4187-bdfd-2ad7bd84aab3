# run_adarnn_eth.py
import os
import re
from datetime import datetime, timedelta

import numpy as np
import pandas as pd

from eth_data_processor import ETHDataProcessor
from pytorch_adarnn import ADARNN
from qlib.data.dataset.handler import DataHandlerLP

def main():
    # ------------------------------
    # 1. 配置区
    # ------------------------------
    USE_FEATURE_TYPE = "360"  # 可选: "158" 或 "360"
    instrument_name = 'ETHUSDT'
    eth_kline_filepath = '../data/ETHUSDT_BINANCE_1m_2025-01-01_00_00_00_2025-05-31_00_00_00.csv'
    features_pkl_158 = eth_kline_filepath.replace('.csv', '_158_features.pkl')
    features_pkl_360 = eth_kline_filepath.replace('.csv', '_360_features.pkl')

    # 从文件名提取开始/结束日期并计算 8:1:1 划分
    match = re.search(
        r'_(\d{4}-\d{2}-\d{2})_\d{2}_\d{2}_\d{2}_(\d{4}-\d{2}-\d{2})_\d{2}_\d{2}_\d{2}',
        eth_kline_filepath
    )
    if not match:
        raise ValueError(f"无法从文件名中提取日期: {eth_kline_filepath}")
    data_start = datetime.strptime(match.group(1), '%Y-%m-%d')
    data_end   = datetime.strptime(match.group(2), '%Y-%m-%d')
    total_days = (data_end - data_start).days + 1
    train_days = int(total_days * 0.8)
    valid_days = int(total_days * 0.1)
    test_days  = total_days - train_days - valid_days

    train_start = data_start
    train_end   = data_start + timedelta(days=train_days - 1)
    valid_start = train_end   + timedelta(days=1)
    valid_end   = valid_start + timedelta(days=valid_days - 1)
    test_start  = valid_end   + timedelta(days=1)
    test_end    = data_end

    print(f"Train : {train_start} ~ {train_end}")
    print(f"Valid : {valid_start} ~ {valid_end}")
    print(f"Test  : {test_start} ~ {test_end}")

    # ------------------------------
    # 2. 数据处理
    # ------------------------------
    # 2.1 特征加载或生成
    if USE_FEATURE_TYPE == "158":
        if os.path.exists(features_pkl_158):
            print(f"[INFO] 加载已有特征文件 {features_pkl_158}")
            df_feat = pd.read_pickle(features_pkl_158)
            processor = ETHDataProcessor(df_feat, instrument_name=instrument_name)
            processor.feature_columns = [c for c in df_feat.columns if c.startswith('feature_')]
        else:
            print("[INFO] 从 CSV 读取并计算158特征")
            raw = pd.read_csv(eth_kline_filepath)
            processor = ETHDataProcessor(raw, instrument_name=instrument_name)
            processor.generate_features_alpha158()
            pd.to_pickle(processor.df, features_pkl_158)
            print(f"[INFO] 158特征已保存到 {features_pkl_158}")
    else:  # 默认360
        if os.path.exists(features_pkl_360):
            print(f"[INFO] 加载已有特征文件 {features_pkl_360}")
            df_feat = pd.read_pickle(features_pkl_360)
            processor = ETHDataProcessor(df_feat, instrument_name=instrument_name)
            processor.feature_columns = [c for c in df_feat.columns if c.startswith('feature_')]
        else:
            print("[INFO] 从 CSV 读取并计算360特征")
            raw = pd.read_csv(eth_kline_filepath)
            processor = ETHDataProcessor(raw, instrument_name=instrument_name)
            processor.generate_features_alpha360()
            pd.to_pickle(processor.df, features_pkl_360)
            print(f"[INFO] 360特征已保存到 {features_pkl_360}")

    # 2.2 生成标签
    prediction_horizon = 2
    processor.generate_labels(future_periods=prediction_horizon)

    # 2.3 清洗 + 切分
    processed = processor.get_processed_data()
    if processed.empty:
        print("[ERROR] 处理后无数据，退出")
        return

    # 切分成 LARA 格式的 MockDatasetH
    dataset = processor.split_data_for_lara(
        processed,
        train_start.strftime('%Y-%m-%d'),
        train_end.strftime('%Y-%m-%d'),
        valid_start.strftime('%Y-%m-%d'),
        valid_end.strftime('%Y-%m-%d'),
        test_start.strftime('%Y-%m-%d'),
        test_end.strftime('%Y-%m-%d'),
    )

    # ------------------------------
    # 3. ADARNN 模型训练 & 预测
    # ------------------------------
    # 3.1 超参数（可根据需要调整）
    adarnn_params = {
        "d_feat": int(len(processor.feature_columns)/60),
        "hidden_size": 64,
        "num_layers": 2,
        "dropout": 0.0,
        "n_epochs": 200,
        "pre_epoch": 40,
        "dw": 0.5,
        "loss_type": "cosine",
        "len_seq": 60,
        "len_win": 0,
        "lr": 1e-3,
        "metric": "loss",
        "batch_size": 800,
        "early_stop": 20,
        "optimizer": "adam",
        "loss": "mse",
        "n_splits": 2,
        "GPU": 0,
        "seed": 42,
    }

    print("[INFO] 初始化 ADARNN 模型")
    model = ADARNN(**adarnn_params)

    print("[INFO] 开始训练 ADARNN")
    model.fit(dataset)
    print("[INFO] 训练完成")

    print("[INFO] 对测试集做预测")
    preds = model.predict(dataset, segment="test")  # 返回 pd.Series，索引=(datetime,instrument)
    print(preds.head())

    # ------------------------------
    # 4. 简单评估
    # ------------------------------
    test_label_df = dataset.test_dict["label"]  # DataFrame，列名为 label_fwd_return_X
    # 重命名与 preds 对齐
    test_label_df = test_label_df.rename(columns={processor.label_column_name: "label"})
    # 合并
    df_ = pd.concat([preds.rename("score"), test_label_df["label"]], axis=1, join="inner")
    if df_.empty:
        print("[WARN] 无法合并预测与真实标签")
        return

    # 取 Top-N 计算命中率 & 平均收益
    N = min(1000, len(df_))
    top_df = df_.sort_values("score", ascending=False).iloc[:N].copy()
    # hit 定义为真实收益 >= 0
    top_df["hit"] = (top_df["label"] >= 0).astype(int)
    precision = top_df["hit"].mean()
    avg_ret   = top_df["label"].mean()
    print(f"[RESULT] Top {N} Precision: {precision:.2%}, Average Return: {avg_ret:.4%}")

    # 如需保存结果，可取消下列注释
    # os.makedirs("results", exist_ok=True)
    # pd.concat([preds.rename("score"), test_label_df], axis=1).to_csv("results/adarnn_eth.csv")


if __name__ == "__main__":
    main()
