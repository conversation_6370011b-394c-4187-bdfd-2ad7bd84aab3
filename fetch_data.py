import sqlite3
import pandas as pd

def fetch_data(symbol, exchange, start_datetime, end_datetime, interval="1m"):
    # 连接到SQLite数据库
    conn = sqlite3.connect('C:\\Users\<USER>\\.vntrader\\database.db')
    
    # 定义查询SQL语句
    query = f"SELECT * FROM dbbardata WHERE symbol = '{symbol}' AND exchange = '{exchange}' AND datetime BETWEEN '{start_datetime}' AND '{end_datetime}'"
    # 定义查询SQL语句，添加ORDER BY子句确保按时间排序
    #query = f"SELECT * FROM dbbardata WHERE symbol = '{symbol}' AND exchange = '{exchange}' AND datetime BETWEEN '{start_datetime}' AND '{end_datetime}' ORDER BY datetime ASC"
    

    # 使用pandas读取整个数据库到DataFrame
    df = pd.read_sql_query(query, conn)

    # 关闭数据库连接
    conn.close()

    # 将datetime列设置为DataFrame的索引
    df['datetime'] = pd.to_datetime(df['datetime'])
    df.set_index('datetime', inplace=True)

    # 重命名指定列
    rename_dict = {
        'open_price': 'open',
        'close_price': 'close',
        'low_price': 'low',
        'high_price': 'high'
    }
    df.rename(columns={k: v for k, v in rename_dict.items() if k in df.columns}, inplace=True)

    # 合并K线周期
    interval_map = {
        "1m": "1min",
        "5m": "5min",
        "15m": "15min",
        "30m": "30min",
        "1h": "1h",
        "2h": "2h",
        "4h": "4h",
        "6h": "6h",
        "12h": "12h",
        "1d": "1D",
        "3d": "3D",
        "1w": "1W"
    }
    if interval not in interval_map:
        raise ValueError(f"不支持的K线周期: {interval}")

    if interval != "1m":
        df = df.resample(interval_map[interval], label='right', closed='right').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum' if 'volume' in df.columns else 'first',
            # 你可以根据实际表结构添加更多字段
        }).dropna()

    return df

def resample_1min_to_nmin(df:pd.DataFrame,n=10,offset=None):
    """将1分钟K线数据合成为10分钟K线"""
    # 重置索引
    df = df.reset_index()
    # 确保datetime列为datetime类型
    df['datetime'] = pd.to_datetime(df['datetime'])
    # 设置datetime为索引
    df = df.set_index('datetime')
    # 定义聚合规则
    agg_dict = {
        'symbol': 'first', 
        'exchange': 'first',
        'interval': 'first',
        'volume': 'sum',
        'turnover': 'sum',
        'open_interest': 'last',
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last'
    }
    # 按10分钟重采样并聚合
    resampled = df.resample(f'{n}min',offset=offset).agg(agg_dict)
    # 重置索引，使datetime重新成为列
    resampled = resampled.reset_index()
    return resampled

if __name__ == "__main__":
    symbol = "ETHUSDT"
    exchange = "BINANCE"
    start_datetime = "2024-01-01 00:00:00"
    end_datetime = "2024-05-01 00:00:00"
    interval = "1m"
    
    df = fetch_data(symbol, exchange, start_datetime, end_datetime, interval)
    # 验证数据排序
    print(f"时间是递增的: {df.index.is_monotonic_increasing}")  # 如果返回True，说明时间是递增的
    # -----调整k线周期------------------------------------------------------------------调整k线周期------
    interval = "1m"
    df= resample_1min_to_nmin(df,n=1,offset='0min')
    # -------------------------------------------------------------------------------------------------
    # 保存为.pkl文件
    output_file = f"data/{symbol}_{exchange}_{interval}_{start_datetime.replace(' ', '_').replace(':', '_')}_{end_datetime.replace(' ', '_').replace(':', '_')}.csv"
    df.to_csv(path_or_buf=output_file)
    print(df)