from backtest_EC_martin import should_open, should_reopen
from calculate_indicator import calculate_atr,calculate_rsica,calculate_rsica_reverse,calculate_RSI,calculate_cti,calculate_EWO,calculate_bollinger_bands,calculate_vwapb
from calculate_indicator import calculate_IMIN,calculate_cti,calculate_ctica,calculate_ctica_reverse
import tqdm
import numpy as np
import pandas as pd
from config import RSI_CA_THRESHOLD,RSI_THRESHOLD

def resample_1min_to_nmin(df:pd.DataFrame,n=10,offset=None):
    """将1分钟K线数据合成为10分钟K线"""
    # 重置索引
    #df = df.reset_index()
    # 确保datetime列为datetime类型
    df['datetime'] = pd.to_datetime(df['datetime'])
    # 设置datetime为索引
    df = df.set_index('datetime')
    # 定义聚合规则
    agg_dict = {
        'symbol': 'first', 
        'exchange': 'first',
        'interval': 'first',
        'volume': 'sum',
        'turnover': 'sum',
        'open_interest': 'last',
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last'
    }
    # 按10分钟重采样并聚合
    resampled = df.resample(f'{n}min',offset=offset).agg(agg_dict)
    # 重置索引，使datetime重新成为列
    resampled = resampled.reset_index()
    return resampled

def label_sup_order(df):
    """
    说明：
    1. 复制输入数据，并新增'Label'列用于存储比较结果；
    2. 遍历数据，以单循环方式：首先判断若无活跃交易，则调用should_open确定母单下单K线，
       若已有活跃交易，则调用should_reopen检测补单信号；对于触发补单的K线，
       如果该K线之后存在10根K线，则比较这根补单K线的收盘价和10分钟后K线的收盘价：
       如果10分钟后的收盘价高于补单K线的收盘价，则Label为1，否则为0；
    3. 更新交易信息，允许同一母单下多次补单（不超过最大补单次数）；
    4. 如果检测到新的母单信号，则重置当前活跃交易，以便后续重新检测。
    5. 返回df_copy['Label']。
    """

    df_copy = df.copy()
    df_copy['Label'] = np.nan
    df_copy['ATR'] = calculate_atr(df)
    df_copy['RSI']=calculate_RSI(df, 14)
    df_copy['RSI_cumulative_area']=calculate_rsica(df, 14, 30)

    # 确保有 datetime 列（如果没有，则尝试从 index 中获取）
    if 'datetime' not in df_copy.columns:
        if isinstance(df_copy.index[0], pd.Timestamp):
            df_copy = df_copy.reset_index()
        else:
            raise ValueError("未检测到 'datetime' 列，请添加时间信息。")
    else:
        # 如果 datetime 列不是 Timestamp 类型，则转换之
        df_copy['datetime'] = pd.to_datetime(df_copy['datetime'])

    # 定义时间参数（单位：分钟）
    order_interval = 30  # 如果当前行的时间与母单时间超过该值，则认为母单过期
    wait_duration = 30   # 仅在此时间段内允许检测补单信号
    max_martin_orders = 3
    active_trade_info = None  # 存储当前活跃的母单交易信息

    # 单循环遍历数据，从第二行开始（因为需要前一个数据用于比较）
    for i in tqdm.tqdm(range(1, len(df_copy)), desc="标签生成进度"):
        current_time = df_copy.iloc[i]['datetime']

        # 如果存在活跃母单，检查是否已经超出 order_interval
        if active_trade_info is not None:
            mother_time = active_trade_info['time']
            elapsed = (current_time - mother_time).total_seconds() / 60.0  # 转换成分钟
            if elapsed > order_interval:
                active_trade_info = None  # 清除过期的交易

        # 没有活跃交易时，检测是否有新的母单信号
        if active_trade_info is None:
            if should_open(df_copy, i):
                mother_price = df_copy.iloc[i]['close']
                initial_ST = ((df_copy.iloc[i]['high'] + df_copy.iloc[i]['low']) / 2 +
                              3 * df_copy.iloc[i]['ATR']) - mother_price
                active_trade_info = {
                    'last_order_price': mother_price,
                    'follow_orders': 0,
                    'last_martin_distance': initial_ST,
                    'time': current_time  # 记录母单建立的时间
                }
        else:
            # 若已经有母单，则仅在等待补单期内（elapsed <= wait_duration）进行补单检测
            elapsed = (current_time - active_trade_info['time']).total_seconds() / 60.0
            if elapsed <= wait_duration:
                if active_trade_info['follow_orders'] < max_martin_orders and \
                   should_reopen(df_copy, i, active_trade_info, max_martin_orders):
                    ST_distance = ((df_copy.iloc[i]['high'] + df_copy.iloc[i]['low']) / 2 +
                                   3 * df_copy.iloc[i]['ATR']) - df_copy.iloc[i]['close']
                    active_trade_info['last_order_price'] = df_copy.iloc[i]['close']
                    active_trade_info['last_martin_distance'] = ST_distance
                    active_trade_info['follow_orders'] += 1

                    # 检查当前补单后 10 根 K 线是否存在，进而确定 Label
                    if i + 10 < len(df_copy):
                        order_close = df_copy.iloc[i]['close']
                        future_close = df_copy.iloc[i + 10]['close']
                        df_copy.at[i, 'Label'] = (future_close - order_close)/order_close
                    else:
                        df_copy.at[i, 'Label'] = np.nan
            # 如果当前行又触发新的母单信号，则重置当前活跃交易（模拟母单重新建立）
            #if should_open(df_copy, i):
            #    active_trade_info = None

    return df_copy['Label']

def label_sup_order_simple(df):
    """
    说明：
    1. 复制输入数据，并新增'Label'列用于存储比较结果；
    2. 遍历数据，以单循环方式：首先判断若无活跃交易，则调用should_open确定母单下单K线，
       若已有活跃交易，则调用should_reopen检测补单信号；对于触发补单的K线，
       如果该K线之后存在10根K线，则比较这根补单K线的收盘价和10分钟后K线的收盘价：
       如果10分钟后的收盘价高于补单K线的收盘价，则Label为1，否则为0；
    3. 更新交易信息，允许同一母单下多次补单（不超过最大补单次数）；
    4. 如果检测到新的母单信号，则重置当前活跃交易，以便后续重新检测。
    5. 返回df_copy['Label']。
    """

    df_copy = df.copy()
    df_copy['RSI_cumulative_area']=calculate_rsica(df, 14, 30)

    
    Threshold = RSI_CA_THRESHOLD
    df_copy['Label'] = np.nan
    for i in range(1, len(df_copy)):
        pre_val = df_copy['RSI_cumulative_area'].iloc[i-1]
        cur_val = df_copy['RSI_cumulative_area'].iloc[i]
        # 只有前一根小于等于，当前大于Threshold，才计算Label
        if pre_val <= Threshold and cur_val > Threshold:
            if i + 10 < len(df_copy):
                close_now = df_copy['close'].iloc[i]
                close_future = df_copy['close'].iloc[i + 10]
                df_copy.at[i, 'Label'] = (close_future - close_now) / close_now
            else:
                df_copy.at[i, 'Label'] = np.nan
        else:
            df_copy.at[i, 'Label'] = np.nan
    # 首根k线无法判断
    df_copy.at[0, 'Label'] = np.nan

    return df_copy['Label']

def label_sup_order_simple_1(df):
    df_copy = df.copy()
    df_copy['RSI_cumulative_area']=calculate_rsica(df, 14, RSI_THRESHOLD)

    
    Threshold = RSI_CA_THRESHOLD
    df_copy['Label'] = np.nan
    for i in range(1, len(df_copy)):
        pre_val = df_copy['RSI_cumulative_area'].iloc[i-1]
        cur_val = df_copy['RSI_cumulative_area'].iloc[i]
        # 当前大于Threshold，才计算Label
        if cur_val > Threshold:
            if i + 10 < len(df_copy):
                close_now = df_copy['close'].iloc[i]
                close_future = df_copy['close'].iloc[i + 10]
                df_copy.at[i, 'Label'] = (close_future - close_now) / close_now
            else:
                df_copy.at[i, 'Label'] = np.nan
        else:
            df_copy.at[i, 'Label'] = np.nan

    return df_copy['Label']

def label_sup_order_simple_1_reverse(df):
    df_copy = df.copy()
    df_copy['RSI_cumulative_area']=calculate_rsica_reverse(df, 14, 70)

    
    Threshold = RSI_CA_THRESHOLD
    df_copy['Label'] = np.nan
    for i in range(1, len(df_copy)):
        pre_val = df_copy['RSI_cumulative_area'].iloc[i-1]
        cur_val = df_copy['RSI_cumulative_area'].iloc[i]
        # 当前大于Threshold，才计算Label
        if cur_val > Threshold:
            if i + 10 < len(df_copy):
                close_now = df_copy['close'].iloc[i]
                close_future = df_copy['close'].iloc[i + 10]
                df_copy.at[i, 'Label'] = (close_future - close_now) / close_now
            else:
                df_copy.at[i, 'Label'] = np.nan
        else:
            df_copy.at[i, 'Label'] = np.nan

    return df_copy['Label']

def label_shape(df):
    df_copy = df.copy()
    df_copy['RSI_cumulative_area']=calculate_rsica(df, 14, 30)

    
    Threshold = RSI_CA_THRESHOLD
    df_copy['Label'] = np.nan
    for i in range(1, len(df_copy)):
        pre_val = df_copy['RSI_cumulative_area'].iloc[i-1]
        cur_val = df_copy['RSI_cumulative_area'].iloc[i]
        # 当前大于Threshold，才计算Label
        if cur_val > Threshold:
            # 当前大于Threshold，才计算Label
            if i + 10 < len(df_copy):
                green_count = 0
                # 遍历当前K线后10根K线
                for j in range(i, i+11):
                    # 判断绿线条件：收盘价 > 开盘价
                    if df_copy['close'].iloc[j] > df_copy['open'].iloc[j]:
                        green_count += 1
                ratio = green_count / 10.0
                ratio = 5*(ratio -0.5)
                df_copy.at[i, 'Label'] = ratio
            else:
                df_copy.at[i, 'Label'] = np.nan
            # 计算当前 K 线往后 10 根 K 线上涨的顺利程度
            #if i + 10 < len(df_copy):
            #    up_count = 0
            #    for j in range(i, i + 10):
            #        # 收盘价连续上涨则计数（可选也可以按 j==i 跳过当前根，但这里包含i）
            #        if df_copy['close'].iloc[j] > df_copy['close'].iloc[j-1]:
            #            up_count += 1
            #    # 顺利程度定义为10根内上涨天数比率
            #    ratio = up_count / 10.0
            #    df_copy.at[i, 'Label'] = ratio
        else:
            df_copy.at[i, 'Label'] = np.nan

    return df_copy['Label']

def label_remaining_area(df):
    df_copy = df.copy()
    df_copy['RSI_cumulative_area']=calculate_rsica(df, 14, 30)
    df_copy['RSI'] = calculate_RSI(df)

    
    Threshold = RSI_CA_THRESHOLD
    df_copy['Label'] = np.nan
    for i in range(1, len(df_copy)):
        pre_val = df_copy['RSI_cumulative_area'].iloc[i-1]
        cur_val = df_copy['RSI_cumulative_area'].iloc[i]
        # 当当前累计面积大于阈值时，计算从当前k线开始之后所有k线中RSI小于30部分的累积面积
        if cur_val > Threshold and pre_val <= Threshold:
            acc_area = 0.0
            # 这里只累计从当前k线开始，直到第一次RSI大于等于30为止
            for j in range(i, len(df_copy)):
                if df_copy['RSI'].iloc[j] < 30:
                    acc_area += (30 - df_copy['RSI'].iloc[j])
                else:
                    break
            df_copy.at[i, 'Label'] = acc_area
        else:
            df_copy.at[i, 'Label'] = np.nan

    return df_copy['Label']

def label_cti(df):
    """
    标记：仅当上一根K线cti<0.5，且当前K线cti>=0.5时，才计算当前K线往后10根K线的收益率（(close[i+10] - close[i])/close[i]）。
    其他情况下为np.nan。
    """
    df_copy = df.copy()
    # 计算cti指标（需要 calculate_cti 函数支持）
    df_copy['cti'] = calculate_cti(df_copy)
    df_copy['Label'] = np.nan
    for i in range(1, len(df_copy)):
        cti_prev = df_copy['cti'].iloc[i - 1]
        cti_cur = df_copy['cti'].iloc[i]
        if cti_cur <= -0.5:
            if i + 10 < len(df_copy):
                ret = (df_copy['close'].iloc[i + 10] - df_copy['close'].iloc[i]) / df_copy['close'].iloc[i]
                df_copy.at[i, 'Label'] = ret
            else:
                df_copy.at[i, 'Label'] = np.nan
        else:
            df_copy.at[i, 'Label'] = np.nan
    return df_copy['Label']

def label_ctica(df):
    df_copy = df.copy()
    # 计算cti指标（需要 calculate_cti 函数支持）
    df_copy['cti'] = calculate_ctica(df_copy)
    df_copy['Label'] = np.nan
    for i in range(1, len(df_copy)):
        cti_prev = df_copy['cti'].iloc[i - 1]
        cti_cur = df_copy['cti'].iloc[i]
        if cti_cur >7.5:
            if i + 10 < len(df_copy):
                ret = (df_copy['close'].iloc[i + 10] - df_copy['close'].iloc[i]) / df_copy['close'].iloc[i]
                df_copy.at[i, 'Label'] = ret
            else:
                df_copy.at[i, 'Label'] = np.nan
        else:
            df_copy.at[i, 'Label'] = np.nan
    return df_copy['Label']

def label_cti_reverse(df):
    """
    标记：仅当上一根K线cti<0.5，且当前K线cti>=0.5时，才计算当前K线往后10根K线的收益率（(close[i+10] - close[i])/close[i]）。
    其他情况下为np.nan。
    """
    df_copy = df.copy()
    # 计算cti指标（需要 calculate_cti 函数支持）
    df_copy['cti'] = calculate_ctica_reverse(df_copy)
    df_copy['Label'] = np.nan
    for i in range(1, len(df_copy)):
        cti_prev = df_copy['cti'].iloc[i - 1]
        cti_cur = df_copy['cti'].iloc[i]
        if cti_cur >= 0.5:
            if i + 10 < len(df_copy):
                ret = (df_copy['close'].iloc[i] - df_copy['close'].iloc[i + 10]) / df_copy['close'].iloc[i]
                df_copy.at[i, 'Label'] = ret
            else:
                df_copy.at[i, 'Label'] = np.nan
        else:
            df_copy.at[i, 'Label'] = np.nan
    return df_copy['Label']

def label_ctica_reverse(df):
    df_copy = df.copy()
    # 计算cti指标（需要 calculate_cti 函数支持）
    df_copy['cti'] = calculate_ctica_reverse(df_copy)
    df_copy['Label'] = np.nan
    for i in range(1, len(df_copy)):
        cti_prev = df_copy['cti'].iloc[i - 1]
        cti_cur = df_copy['cti'].iloc[i]
        if cti_cur >7.5:
            if i + 10 < len(df_copy):
                ret = (df_copy['close'].iloc[i] - df_copy['close'].iloc[i + 10]) / df_copy['close'].iloc[i]
                df_copy.at[i, 'Label'] = ret
            else:
                df_copy.at[i, 'Label'] = np.nan
        else:
            df_copy.at[i, 'Label'] = np.nan
    return df_copy['Label']

def label_ewo(df):
    """
    标记 EWO 指标低于下方10%分位数时，当前K线往后10根K线的收益率（(close[i+10] - close[i])/close[i]）。
    只有当EWO低于分位线时才计算，否则为np.nan。
    """
    df_copy = df.copy()
    # 计算 EWO 指标，这里默认快速EMA为5，慢速EMA为35，与 calculate_EWO 一致
    ewo = calculate_EWO(df_copy)
    ewo_lower_band = ewo.quantile(0.1)
    df_copy['ewo'] = ewo
    df_copy['Label'] = np.nan
    for i in range(len(df_copy)):
        if df_copy['ewo'].iloc[i] < ewo_lower_band:
            if i + 10 < len(df_copy):
                ret = (df_copy['close'].iloc[i+10] - df_copy['close'].iloc[i]) / df_copy['close'].iloc[i]
                df_copy.at[i, 'Label'] = ret
            else:
                df_copy.at[i, 'Label'] = np.nan
        else:
            df_copy.at[i, 'Label'] = np.nan
    return df_copy['Label']

def label_bollinger_flat_down(df, period=20, std_dev=2, flat_threshold=0.00025):
    """
    标记布林带上下中轨均“非常水平”时，当前K线往后10根K线的收益率（(close[i+10] - close[i])/close[i]）。
    水平度采用: abs((轨[i] - 轨[i-1])/轨[i-1]) < 阈值 三轨均满足为“水平”。
    参数:
        period: 布林带周期
        std_dev: 标准差倍数
        flat_threshold: 水平阈值（建议0.0005或更小）
    返回:
        pd.Series，满足条件时为收益率，否则为nan
    """
    unflat_threshold=0.00025
    df_copy = df.copy()
    # 计算三条布林带
    upper, middle, lower = calculate_bollinger_bands(df_copy, period=period, std_dev=std_dev)
    df_copy['bb_upper'] = upper
    df_copy['bb_middle'] = middle
    df_copy['bb_lower'] = lower

    # 计算三轨的“水平度”（近似绝对斜率）
    df_copy['upper_slope'] = abs(df_copy['bb_upper'].pct_change())
    df_copy['middle_slope'] = abs(df_copy['bb_middle'].pct_change())
    df_copy['lower_slope'] = abs(df_copy['bb_lower'].pct_change())

    df_copy['Label'] = np.nan
    for i in range(1, len(df_copy)):
        # 增加判断：
        # 1. 当前K线实体触碰中轨且越过下轨
        # 2. 当前K线收盘价与下轨很接近但没穿过下轨，并且当前k线是“十字星”形状
        
        # 修改后的 cond_1：收盘价低于开盘，收盘价低于下轨，开盘价高于中轨
        cond_1 = (
            (df_copy['close'].iloc[i] < df_copy['open'].iloc[i]) and
            (df_copy['close'].iloc[i] < df_copy['bb_lower'].iloc[i]) and
            (df_copy['open'].iloc[i] > df_copy['bb_middle'].iloc[i])
        )

        # 判断条件2：当前K线收盘价与下轨接近但没跌破，且是十字星
        # 十字星定义为 |open-close| <= 0.15*|high-low|，可调整
        body = abs(df_copy['open'].iloc[i] - df_copy['close'].iloc[i])
        candle_len = abs(df_copy['high'].iloc[i] - df_copy['low'].iloc[i])
        is_doji = candle_len > 0 and (body / candle_len) <= 0.25
        is_near_lower = (
            (df_copy['close'].iloc[i] > df_copy['bb_lower'].iloc[i]) and
            (abs(df_copy['close'].iloc[i] - df_copy['bb_lower'].iloc[i]) / df_copy['bb_lower'].iloc[i] < 0.01) # 0.2%内接近
        )
        cond_2 = is_doji and is_near_lower

        if (
            (df_copy['upper_slope'].iloc[i-1] < flat_threshold)
            and (df_copy['middle_slope'].iloc[i-1] < flat_threshold)
            and (df_copy['lower_slope'].iloc[i-1] < flat_threshold)
            and (
                (df_copy['upper_slope'].iloc[i] > unflat_threshold)
                or (df_copy['middle_slope'].iloc[i] > unflat_threshold)
                or (df_copy['lower_slope'].iloc[i] > unflat_threshold)
            )
            and (cond_1 or cond_2)
        ):
            if i + 2 < len(df_copy):
                ret = (df_copy['close'].iloc[i + 2] - df_copy['close'].iloc[i]) / df_copy['close'].iloc[i]
                df_copy.at[i, 'Label'] = ret
            else:
                df_copy.at[i, 'Label'] = np.nan
        else:
            df_copy.at[i, 'Label'] = np.nan

    return df_copy['Label']


def label_vwap(df):
    """
    标记：当当前K线收盘价低于calculate_vwapb输出的下轨时，
    则以当前K线往后5根K线的收益率 ((close[i+10] - close[i]) / close[i])作为标签，
    否则标记为 np.nan.
    """
    df_copy = df.copy()
    # 计算VWAP及其Bands，下轨, 中轨, 上轨分别为vwap_low, vwap_mid, vwap_high
    vwap_low, vwap_mid, vwap_high = calculate_vwapb(df_copy,window_size=20, num_of_std=1)
    df_copy['Label'] = np.nan
    for i in range(len(df_copy)):
        if df_copy['close'].iloc[i] < vwap_low.iloc[i]:
            if i + 5 < len(df_copy):
                ret = (df_copy['close'].iloc[i + 5] - df_copy['close'].iloc[i]) / df_copy['close'].iloc[i]
                df_copy.at[i, 'Label'] = ret
            else:
                df_copy.at[i, 'Label'] = np.nan
        else:
            df_copy.at[i, 'Label'] = np.nan
    return df_copy['Label']

def label_vwap_reverse(df):
    """
    标记：当当前K线收盘价低于calculate_vwapb输出的下轨时，
    则以当前K线往后5根K线的收益率 ((close[i+10] - close[i]) / close[i])作为标签，
    否则标记为 np.nan.
    """
    df_copy = df.copy()
    # 计算VWAP及其Bands，下轨, 中轨, 上轨分别为vwap_low, vwap_mid, vwap_high
    vwap_low, vwap_mid, vwap_high = calculate_vwapb(df_copy,window_size=20, num_of_std=1)
    df_copy['Label'] = np.nan
    for i in range(len(df_copy)):
        if df_copy['close'].iloc[i] > vwap_high.iloc[i]:
            if i + 10 < len(df_copy):
                ret = (df_copy['close'].iloc[i + 10] - df_copy['close'].iloc[i]) / df_copy['close'].iloc[i]
                df_copy.at[i, 'Label'] = ret
            else:
                df_copy.at[i, 'Label'] = np.nan
        else:
            df_copy.at[i, 'Label'] = np.nan
    return df_copy['Label']

def label_imin(df):
    """
    对于IMIN5大于0.5的K线，标签为当前K线向后2根K线的收益率，否则为np.nan
    """
    df_copy = df.copy()
    df_copy['IMIN5'] = calculate_IMIN(df_copy, window_size=5)
    df_copy['Label'] = np.nan
    for i in range(len(df_copy)):
        if df_copy['IMIN5'].iloc[i] > 0.7:
            if i + 2 < len(df_copy):
                ret = (df_copy['close'].iloc[i + 2] - df_copy['close'].iloc[i]) / df_copy['close'].iloc[i]
                df_copy.at[i, 'Label'] = ret
            else:
                df_copy.at[i, 'Label'] = np.nan
        else:
            df_copy.at[i, 'Label'] = np.nan
    return df_copy['Label']

