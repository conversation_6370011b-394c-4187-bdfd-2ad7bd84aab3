# -*- coding: utf-8 -*-
import os, random
import numpy as np
import pandas as pd
import lightgbm as lgb
import hnswlib
from metric_learn import ITML_Supervised
from scipy.sparse import csc_matrix
from sklearn.preprocessing import StandardScaler

# -------------------------------
# 1. 数据准备：加载 ETH K 线并做特征工程
# -------------------------------
def load_eth_klines(path_csv):
    """
    假设 path_csv 包含 datetime, open, high, low, close, volume
    """
    df = pd.read_csv(path_csv, parse_dates=['datetime'])
    df = df.sort_values('datetime').reset_index(drop=True)
    # 计算 n 日后收益率，如 PCT20, PCT100
    for n in [20, 100]:
        df[f'ret_{n}'] = df['close'].shift(-n) / df['close'] - 1
    # 基础技术指标（可根据需要扩展）
    df['ma5']  = df['close'].rolling(5).mean()
    df['ma20'] = df['close'].rolling(20).mean()
    df['std5'] = df['close'].rolling(5).std()
    df['rsi14'] = compute_rsi(df['close'], 14)
    df = df.dropna().reset_index(drop=True)
    return df

def compute_rsi(close, period=14):
    delta = close.diff()
    up = delta.clip(lower=0)
    down = -delta.clip(upper=0)
    ma_up = up.rolling(period).mean()
    ma_down = down.rolling(period).mean()
    rs = ma_up / (ma_down + 1e-8)
    return 100 - 100 / (1 + rs)

# -------------------------------
# 2. 划分训练/测试集
# -------------------------------
def train_test_split_by_date(df, date_col='datetime',
                             train_start=None, train_end=None,
                             test_start=None, test_end=None):
    df['date_str'] = df[date_col].dt.strftime('%Y%m%d')
    if train_start is None: train_start = df['date_str'].iloc[0]
    if train_end   is None: train_end   = df['date_str'].iloc[-1]
    if test_start  is None: test_start  = train_end
    if test_end    is None: test_end    = df['date_str'].iloc[-1]
    train = df[(df['date_str']>=train_start)&(df['date_str']<=train_end)]
    test  = df[(df['date_str']>=test_start )&(df['date_str']<=test_end )]
    return train.reset_index(drop=True), test.reset_index(drop=True)

# -------------------------------
# 3. 构造二分类标签 & 6 级多标签 & 等样本采样
# -------------------------------
def label_and_sample(df, ret_col='ret_20', pos_th=0.001, pcteject=1e-3):
    """返回：df_sampled (带 label)、df_ping (半样本)、df_metric_label"""
    df = df.copy()
    # 二分类 label
    df['label'] = (df[ret_col] >= pos_th).astype(int)
    # 多标签（6 级）
    met = pd.DataFrame({ret_col: df[ret_col].values})
    met['label6'] = 0
    met.loc[ met[ret_col]>= 1e-3, 'label6'] = 5
    met.loc[(met[ret_col]<1e-3)&(met[ret_col]>=5e-4),'label6']=4
    met.loc[(met[ret_col]<5e-4)&(met[ret_col]>=0),'label6']=3
    met.loc[(met[ret_col]<0)&(met[ret_col]>=-5e-4),'label6']=2
    met.loc[(met[ret_col]<-5e-4)&(met[ret_col]>=-1e-3),'label6']=1
    met.loc[ met[ret_col]<-1e-3,'label6']=0

    # 等样本采样：保证正反样本平衡，并从低收益区抽取 ping 数据
    pos = df[df['label']==1]
    neg = df[df['label']==0]
    mid = df[np.abs(df[ret_col])<pcteject]
    if len(pos) > len(neg):
        pos = pos.sample(len(neg), random_state=0)
        neg_ping = mid.sample(min(len(neg), len(mid)), random_state=0)
        df_train = pd.concat([pos, neg])
    else:
        neg = neg.sample(len(pos), random_state=0)
        pos_ping = mid.sample(min(len(pos), len(mid)), random_state=0)
        df_train = pd.concat([neg, pos])
    df_train = df_train.sample(frac=1, random_state=0).reset_index(drop=True)
    # ping 数据
    df_ping = mid.sample(len(df_train), random_state=1).reset_index(drop=True)
    # metric label 取对应行
    idx = df_train.index.union(df_ping.index)
    df_metric_label = met.loc[idx].reset_index(drop=True)

    return df_train.reset_index(drop=True), df_ping, df_metric_label

# -------------------------------
# 4. Metric Learning（ITML）
# -------------------------------
def metric_learning_ITML(x_train, y6):
    """
    输入 raw features 和 6 级标签，输出投影矩阵 model
    """
    itml = ITML_Supervised(num_constraints=1000, random_state=0)
    itml.fit(x_train.values, y6.values)
    return itml

# -------------------------------
# 5. HNSW + KNN/RNN 过滤
# -------------------------------
def knn_filter(x_base, y_label, x_query, k=100, mode='knn', radius=100):
    """
    mode: 'knn' or 'rnn'
    返回 x_query_filtered, y_label_filtered
    """
    dim = x_base.shape[1]
    p = hnswlib.Index(space='l2', dim=dim)
    p.init_index(max_elements=len(x_base), ef_construction=200, M=16)
    p.add_items(x_base.values, np.arange(len(x_base)))
    p.set_ef(200)
    inds, dists = p.knn_query(x_query.values, k=k)
    # 构造邻接矩阵 Bi→j
    row = np.repeat(np.arange(len(x_query)), k)
    col = inds.reshape(-1)
    if mode=='knn':
        data = np.ones_like(col, dtype=float)
    else:  # rnn
        d = dists.reshape(-1)
        d[d>radius]=0
        inv = np.where(d>0, 1/d, 0)
        data = inv
    A = csc_matrix((data, (row, col)), shape=(len(x_query), len(x_base)))
    # 计算 B * label
    lbl = y_label['label'].values
    arr = (A.dot(lbl.reshape(-1,1))).A.ravel()
    mask = arr>=0
    return x_query.loc[mask].reset_index(drop=True), y_label.loc[mask].reset_index(drop=True)

# -------------------------------
# 6. Dual-Label 迭代训练
# -------------------------------
def dual_label_training(x_tr, y_tr, params, dual_ratio=0.01, dual_times=5):
    """
    输入：x_tr/raw, y_tr(含 proba, label), LightGBM params
    返回：模型列表 gbm_list
    """
    gbm = lgb.train(params, lgb.Dataset(x_tr, y_tr['label']))
    gbm_list = []
    for i in range(dual_times):
        y_tr['proba'] = gbm.predict(x_tr)
        # top/bottom 按比例翻标签
        num2 = int(dual_ratio * len(y_tr))
        idx_top   = y_tr['proba'].nlargest(num2).index
        idx_bottom= y_tr['proba'].nsmallest(num2).index
        # 新标签
        new_lbl = y_tr['label'].copy()
        # 正例中 top 设为正，负例中 bottom 设为正
        new_lbl.loc[idx_top]    = 1
        new_lbl.loc[idx_bottom] = 1 - new_lbl.loc[idx_bottom]
        y_tr['label'] = new_lbl
        gbm_list.append(gbm)
        gbm = lgb.train(params, lgb.Dataset(x_tr, y_tr['label']))
    return gbm_list

# -------------------------------
# 7. 主流程封装
# -------------------------------
class LARA_ETH:
    def __init__(self,
                 ret_col='ret_20',
                 pos_th=0.001,
                 pcteject=1e-3,
                 metric_method='ITML',
                 kNN=100,
                 knn_mode='knn',
                 dual_ratio=0.01,
                 dual_times=5):
        self.ret_col     = ret_col
        self.pos_th      = pos_th
        self.pcteject    = pcteject
        self.metric_method = metric_method
        self.kNN         = kNN
        self.knn_mode    = knn_mode
        self.dual_ratio  = dual_ratio
        self.dual_times  = dual_times
        self.params = {
            'objective': 'binary',
            'boosting_type': 'gbdt',
            'metric': 'auc',
            'verbose': -1,
            'seed': 0
        }

    def fit(self, df_train_raw, df_test_raw):
        # 3. 标签 & 采样
        df_tr, df_ping, df_met = label_and_sample(df_train_raw,
                                                  self.ret_col,
                                                  self.pos_th,
                                                  self.pcteject)
        # 特征列
        feats = [c for c in df_tr.columns if c not in ['datetime','label','date_str']]

        # 4. Metric Learning
        if self.metric_method=='ITML':
            m_model = metric_learning_ITML(df_tr[feats], df_met['label6'])
            X_tr = pd.DataFrame(m_model.transform(df_tr[feats]),
                                columns=feats)
            X_ping = pd.DataFrame(m_model.transform(df_ping[feats]),
                                  columns=feats)
            X_te  = pd.DataFrame(m_model.transform(df_test_raw[feats]),
                                  columns=feats)
        else:
            X_tr, X_ping, X_te = df_tr[feats], df_ping[feats], df_test_raw[feats]

        # 合并 ping
        X_tr = pd.concat([X_tr, X_ping],axis=0).reset_index(drop=True)
        y_tr = pd.concat([df_tr[['label']], df_ping[['label']]],axis=0).reset_index(drop=True)
        X_te, y_te = X_te.reset_index(drop=True), df_test_raw[['label']].reset_index(drop=True)

        # 5. KNN 过滤
        X_te, y_te = knn_filter(X_tr, y_tr, X_te,
                                k=self.kNN, mode=self.knn_mode)

        # 6. Dual Label + LightGBM
        # 初次训练 & 迭代
        gbm_list = dual_label_training(X_tr.copy(),
                                       y_tr.copy(),
                                       self.params,
                                       dual_ratio=self.dual_ratio,
                                       dual_times=self.dual_times)
        # 7. 集成预测
        preds = []
        for m in gbm_list:
            preds.append(m.predict(X_te))
        self.signal = np.mean(preds, axis=0)
        self.y_te   = y_te[self.ret_col.replace('ret_','')].values if self.ret_col.startswith('ret_') else y_te.values

    def predict_signal(self):
        return self.signal

# -------------------------------
# 8. 评估指标
# -------------------------------
def test_index(signal, returns, top_k=100, pos_th=0.001):
    df = pd.DataFrame({'sig':signal, 'ret':returns})
    df = df.sort_values('sig', ascending=False).iloc[:top_k]
    df['label'] = (df['ret']>=pos_th).astype(int)
    prec = df['label'].mean()
    win = df.loc[df['ret']>0,'ret'].mean()
    loss= df.loc[df['ret']<0,'ret'].mean()
    wlr = abs(win/loss) if loss<0 else np.nan
    ar  = df['ret'].mean()
    return prec, wlr, ar

# -------------------------------
# 9. 运行示例
# -------------------------------
if __name__=='__main__':
    # 1. 加载数据
    df = load_eth_klines('eth_1h.csv')  # 替换你的 ETH K 线文件
    # 2. 划分
    df_tr, df_te = train_test_split_by_date(df,
                                            train_start='20200101',
                                            train_end='20211231',
                                            test_start ='20220101',
                                            test_end   ='20220630')
    # 3~7. 训练 & 预测
    model = LARA_ETH(ret_col='ret_20',
                     pos_th=0.001,
                     pcteject=1e-3,
                     metric_method='ITML',
                     kNN=100,
                     knn_mode='knn',
                     dual_ratio=0.01,
                     dual_times=5)
    model.fit(df_tr, df_te)
    sig = model.predict_signal()
    # 8. 评估
    prec, wlr, ar = test_index(sig,
                               df_te['ret_20'].values,
                               top_k=100,
                               pos_th=0.001)
    print(f'Precision={prec:.4f}, Win/Loss={wlr:.4f}, AvgRet={ar:.4f}')
