# eth_data_processor.py
import pandas as pd
import numpy as np
# 引入您可能需要的技术指标库, 例如 talib
# import talib
from tqdm import tqdm

class ETHDataProcessor:
    def __init__(self, ohlcv_df, instrument_name='ETHUSDT'):
        """
        初始化数据处理器
        :param ohlcv_df: Pandas DataFrame, 包含ETH的OHLCV数据，
                         必须有 'datetime' (pd.Timestamp), 'open', 'high', 'low', 'close', 'volume' 列。
                         'datetime' 列应该是唯一的，并已排序。
        :param instrument_name: 字符串, 交易对的名称，如 'ETHUSDT'
        """
        self.df = ohlcv_df.copy()
        self.instrument_name = instrument_name
        self.feature_columns = []
        self.label_column_name = 'label_return' # LARA中的 self.out

        if not isinstance(self.df.index, pd.DatetimeIndex):
            if 'datetime' in self.df.columns:
                self.df['datetime'] = pd.to_datetime(self.df['datetime'])
                self.df = self.df.set_index('datetime', drop=True)
            else:
                raise ValueError("DataFrame必须包含 'datetime' 列或拥有 DatetimeIndex.")
        self.df = self.df.sort_index()


    def generate_features_alpha158(self):
        """
        生成158个技术指标因子，计算方法基于Alpha158DL中的158因子公式。
        包含：
          1. kbar因子 (9个因子)
          2. Price因子（仅使用OPEN, HIGH, LOW, CLOSE; window=0，共4个因子）
          3. Rolling因子 (29组因子，每组对窗口[w]计算, w in [5,10,20,30,60]，共145个因子)
        """
        eps = 1e-12
        df = self.df.copy()

        # 1. KBar因子 (9个因子)
        df['feature_KMID']   = (df['close'] - df['open']) / df['open']
        df['feature_KLEN']   = (df['high'] - df['low']) / df['open']
        df['feature_KMID2']  = (df['close'] - df['open']) / (df['high'] - df['low'] + eps)
        df['feature_KUP']    = (df['high'] - df[['open', 'close']].max(axis=1)) / df['open']
        df['feature_KUP2']   = (df['high'] - df[['open', 'close']].max(axis=1)) / (df['high'] - df['low'] + eps)
        df['feature_KLOW']   = (df[['open', 'close']].min(axis=1) - df['low']) / df['open']
        df['feature_KLOW2']  = (df[['open', 'close']].min(axis=1) - df['low']) / (df['high'] - df['low'] + eps)
        df['feature_KSFT']   = (2 * df['close'] - df['high'] - df['low']) / df['open']
        df['feature_KSFT2']  = (2 * df['close'] - df['high'] - df['low']) / (df['high'] - df['low'] + eps)

        # 2. Price因子 (仅使用OPEN, HIGH, LOW, CLOSE; window=0), 共4个因子
        for field in ['open', 'high', 'low', 'close']:
            col_name = f'feature_PRICE_{field.upper()}0'
            df[col_name] = df[field] / df['close']

        # 3. Rolling因子 (29组因子，每组在窗口 [5,10,20,30,60] 下计算，共145个因子)
        windows = [5, 10, 20, 30, 60]

        # 定义滚动线性回归函数，返回斜率、R平方和最后一个残差
        def rolling_linreg(x):
            t = np.arange(len(x))
            if np.all(np.isnan(x)):
                return (np.nan, np.nan, np.nan)
            try:
                slope, intercept = np.polyfit(t, x, 1)
                pred = slope * t + intercept
                ss_res = np.sum((x - pred) ** 2)
                ss_tot = np.sum((x - np.mean(x)) ** 2)
                r2 = 1 - ss_res / ss_tot if ss_tot != 0 else np.nan
                resid = x[-1] - (slope * (len(x) - 1) + intercept)
            except Exception:
                slope, r2, resid = np.nan, np.nan, np.nan
            return slope, r2, resid

        for w in windows:
            # ROC: 前w期的close与当前close比值
            df[f'feature_ROC{w}'] = df['close'].shift(w) / df['close']

            # MA: w期移动平均
            df[f'feature_MA{w}'] = df['close'].rolling(window=w).mean() / df['close']

            # STD: w期标准差
            df[f'feature_STD{w}'] = df['close'].rolling(window=w).std() / df['close']

            # BETA, RSQR, RESI：采用滚动线性回归计算
            df[f'feature_BETA{w}'] = df['close'].rolling(window=w).apply(lambda x: rolling_linreg(x)[0], raw=True) / df['close']
            df[f'feature_RSQR{w}'] = df['close'].rolling(window=w).apply(lambda x: rolling_linreg(x)[1], raw=True)
            df[f'feature_RESI{w}'] = df['close'].rolling(window=w).apply(lambda x: rolling_linreg(x)[2], raw=True) / df['close']

            # MAX: w期内最高价除以当前close
            df[f'feature_MAX{w}'] = df['high'].rolling(window=w).max() / df['close']

            # LOW: w期内最低价除以当前close
            df[f'feature_LOW{w}'] = df['low'].rolling(window=w).min() / df['close']

            # QTLU: w期内close的0.8分位数
            df[f'feature_QTLU{w}'] = df['close'].rolling(window=w).quantile(0.8) / df['close']

            # QTLD: w期内close的0.2分位数
            df[f'feature_QTLD{w}'] = df['close'].rolling(window=w).quantile(0.2) / df['close']

            # RANK: 当前close在w期内的百分位排名
            df[f'feature_RANK{w}'] = df['close'].rolling(window=w).apply(lambda x: pd.Series(x).rank(pct=True).iloc[-1], raw=False)

            # RSV: (close - w期内最低低价) / (w期内最高价 - w期内最低低价)
            roll_low  = df['low'].rolling(window=w).min()
            roll_high = df['high'].rolling(window=w).max()
            df[f'feature_RSV{w}'] = (df['close'] - roll_low) / (roll_high - roll_low + eps)

            # IMAX: w期内最高价出现位置（归一化）
            df[f'feature_IMAX{w}'] = df['high'].rolling(window=w).apply(lambda x: np.argmax(x) / w, raw=True)

            # IMIN: w期内最低价出现位置（归一化）
            df[f'feature_IMIN{w}'] = df['low'].rolling(window=w).apply(lambda x: np.argmin(x) / w, raw=True)

            # IMXD: 最高价和最低价位置差（归一化）
            imax = df['high'].rolling(window=w).apply(lambda x: np.argmax(x), raw=True)
            imin = df['low'].rolling(window=w).apply(lambda x: np.argmin(x), raw=True)
            df[f'feature_IMXD{w}'] = (imax - imin) / w

            # CORR: w期内close与log(volume+1)的相关系数
            log_vol = np.log(df['volume'] + 1)
            df[f'feature_CORR{w}'] = df['close'].rolling(window=w).corr(log_vol)

            # CORD: w期内 (close/前一日close) 与 log( volume/前一日volume + 1) 的相关系数
            ret = df['close'] / df['close'].shift(1)
            vol_ret = np.log(df['volume'] / df['volume'].shift(1) + 1)
            df[f'feature_CORD{w}'] = ret.rolling(window=w).corr(vol_ret)

            # CNTP: w期内上涨比例
            df[f'feature_CNTP{w}'] = df['close'].diff().rolling(window=w).apply(lambda x: np.mean(x > 0))

            # CNTN: w期内下跌比例
            df[f'feature_CNTN{w}'] = df['close'].diff().rolling(window=w).apply(lambda x: np.mean(x < 0))

            # CNTD: 上涨比例与下跌比例之差
            df[f'feature_CNTD{w}'] = df[f'feature_CNTP{w}'] - df[f'feature_CNTN{w}']

            # SUMP: 上涨天数占比 (正差的天数/绝对变化之和)
            def sump(x):
                pos = np.sum(x > 0)
                denom = np.sum(np.abs(x))
                return pos / (denom + eps)
            df[f'feature_SUMP{w}'] = df['close'].diff().rolling(window=w).apply(sump, raw=True)

            # SUMN: 下跌天数占比 (负差的天数/绝对变化之和)
            def sumn(x):
                neg = np.sum(x < 0)
                denom = np.sum(np.abs(x))
                return neg / (denom + eps)
            df[f'feature_SUMN{w}'] = df['close'].diff().rolling(window=w).apply(sumn, raw=True)

            # SUMD: SUMP与SUMN的差值
            df[f'feature_SUMD{w}'] = df[f'feature_SUMP{w}'] - df[f'feature_SUMN{w}']

            # VMA: w期内成交量均值与当前成交量比值
            df[f'feature_VMA{w}'] = df['volume'].rolling(window=w).mean() / (df['volume'] + eps)

            # VSTD: w期内成交量标准差与当前成交量比值
            df[f'feature_VSTD{w}'] = df['volume'].rolling(window=w).std() / (df['volume'] + eps)

            # WVMA: 利用成交量加权的波动率指标
            vol_factor = abs(df['close'] / df['close'].shift(1) - 1) * df['volume']
            df[f'feature_WVMA{w}'] = vol_factor.rolling(window=w).std() / (vol_factor.rolling(window=w).mean() + eps)

            # VSUMP: w期内成交量上升日占比
            def vsump(x):
                pos = np.sum(x > 0)
                denom = np.sum(np.abs(x))
                return pos / (denom + eps)
            df[f'feature_VSUMP{w}'] = df['volume'].diff().rolling(window=w).apply(vsump, raw=True)

            # VSUMN: w期内成交量下降日占比
            def vsumn(x):
                neg = np.sum(x < 0)
                denom = np.sum(np.abs(x))
                return neg / (denom + eps)
            df[f'feature_VSUMN{w}'] = df['volume'].diff().rolling(window=w).apply(vsumn, raw=True)

            # VSUMD: VSUMP与VSUMN的差值
            df[f'feature_VSUMD{w}'] = df[f'feature_VSUMP{w}'] - df[f'feature_VSUMN{w}']

        # 记录所有生成的特征列名
        self.feature_columns = [col for col in df.columns if col.startswith('feature_')]
        print(f"Generated {len(self.feature_columns)} features.")
        self.df = df
        return self.df
    
    def generate_features_alpha360(self):
        """
        根据Alpha360DL里面360个因子的计算方法生成特征因子，共360个因子。
        每组因子的计算方法如下:
          CLOSE系列: (过去lag期的close)/(当前close)，lag取值59~1，lag=0时为 $close/$close
          OPEN系列: (过去lag期的open)/(当前close)
          HIGH系列: (过去lag期的high)/(当前close)
          LOW系列: (过去lag期的low)/(当前close)
          VWAP系列: (过去lag期的vwap)/(当前close)，如果vwap不存在，则用 (high+low+close)/3
          VOLUME系列: (过去lag期的volume)/(volume+1e-12)，lag=0时为 $volume/($volume+1e-12)
        """
        df = self.df.copy()
        # 如果vwap不存在，则计算vwap = (high + low + close) / 3
        if 'vwap' not in df.columns:
            df['vwap'] = (df['high'] + df['low'] + df['close']) / 3

        # CLOSE系列
        for lag in range(59, 0, -1):
            df[f'feature_CLOSE{lag}'] = df['close'].shift(lag) / df['close']
        df['feature_CLOSE0'] = df['close'] / df['close']

        # OPEN系列
        for lag in range(59, 0, -1):
            df[f'feature_OPEN{lag}'] = df['open'].shift(lag) / df['close']
        df['feature_OPEN0'] = df['open'] / df['close']

        # HIGH系列
        for lag in range(59, 0, -1):
            df[f'feature_HIGH{lag}'] = df['high'].shift(lag) / df['close']
        df['feature_HIGH0'] = df['high'] / df['close']

        # LOW系列
        for lag in range(59, 0, -1):
            df[f'feature_LOW{lag}'] = df['low'].shift(lag) / df['close']
        df['feature_LOW0'] = df['low'] / df['close']

        # VWAP系列
        for lag in range(59, 0, -1):
            df[f'feature_VWAP{lag}'] = df['vwap'].shift(lag) / df['close']
        df['feature_VWAP0'] = df['vwap'] / df['close']

        # VOLUME系列
        for lag in range(59, 0, -1):
            df[f'feature_VOLUME{lag}'] = df['volume'].shift(lag) / (df['volume'] + 1e-12)
        df['feature_VOLUME0'] = df['volume'] / (df['volume'] + 1e-12)

        # 更新特征列并返回处理后的df
        self.feature_columns = [col for col in df.columns if col.startswith('feature_')]
        print(f"Generated {len(self.feature_columns)} features.")
        self.df = df
        return self.df
    
    def generate_features_alpha101(self):
        """
        生成101个Alpha因子，计算方法基于WorldQuant的《101 Formulaic Alphas》。
        
        该实现将原始的为股票市场（横截面数据）设计的公式适配到单一金融工具（如ETHUSDT）的时间序列上。
        主要适配和假设如下：
        1.  `rank(x)`: 原始定义为横截面排名。此处适配为时间序列上的滚动排名（百分位），
            即计算当前值在过去N个时间点中的排名。默认窗口期设为10。
        2.  `indneutralize(x, IndClass)`: 行业中性化操作。在单一工具场景下无意义，因此该函数为恒等变换 (no-op)，直接返回输入x。
        3.  `scale(x)`: 原始定义为使x的绝对值之和为1的横截面缩放。在单一时间序列中，此操作意义不明确，因此作为恒等变换处理。
        4.  `cap`: 市值。由于OHLCV数据不包含市值，`cap` 在公式中被假定为1，从而消除其影响。
        5.  非整数窗口/参数: 公式中的浮点数参数（如窗口大小）被四舍五入到最近的整数。
        6.  健壮性: 每个alpha的计算都包裹在 try-except 块中，以防止因数据问题（如NaN值、除零）导致整个过程失败。
        """
        df = self.df.copy()
        alpha_list = []

        # --- Helper Functions (适配后的Alpha算子) ---
        _rank_default_window = 10 # 为`rank`设置一个较小的默认窗口以减少初始NaN

        def _rank(s, window=_rank_default_window):
            return s.rolling(window=int(window), min_periods=int(window * 0.8)).apply(lambda x: pd.Series(x).rank(pct=True).iloc[-1], raw=False)
        def _delta(s, d=1): return s.diff(int(d))
        def _delay(s, d=1): return s.shift(int(d))
        def _correlation(s1, s2, d): return s1.rolling(window=int(d)).corr(s2)
        def _covariance(s1, s2, d): return s1.rolling(window=int(d)).cov(s2)
        def _sum(s, d): return s.rolling(window=int(d)).sum()
        def _stddev(s, d): return s.rolling(window=int(d)).std()
        def _product(s, d): return s.rolling(window=int(d)).apply(np.prod, raw=True)
        def _ts_min(s, d): return s.rolling(window=int(d)).min()
        def _ts_max(s, d): return s.rolling(window=int(d)).max()
        def _ts_rank(s, d): return _rank(s, window=d)
        def _ts_argmax(s, d): return s.rolling(window=int(d)).apply(lambda x: np.argmax(x.to_numpy()), raw=False)
        def _ts_argmin(s, d): return s.rolling(window=int(d)).apply(lambda x: np.argmin(x.to_numpy()), raw=False)
        def _signed_power(s, a): return np.sign(s) * (np.abs(s) ** a)
        def _log(s): return np.log(s.replace(to_replace=0, method='ffill').abs())
        def _sign(s): return np.sign(s)
        def _scale(s, a=1): return s
        def _indneutralize(s, ind_class=None): return s
        def _decay_linear(s, d):
            d = int(d)
            weights = np.arange(1, d + 1)
            return s.rolling(window=d).apply(lambda x: np.dot(x, weights) / weights.sum() if not np.isnan(x).any() else np.nan, raw=True)

        # --- Pre-calculation of common variables ---
        open_ = df['open']
        high = df['high']
        low = df['low']
        close = df['close']
        volume = df['volume'].replace(0, 1e-12) # 避免除零
        
        if 'vwap' not in df.columns: df['vwap'] = (high + low + close) / 3
        vwap = df['vwap']
        
        returns = close.pct_change().fillna(0)
        
        adv_periods = {5, 10, 15, 20, 30, 40, 50, 60, 81, 120, 180, 240, 250}
        advs = {d: _sum(volume, d) / d for d in adv_periods}
        def adv(d):
            d = int(d)
            if d not in advs: advs[d] = _sum(volume, d) / d
            return advs[d]

        # --- Alpha Calculations ---
        for i in tqdm(range(1, 102), desc="Alpha计算进度"):
            alpha_name = f'feature_alpha_{i:03d}'
            try:
                #if   i == 1:   alpha = (_rank(_ts_argmax(_signed_power(np.where(returns < 0, _stddev(returns, 20), close), 2.), 5)) - 0.5)
                if i == 1:
                    temp = pd.Series(np.where(returns < 0, _stddev(returns, 20), close), index=returns.index)
                    alpha = (_rank(_ts_argmax(_signed_power(temp, 2.), 5)) - 0.5)
                elif i == 2:   alpha = (-1 * _correlation(_rank(_delta(_log(volume), 2)), _rank(((close - open_) / open_)), 6))
                elif i == 3:   alpha = (-1 * _correlation(_rank(open_), _rank(volume), 10))
                elif i == 4:   alpha = (-1 * _ts_rank(_rank(low), 9))
                elif i == 5:   alpha = (_rank((open_ - (_sum(vwap, 10) / 10))) * (-1 * abs(_rank((close - vwap)))))
                elif i == 6:   alpha = (-1 * _correlation(open_, volume, 10))
                elif i == 7:   alpha = np.where(adv(20) < volume, (-1 * _ts_rank(abs(_delta(close, 7)), 60)) * _sign(_delta(close, 7)), -1)
                elif i == 8:   alpha = (-1 * _rank(((_sum(open_, 5) * _sum(returns, 5)) - _delay((_sum(open_, 5) * _sum(returns, 5)), 10))))
                elif i == 9:
                    delta_close_1 = _delta(close, 1)
                    cond1 = _ts_min(delta_close_1, 5) > 0
                    cond2 = _ts_max(delta_close_1, 5) < 0
                    alpha = -1 * delta_close_1
                    alpha[cond1] = delta_close_1
                    alpha[cond2] = delta_close_1
                elif i == 10:
                    delta_close_1 = _delta(close, 1)
                    cond1 = _ts_min(delta_close_1, 4) > 0
                    cond2 = _ts_max(delta_close_1, 4) < 0
                    val = -1 * delta_close_1
                    val[cond1] = delta_close_1
                    val[cond2] = delta_close_1
                    alpha = _rank(val)
                elif i == 11:  alpha = ((_rank(_ts_max((vwap - close), 3)) + _rank(_ts_min((vwap - close), 3))) * _rank(_delta(volume, 3)))
                elif i == 12:  alpha = (_sign(_delta(volume, 1)) * (-1 * _delta(close, 1)))
                elif i == 13:  alpha = (-1 * _rank(_covariance(_rank(close), _rank(volume), 5)))
                elif i == 14:  alpha = ((-1 * _rank(_delta(returns, 3))) * _correlation(open_, volume, 10))
                elif i == 15:  alpha = (-1 * _sum(_rank(_correlation(_rank(high), _rank(volume), 3)), 3))
                elif i == 16:  alpha = (-1 * _rank(_covariance(_rank(high), _rank(volume), 5)))
                elif i == 17:  alpha = (((-1 * _rank(_ts_rank(close, 10))) * _rank(_delta(_delta(close, 1), 1))) * _rank(_ts_rank((volume / adv(20)), 5)))
                elif i == 18:  alpha = (-1 * _rank(((_stddev(abs((close - open_)), 5) + (close - open_)) + _correlation(close, open_, 10))))
                elif i == 19:  alpha = ((-1 * _sign(((close - _delay(close, 7)) + _delta(close, 7)))) * (1 + _rank((1 + _sum(returns, 250)))))
                elif i == 20:  alpha = (((-1 * _rank((open_ - _delay(high, 1)))) * _rank((open_ - _delay(close, 1)))) * _rank((open_ - _delay(low, 1))))
                elif i == 21:
                    cond1 = (_sum(close, 8) / 8 + _stddev(close, 8)) < (_sum(close, 2) / 2)
                    cond2 = (_sum(close, 2) / 2) < (_sum(close, 8) / 8 - _stddev(close, 8))
                    cond3 = (volume / adv(20)) >= 1
                    alpha = pd.Series(np.nan, index=df.index)
                    alpha[cond1] = -1
                    alpha[cond2] = 1
                    alpha[~(cond1 | cond2)] = np.where(cond3[~(cond1 | cond2)], 1, -1)
                elif i == 22:  alpha = (-1 * (_delta(_correlation(high, volume, 5), 5) * _rank(_stddev(close, 20))))
                elif i == 23:  alpha = np.where((_sum(high, 20) / 20) < high, -1 * _delta(high, 2), 0)
                elif i == 24:  alpha = np.where(((_delta((_sum(close, 100) / 100), 100) / _delay(close, 100)) <= 0.05), (-1 * (close - _ts_min(close, 100))), (-1 * _delta(close, 3)))
                elif i == 25:  alpha = _rank(((((-1 * returns) * adv(20)) * vwap) * (high - close)))
                elif i == 26:  alpha = (-1 * _ts_max(_correlation(_ts_rank(volume, 5), _ts_rank(high, 5), 5), 3))
                elif i == 27:  alpha = np.where(0.5 < _rank((_sum(_correlation(_rank(volume), _rank(vwap), 6), 2) / 2.0)), -1, 1)
                elif i == 28:  alpha = _scale(((_correlation(adv(20), low, 5) + ((high + low) / 2)) - close))
                elif i == 29:  alpha = (_ts_min(_product(_rank(_rank(_scale(_log(_sum(_ts_min(_rank(_rank((-1 * _rank(_delta((close - 1), 5))))), 2), 1))))), 1), 5) + _ts_rank(_delay((-1 * returns), 6), 5))
                elif i == 30:  alpha = (((1.0 - _rank(((_sign((close - _delay(close, 1))) + _sign((_delay(close, 1) - _delay(close, 2)))) + _sign((_delay(close, 2) - _delay(close, 3)))))) * _sum(volume, 5)) / _sum(volume, 20))
                elif i == 31:  alpha = ((_rank(_rank(_rank(_decay_linear((-1 * _rank(_rank(_delta(close, 10)))), 10)))) + _rank((-1 * _delta(close, 3)))) + _sign(_scale(_correlation(adv(20), low, 12))))
                elif i == 32:  alpha = (_scale(((_sum(close, 7) / 7) - close)) + (20 * _scale(_correlation(vwap, _delay(close, 5), 230))))
                elif i == 33:  alpha = _rank((-1 * ((1 - (open_ / close))**1)))
                elif i == 34:  alpha = _rank(((1 - _rank((_stddev(returns, 2) / _stddev(returns, 5)))) + (1 - _rank(_delta(close, 1)))))
                elif i == 35:  alpha = ((_ts_rank(volume, 32) * (1 - _ts_rank(((close + high) - low), 16))) * (1 - _ts_rank(returns, 32)))
                elif i == 36:  alpha = (((((2.21 * _rank(_correlation((close - open_), _delay(volume, 1), 15))) + (0.7 * _rank((open_ - close)))) + (0.73 * _rank(_ts_rank(_delay((-1 * returns), 6), 5)))) + _rank(abs(_correlation(vwap, adv(20), 6)))) + (0.6 * _rank((((_sum(close, 200) / 200) - open_) * (close - open_)))))
                elif i == 37:  alpha = (_rank(_correlation(_delay((open_ - close), 1), close, 200)) + _rank((open_ - close)))
                elif i == 38:  alpha = ((-1 * _rank(_ts_rank(close, 10))) * _rank((close / open_)))
                elif i == 39:  alpha = ((-1 * _rank((_delta(close, 7) * (1 - _rank(_decay_linear((volume / adv(20)), 9)))))) * (1 + _rank(_sum(returns, 250))))
                elif i == 40:  alpha = ((-1 * _rank(_stddev(high, 10))) * _correlation(high, volume, 10))
                elif i == 41:  alpha = (((high * low)**0.5) - vwap)
                elif i == 42:  alpha = (_rank((vwap - close)) / _rank((vwap + close)))
                elif i == 43:  alpha = (_ts_rank((volume / adv(20)), 20) * _ts_rank((-1 * _delta(close, 7)), 8))
                elif i == 44:  alpha = (-1 * _correlation(high, _rank(volume), 5))
                elif i == 45:  alpha = (-1 * ((_rank((_sum(_delay(close, 5), 20) / 20)) * _correlation(close, volume, 2)) * _rank(_correlation(_sum(close, 5), _sum(close, 20), 2))))
                elif i == 46:
                    val = (((_delay(close, 20) - _delay(close, 10)) / 10) - ((_delay(close, 10) - close) / 10))
                    alpha = np.where(0.25 < val, -1, np.where(val < 0, 1, -1 * (close - _delay(close, 1))))
                elif i == 47:  alpha = ((((_rank((1 / close)) * volume) / adv(20)) * ((high * _rank((high - close))) / (_sum(high, 5) / 5))) - _rank((vwap - _delay(vwap, 5))))
                elif i == 48: # IndNeutralize and subindustry class are no-ops for single instrument
                    term1 = (_correlation(_delta(close, 1), _delta(_delay(close, 1), 1), 250) * _delta(close, 1) / close)
                    term2 = _sum(((_delta(close, 1) / _delay(close, 1))**2), 250)
                    alpha = _indneutralize(term1) / term2
                elif i == 49:  alpha = np.where(((((_delay(close, 20) - _delay(close, 10)) / 10) - ((_delay(close, 10) - close) / 10)) < (-1 * 0.1)), 1, (-1 * (close - _delay(close, 1))))
                elif i == 50:  alpha = (-1 * _ts_max(_rank(_correlation(_rank(volume), _rank(vwap), 5)), 5))
                elif i == 51:  alpha = np.where(((((_delay(close, 20) - _delay(close, 10)) / 10) - ((_delay(close, 10) - close) / 10)) < (-1 * 0.05)), 1, (-1 * (close - _delay(close, 1))))
                elif i == 52:  alpha = ((((-1 * _ts_min(low, 5)) + _delay(_ts_min(low, 5), 5)) * _rank(((_sum(returns, 240) - _sum(returns, 20)) / 220))) * _ts_rank(volume, 5))
                elif i == 53:  alpha = (-1 * _delta((((close - low) - (high - close)) / (close - low).replace(0, 1e-9)), 9))
                elif i == 54:  alpha = ((-1 * ((low - close) * (open_**5))) / ((low - high).replace(0, 1e-9) * (close**5)))
                elif i == 55:  alpha = (-1 * _correlation(_rank((((close - _ts_min(low, 12)) / (_ts_max(high, 12) - _ts_min(low, 12)).replace(0, 1e-9)))), _rank(volume), 6))
                elif i == 56:  alpha = (0 - (1 * (_rank((_sum(returns, 10) / _sum(_sum(returns, 2), 3))) * _rank((returns * 1))))) # cap is 1
                elif i == 57:  alpha = (0 - (1 * ((close - vwap) / _decay_linear(_rank(_ts_argmax(close, 30)), 2))))
                elif i == 58:  alpha = (-1 * _ts_rank(_decay_linear(_correlation(_indneutralize(vwap), volume, 4), 8), 6))
                elif i == 59:  alpha = (-1 * _ts_rank(_decay_linear(_correlation(_indneutralize(((vwap * 0.728317) + (vwap * (1 - 0.728317)))), volume, 4), 16), 8))
                elif i == 60:  alpha = (0 - (1 * ((2 * _scale(_rank(((((close - low) - (high - close)) / (high - low).replace(0, 1e-9)) * volume)))) - _scale(_rank(_ts_argmax(close, 10))))))
                elif i == 61:  alpha = (_rank((vwap - _ts_min(vwap, 16))) < _rank(_correlation(vwap, adv(180), 18))).astype(int)
                elif i == 62:  alpha = ((_rank(_correlation(vwap, _sum(adv(20), 22), 10)) < _rank(((_rank(open_) + _rank(open_)) < (_rank(((high + low) / 2)) + _rank(high))))) * -1)
                elif i == 63:  alpha = ((_rank(_decay_linear(_delta(_indneutralize(close), 2), 8)) - _rank(_decay_linear(_correlation(((vwap * 0.318108) + (open_ * (1 - 0.318108))), _sum(adv(180), 37), 14), 12))) * -1)
                elif i == 64:  alpha = ((_rank(_correlation(_sum(((open_ * 0.178404) + (low * (1 - 0.178404))), 13), _sum(adv(120), 13), 17)) < _rank(_delta(((((high + low) / 2) * 0.178404) + (vwap * (1 - 0.178404))), 4))) * -1)
                elif i == 65:  alpha = ((_rank(_correlation(((open_ * 0.00817205) + (vwap * (1 - 0.00817205))), _sum(adv(60), 9), 6)) < _rank((open_ - _ts_min(open_, 14)))) * -1)
                elif i == 66:  alpha = ((_rank(_decay_linear(_delta(vwap, 4), 7)) + _ts_rank(_decay_linear(((((low * 0.96633) + (low * (1 - 0.96633))) - vwap) / (open_ - ((high + low) / 2)).replace(0, 1e-9)), 11), 7)) * -1)
                elif i == 67:  alpha = ((_rank((high - _ts_min(high, 2)))**_rank(_correlation(_indneutralize(vwap), _indneutralize(adv(20)), 6))) * -1)
                elif i == 68:  alpha = ((_ts_rank(_correlation(_rank(high), _rank(adv(15)), 9), 14) < _rank(_delta(((close * 0.518371) + (low * (1 - 0.518371))), 1))) * -1)
                elif i == 69:  alpha = ((_rank(_ts_max(_delta(_indneutralize(vwap), 3), 5))**_ts_rank(_correlation(((close * 0.490655) + (vwap * (1 - 0.490655))), adv(20), 5), 9)) * -1)
                elif i == 70:  alpha = ((_rank(_delta(vwap, 1))**_ts_rank(_correlation(_indneutralize(close), adv(50), 18), 18)) * -1)
                elif i == 71:  alpha = np.maximum(_ts_rank(_decay_linear(_correlation(_ts_rank(close, 3), _ts_rank(adv(180), 12), 18), 4), 16), _ts_rank(_decay_linear((_rank(((low + open_) - (vwap + vwap)))**2), 16), 4))
                elif i == 72:  alpha = (_rank(_decay_linear(_correlation(((high + low) / 2), adv(40), 9), 10)) / _rank(_decay_linear(_correlation(_ts_rank(vwap, 4), _ts_rank(volume, 19), 7), 3)))
                elif i == 73:  alpha = (np.maximum(_rank(_decay_linear(_delta(vwap, 5), 3)), _ts_rank(_decay_linear(((_delta(((open_ * 0.147155) + (low * (1 - 0.147155))), 2) / ((open_ * 0.147155) + (low * (1 - 0.147155)))) * -1), 3), 17)) * -1)
                elif i == 74:  alpha = ((_rank(_correlation(close, _sum(adv(30), 37), 15)) < _rank(_correlation(_rank(((high * 0.0261661) + (vwap * (1 - 0.0261661)))), _rank(volume), 11))) * -1)
                elif i == 75:  alpha = (_rank(_correlation(vwap, volume, 4)) < _rank(_correlation(_rank(low), _rank(adv(50)), 12))).astype(int)
                elif i == 76:  alpha = (np.maximum(_rank(_decay_linear(_delta(vwap, 1), 12)), _ts_rank(_decay_linear(_ts_rank(_correlation(_indneutralize(low), adv(81), 8), 20), 17), 19)) * -1)
                elif i == 77:  alpha = np.minimum(_rank(_decay_linear(((((high + low) / 2) + high) - (vwap + high)), 20)), _rank(_decay_linear(_correlation(((high + low) / 2), adv(40), 3), 6)))
                elif i == 78:  alpha = (_rank(_correlation(_sum(((low * 0.352233) + (vwap * (1 - 0.352233))), 20), _sum(adv(40), 20), 7))**_rank(_correlation(_rank(vwap), _rank(volume), 6)))
                elif i == 79:  alpha = (_rank(_delta(_indneutralize(((close * 0.60733) + (open_ * (1 - 0.60733)))), 1)) < _rank(_correlation(_ts_rank(vwap, 4), _ts_rank(adv(150), 9), 15))).astype(int)
                elif i == 80:  alpha = ((_rank(_sign(_delta(_indneutralize(((open_ * 0.868128) + (high * (1 - 0.868128)))), 4)))**_ts_rank(_correlation(high, adv(10), 5), 6)) * -1)
                elif i == 81:  alpha = ((_rank(_log(_product(_rank((_rank(_correlation(vwap, _sum(adv(10), 50), 8))**4)), 15))) < _rank(_correlation(_rank(vwap), _rank(volume), 5))) * -1)
                elif i == 82:  alpha = (np.minimum(_rank(_decay_linear(_delta(open_, 1), 15)), _ts_rank(_decay_linear(_correlation(_indneutralize(volume), ((open_ * 0.634196) + (open_ * (1 - 0.634196))), 17), 7), 13)) * -1)
                elif i == 83:  alpha = ((_rank(_delay(((high - low) / (_sum(close, 5) / 5)), 2)) * _rank(_rank(volume))) / (((high - low) / (_sum(close, 5) / 5)) / (vwap - close).replace(0, 1e-9)))
                elif i == 84:  alpha = _signed_power(_ts_rank((vwap - _ts_max(vwap, 15)), 21), _delta(close, 5))
                #85算得巨慢暂时注释了
                # elif i == 85:  alpha = (_rank(_correlation(((high * 0.876703) + (close * (1 - 0.876703))), adv(30), 10))**_rank(_correlation(_ts_rank(((high + low) / 2), 4), _ts_rank(volume, 10), 7)))
                elif i == 86:  alpha = ((_ts_rank(_correlation(close, _sum(adv(20), 15), 6), 20) < _rank(((open_ + close) - (vwap + open_)))) * -1)
                elif i == 87:  alpha = (np.maximum(_rank(_decay_linear(_delta(((close * 0.369701) + (vwap * (1 - 0.369701))), 2), 3)), _ts_rank(_decay_linear(abs(_correlation(_indneutralize(adv(81)), close, 13)), 5), 14)) * -1)
                elif i == 88:  alpha = np.minimum(_rank(_decay_linear((( _rank(open_) + _rank(low)) - (_rank(high) + _rank(close))), 8)), _ts_rank(_decay_linear(_correlation(_ts_rank(close, 8), _ts_rank(adv(60), 21), 8), 7), 3))
                elif i == 89:  alpha = (_ts_rank(_decay_linear(_correlation(((low * 0.967285) + (low * (1 - 0.967285))), adv(10), 7), 6), 4) - _ts_rank(_decay_linear(_delta(_indneutralize(vwap), 3), 10), 15))
                elif i == 90:  alpha = ((_rank((close - _ts_max(close, 5)))**_ts_rank(_correlation(_indneutralize(adv(40)), low, 5), 3)) * -1)
                elif i == 91:  alpha = ((_ts_rank(_decay_linear(_decay_linear(_correlation(_indneutralize(close), volume, 10), 16), 4), 5) - _rank(_decay_linear(_correlation(vwap, adv(30), 4), 3))) * -1)
                elif i == 92:  alpha = np.minimum(_ts_rank(_decay_linear(((((high + low) / 2) + close) < (low + open_)), 15), 19), _ts_rank(_decay_linear(_correlation(_rank(low), _rank(adv(30)), 8), 7), 7))
                elif i == 93:  alpha = (_ts_rank(_decay_linear(_correlation(_indneutralize(vwap), adv(81), 17), 20), 8) / _rank(_decay_linear(_delta(((close * 0.524434) + (vwap * (1 - 0.524434))), 3), 16)))
                elif i == 94:  alpha = ((_rank((vwap - _ts_min(vwap, 12)))**_ts_rank(_correlation(_ts_rank(vwap, 20), _ts_rank(adv(60), 4), 18), 3)) * -1)
                elif i == 95:  alpha = (_rank((open_ - _ts_min(open_, 12))) < _ts_rank((_rank(_correlation(_sum(((high + low) / 2), 19), _sum(adv(40), 19), 13))**5), 12)).astype(int)
                elif i == 96:  alpha = (np.maximum(_ts_rank(_decay_linear(_correlation(_rank(vwap), _rank(volume), 4), 4), 8), _ts_rank(_decay_linear(_ts_argmax(_correlation(_ts_rank(close, 7), _ts_rank(adv(60), 4), 4), 13), 14), 13)) * -1)
                elif i == 97:  alpha = ((_rank(_decay_linear(_delta(_indneutralize(((low * 0.721001) + (vwap * (1 - 0.721001)))), 3), 20)) - _ts_rank(_decay_linear(_ts_rank(_correlation(_ts_rank(low, 8), _ts_rank(adv(60), 17), 5), 19), 16), 7)) * -1)
                elif i == 98:  alpha = (_rank(_decay_linear(_correlation(vwap, _sum(adv(5), 26), 5), 7)) - _rank(_decay_linear(_ts_rank(_ts_argmin(_correlation(_rank(open_), _rank(adv(15)), 21), 9), 7), 8)))
                elif i == 99:  alpha = ((_rank(_correlation(_sum(((high + low) / 2), 20), _sum(adv(60), 20), 9)) < _rank(_correlation(low, volume, 6))) * -1)
                elif i == 100:
                    term1 = _indneutralize( _rank(((((close - low) - (high - close)) / (high - low).replace(0, 1e-9)) * volume)))
                    term2 = _indneutralize((_correlation(close, _rank(adv(20)), 5) - _rank(_ts_argmin(close, 30))))
                    alpha = (0 - (1 * (((1.5 * _scale(term1)) - _scale(term2)) * (volume / adv(20)))))
                elif i == 101: alpha = ((close - open_) / ((high - low) + 0.001))
                else: continue
                
                df[alpha_name] = alpha
                alpha_list.append(alpha_name)
            except Exception as e:
                print(f"Could not calculate {alpha_name} due to error: {e}")

        # --- Finalize ---
        self.feature_columns.extend(alpha_list)
        df.replace([np.inf, -np.inf], np.nan, inplace=True)
        self.df = df
        print(f"Successfully generated {len(alpha_list)} out of 101 alpha features.")
        return self.df
    
    def _calculate_rsi(self, series, period=14):
        delta = series.diff(1)
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def generate_labels(self, future_periods=20):
        """
        生成标签，即未来N期的收益率。
        :param future_periods: 预测未来多少个周期的收益率。
                               根据您的K线数据频率（如1小时、4小时、1天）来确定这个值。
        """
        self.label_column_name = f'label_fwd_return_{future_periods}'
        self.df[self.label_column_name] = (self.df['close'].shift(-future_periods) / self.df['close']) - 1
        print(f"Generated label: {self.label_column_name}")
        return self.df

    def get_processed_data(self):
        """
        清理数据，去除因滚动窗口或shift产生的NaN值。
        """
        self.df = self.df.dropna()
        # 确保所有特征列都是数值类型
        for col in self.feature_columns:
            self.df[col] = pd.to_numeric(self.df[col], errors='coerce')
        self.df = self.df.dropna() # 再次dropna以防有转换错误
        return self.df

    def split_data_for_lara(self, processed_df, train_start_date, train_end_date,
                            valid_start_date, valid_end_date,
                            test_start_date, test_end_date):
        """
        将数据划分为训练集、验证集、测试集，并构造成LARA.fit所需的格式。
        日期应该是字符串 'YYYY-MM-DD' 或 'YYYY-MM-DD HH:MM:SS'
        """
        datasets_dict = {}
        for periode_name, start_date, end_date in [
            ("train", train_start_date, train_end_date),
            ("valid", valid_start_date, valid_end_date),
            ("test", test_start_date, test_end_date)
        ]:
            # 获取索引的时区
            tz = processed_df.index.tz

            # 转换 start_date 和 end_date 为带时区的时间
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            if start_dt.tzinfo is None:
                start_dt = start_dt.tz_localize(tz)
            else:
                start_dt = start_dt.tz_convert(tz)
            if end_dt.tzinfo is None:
                end_dt = end_dt.tz_localize(tz)
            else:
                end_dt = end_dt.tz_convert(tz)
            subset_df = processed_df[(processed_df.index >= pd.to_datetime(start_dt)) &
                                     (processed_df.index <= pd.to_datetime(end_dt))].copy()

            if subset_df.empty:
                print(f"Warning: {periode_name} dataset is empty for the given date range.")
                features_df = pd.DataFrame(columns=self.feature_columns)
                labels_df = pd.DataFrame(columns=[self.label_column_name, 'instrument', 'datetime'])
            else:
                # 特征 DataFrame
                features_df = subset_df[self.feature_columns].copy()
                features_df['instrument'] = self.instrument_name
                features_df['datetime'] = subset_df.index
                features_df = features_df.set_index(['datetime', 'instrument'])

                # 标签 DataFrame
                labels_df = subset_df[[self.label_column_name]].copy()
                labels_df['instrument'] = self.instrument_name
                labels_df['datetime'] = subset_df.index
                labels_df = labels_df.set_index(['datetime', 'instrument'])

            datasets_dict[periode_name] = {"feature": features_df, "label": labels_df}
            print(f"{periode_name} set: features shape {features_df.shape}, labels shape {labels_df.shape}")

        # LARA.fit 需要一个 prepare 方法返回 [train_dict, valid_dict, test_dict]
        class MockDatasetH:
            def __init__(self, train_d, valid_d, test_d):
                self.train_dict = train_d
                self.valid_dict = valid_d
                self.test_dict = test_d

            def prepare(self, segments, col_set, data_key):
                # segments is ["train", "valid", "test"]
                # col_set is ["feature", "label"]
                # data_key is DataHandlerLP.DK_L (not directly used here, as we pre-construct)
                prepared_data = []
                if "train" in segments:
                    prepared_data.append(self.train_dict)
                if "valid" in segments:
                    prepared_data.append(self.valid_dict)
                if "test" in segments:
                    prepared_data.append(self.test_dict)
                return prepared_data

        return MockDatasetH(datasets_dict["train"], datasets_dict["valid"], datasets_dict["test"])
