import os
import re
from datetime import datetime, timedelta
import pandas as pd
from eth_data_processor import ETHDataProcessor

from rsica_label import resample_1min_to_nmin,label_sup_order_simple,label_sup_order,label_sup_order_simple_1,label_remaining_area,label_ewo,label_cti,label_bollinger_flat_down,label_vwap
from calculate_indicator import calculate_rsica

# ----------------------------
# 1. 配置区域
# ----------------------------
USE_FEATURE_TYPE = "158"   # "158" or "360"
instrument_name = 'ETHUSDT'
current_directory = os.getcwd()
eth_kline_filepath = f'data/ETHUSDT_BINANCE_5m_2025-06-15_23_30_00_2025-06-16_18_05_00.csv'
# pkl 路径
features_pkl_158 = eth_kline_filepath.replace('.csv', '_158_features.pkl')
features_pkl_360 = eth_kline_filepath.replace('.csv', '_360_features.pkl')

# 从文件名提取开始/结束日期并计算 8:1:1 划分
match = re.search(
    r'_(\d{4}-\d{2}-\d{2})_\d{2}_\d{2}_\d{2}_(\d{4}-\d{2}-\d{2})_\d{2}_\d{2}_\d{2}',
    eth_kline_filepath
)
if not match:
    raise ValueError(f"无法从文件名中提取日期: {eth_kline_filepath}")
data_start = datetime.strptime(match.group(1), '%Y-%m-%d')
data_end   = datetime.strptime(match.group(2), '%Y-%m-%d')
total_days = (data_end - data_start).days + 1
train_days = int(total_days * 0.8)
valid_days = int(total_days * 0.0)
test_days  = total_days - train_days - valid_days

train_start = data_start
train_end   = data_start + timedelta(days=train_days - 1)
valid_start = train_end   + timedelta(days=1)
valid_end   = valid_start + timedelta(days=valid_days - 1)
test_start  = valid_end   + timedelta(days=1)
test_end    = data_end

print(f"Train : {train_start} ~ {train_end}")
print(f"Valid : {valid_start} ~ {valid_end}")
print(f"Test  : {test_start} ~ {test_end}")

# ------------------------------
# 2. 特征加载或生成
# ------------------------------
# 先把原始 CSV 读一下（下面只是为了构造 ETHDataProcessor 需要的 ohlcv）
raw_csv = pd.read_csv(eth_kline_filepath)
# 确保 datetime 列
raw_csv['datetime'] = pd.to_datetime(raw_csv['datetime'])
raw_csv = raw_csv.set_index('datetime', drop=False).sort_index()
# -----调整k线周期------------------------------------------------------------------调整k线周期------
#raw_csv= resample_1min_to_nmin(raw_csv,n=5,offset='0min')
# -------------------------------------------------------------------------------------------------
# 构造 ETHDataProcessor 需要的标准 OHLCV DataFrame
ohlcv_df = pd.DataFrame({
    'datetime': raw_csv.index,
    'open':     raw_csv['open'],
    'high':     raw_csv['high'],
    'low':      raw_csv['low'],
    'close':    raw_csv['close'],
    'volume':   raw_csv.get('volume', 0.0),
})
ohlcv_df = ohlcv_df.set_index('datetime')
# 根据 USE_FEATURE_TYPE 决定加载/生成哪一套特征
if USE_FEATURE_TYPE == "158":
    if os.path.exists(features_pkl_158):
        print(f"[INFO] 加载已有158特征文件 {features_pkl_158}")
        df_feat = pd.read_pickle(features_pkl_158)
        processor = ETHDataProcessor(df_feat, instrument_name=instrument_name)
        processor.feature_columns = [c for c in df_feat.columns if c.startswith('feature_')]
    else:
        print("[INFO] 从 CSV 计算 Alpha158 特征")
        processor = ETHDataProcessor(ohlcv_df, instrument_name=instrument_name)
        processor.generate_features_alpha158()
        pd.to_pickle(processor.df, features_pkl_158)
        print(f"[INFO] 158 特征已保存到 {features_pkl_158}")
elif USE_FEATURE_TYPE == "360":
    if os.path.exists(features_pkl_360):
        print(f"[INFO] 加载已有360特征文件 {features_pkl_360}")
        df_feat = pd.read_pickle(features_pkl_360)
        processor = ETHDataProcessor(df_feat, instrument_name=instrument_name)
        processor.feature_columns = [c for c in df_feat.columns if c.startswith('feature_')]
    else:
        print("[INFO] 从 CSV 计算 Alpha360 特征")
        processor = ETHDataProcessor(ohlcv_df, instrument_name=instrument_name)
        processor.generate_features_alpha360()
        pd.to_pickle(processor.df, features_pkl_360)
        print(f"[INFO] 360 特征已保存到 {features_pkl_360}")
else:
    raise ValueError(f"不支持的 USE_FEATURE_TYPE: {USE_FEATURE_TYPE}")

# ------------------------------------------------------------------
# 3. 生成信号标签 + RSI累积面积 + 过滤
# ------------------------------------------------------------------
print("[INFO] 生成信号标签 label_sup_order ...")
label_df = pd.read_csv(f'data/niuerbizhi_label.txt')
# 先将label_df中的action列的'sell'和'buy'映射为-1和1
#label_df['action'] = label_df['action'].map({'sell': -1, 'buy': 1})
# 构建以datetime(HH:MM)为key，action为value的映射
#label_mapping = dict(zip(pd.to_datetime(label_df['datetime']).dt.strftime(f'%Y-%m-%d %H:%M:%S'), label_df['action']))
# 对 raw_csv 的所有行，提取其时间（HH:MM格式），并找到对应的 label
#labels = raw_csv.index.to_series().apply(lambda dt: label_mapping.get(dt.strftime(f'%Y-%m-%d %H:%M:%S'), None))
#labels.name = 'rsica_return'  # 与后续代码中的 label 列名保持一致
# 构建以datetime(HH:MM)为key，action为value的映射
label_mapping = dict(zip(pd.to_datetime(label_df['time']).dt.strftime(f'%H:%M'), label_df['label']))
# 对 raw_csv 的所有行，提取其时间（HH:MM格式），并找到对应的 label
labels = raw_csv.index.to_series().apply(lambda dt: label_mapping.get(dt.strftime(f'%H:%M'), None))
labels.name = 'rsica_return'  # 与后续代码中的 label 列名保持一致

#prediction_horizon = 10
#labels = (raw_csv['close'].shift(-prediction_horizon) / raw_csv['close']) - 1
#labels = label_shape(raw_csv.reset_index(drop=True))
# 因为 raw 的 index 是 datetime，我们要把 labels 和 features 对齐
# 首先把 labels 放到一个按时间索引的 Series
label_ser = pd.Series(labels.values, index=raw_csv.index, name='rsica_return').fillna(0)

# 将 label_ser 中的缺失值进行插值处理，这里采用最近邻插值，保证所有时间点都有值
# step 1: 线性插值得到骨架
#linear = label_ser.interpolate(method='linear', limit_direction='both')
# step 2
#from scipy.signal import savgol_filter,butter, filtfilt
#smooth = pd.Series(savgol_filter(linear, window_length=7, polyorder=3), index=linear.index)
#fs = 1.0  # 采样率，假设你的数据点间隔为1（如秒、采样点等）
#cutoff = 0.25   # 截止频率（归一化到Nyquist频率，即0到0.5之间）
# 设计低通滤波器
#b, a = butter(N=4, Wn=cutoff, btype='low', analog=False)
# 应用滤波器（零相位滤波，防止相位延迟）
#filtered = filtfilt(b, a, smooth)
# 如要与Pandas合用，重建索引
#filtered = pd.Series(filtered, index=label_ser.index,name='rsica_return')
#label_ser=filtered

# 计算 RSI 累积面积
print("[INFO] 计算 RSI_cumulative_area ...")
rsi_ca = calculate_rsica(raw_csv.reset_index(drop=True), rsi_period=14, rsi_threshold=30)
rsi_ca_ser = pd.Series(rsi_ca, index=raw_csv.index, name='RSI_cumulative_area')

# 把标签和 RSI_cum_area 合并到特征表里
df = processor.df.copy()
df['rsica_return'] = label_ser
# -----将反转量改成负值------------------------------------------------------------------------------
#df['rsica_return'] = -label_ser
# -------------------------------------------------------------------------------------------------
df['RSI_cumulative_area'] = rsi_ca_ser

# 只保留那些真正触发补单的行（label 不为空）
df = df.dropna(subset=['rsica_return'])

# 筛选出df['rsica_return']的值最大的前33%
#df = df[df['rsica_return'] >= df['rsica_return'].quantile(0.66)]
# 筛选出df['rsica_return']的值最小的后33%
#df = df[df['rsica_return'] <= df['rsica_return'].quantile(0.33)]


# 设置好用于 LARA 的 label 列名
processor.df = df
processor.feature_columns = [c for c in df.columns if c.startswith('feature_')]
processor.label_column_name = 'rsica_return'

# ----------------------------
# 4. 数据清洗 & 划分
# ----------------------------
print("[INFO] 清洗 NaN 并划分数据集 ...")
processed_df = processor.get_processed_data()



# ----------------------------
# 5. K线图可视化
# ----------------------------
import matplotlib.pyplot as plt
import mplfinance as mpf
from matplotlib.patches import Rectangle
import numpy as np
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False  
print("[INFO] 生成K线图可视化 ...")

# 准备K线数据
plot_df = processed_df.copy()

# 确保有OHLCV数据
if 'open' not in plot_df.columns:
    # 如果processed_df中没有OHLCV，从原始数据中获取
    ohlcv_for_plot = raw_csv[['open', 'high', 'low', 'close', 'volume']].copy()
    # 按索引对齐
    plot_df = plot_df.join(ohlcv_for_plot, how='left')

# 确保索引是datetime类型
if not isinstance(plot_df.index, pd.DatetimeIndex):
    plot_df.index = pd.to_datetime(plot_df.index)

# 准备OHLCV数据用于mplfinance
ohlcv_plot = plot_df[['open', 'high', 'low', 'close', 'volume']].dropna()

# ----------------------------
# 6. 设置可视化参数
# ----------------------------
# 预设条件示例
condition_name = ""  # 条件描述
#condition = plot_df['feature_PRICE_LOW0'] >0.999  # 实际条件
#condition = plot_df['feature_KMID2'] <-0.618  # 实际条件
condition = plot_df['feature_ROC10'] >1
#condition = plot_df['feature_KSFT2'] <-0.5
condition_color = 'yellow'  # 背景色
condition_alpha = 0.3  # 透明度

# 标签条件
positive_labels = plot_df['rsica_return'] > 0.5
negative_labels = plot_df['rsica_return'] < -0.5

# ----------------------------
# 7. 绘制K线图
# ----------------------------
# 创建子图
fig, axes = plt.subplots(2, 1, figsize=(15, 10), gridspec_kw={'height_ratios': [3, 1]})

# 主图 - K线图
ax_main = axes[0]
ax_volume = axes[1]

# 使用mplfinance绘制K线
mc = mpf.make_marketcolors(up='green', down='red', inherit=True)
s = mpf.make_mpf_style(marketcolors=mc, gridstyle=':', y_on_right=False)

# 只取最近500根K线用于可视化（可以根据需要调整窗口大小）
window_size = 500
if len(ohlcv_plot) > window_size:
    ohlcv_plot_view = ohlcv_plot.iloc[-2500-window_size:-2500]
else:
    ohlcv_plot_view = ohlcv_plot

# 绘制基础K线图，只画部分数据
mpf.plot(ohlcv_plot, 
         type='candle',
         style=s,
         ax=ax_main,
         volume=ax_volume,
         show_nontrading=False)

# ----------------------------
# 8. 添加标签标记
# ----------------------------
# 获取价格范围用于标记位置
price_min = ohlcv_plot['low'].min()
price_max = ohlcv_plot['high'].max()
price_range = price_max - price_min

# 标记负标签 (绿色向上箭头)
negative_times = plot_df[negative_labels].index
for time in negative_times:
    if time in ohlcv_plot.index:
        x = ohlcv_plot.index.get_loc(time)  # 用下标定位
        ax_main.annotate('↑', xy=(x, ohlcv_plot.loc[time, 'low']),
                         xytext=(0, -15), textcoords='offset points',
                         ha='center', va='bottom', fontsize=12, color='green', weight='bold')

# 标记正标签 (红色向下箭头)
positive_times = plot_df[positive_labels].index
for time in positive_times:
    if time in ohlcv_plot.index:
        x = ohlcv_plot.index.get_loc(time)
        ax_main.annotate('↓', xy=(x, ohlcv_plot.loc[time, 'high']),
                         xytext=(0, 15), textcoords='offset points',
                         ha='center', va='top', fontsize=12, color='red', weight='bold')

# ----------------------------
# 9. 添加背景色标记满足条件的区域
# ----------------------------
condition_times = plot_df[condition].index
if len(condition_times) > 0:
    # 计算时间间隔（用于设置矩形宽度）
    time_diff = pd.Timedelta(minutes=5)  # 假设是5分钟K线，根据实际情况调整
    
    for time in condition_times:
        if time in ohlcv_plot.index:
            x = ohlcv_plot.index.get_loc(time)
            rect = Rectangle((x-0.5, price_min), 1, price_range,
                            facecolor=condition_color, alpha=condition_alpha, zorder=0)
            ax_main.add_patch(rect)

# ----------------------------
# 10. 图表美化和信息添加
# ----------------------------
# 设置标题
ax_main.set_title(f'{instrument_name} K线图 - 特征: {USE_FEATURE_TYPE}\n'
                 f'数据时间: {data_start.strftime("%Y-%m-%d")} ~ {data_end.strftime("%Y-%m-%d")}', 
                 fontsize=14, fontweight='bold')

# 添加图例
from matplotlib.lines import Line2D
legend_elements = [
    Line2D([0], [0], marker='^', color='w', markerfacecolor='green', markersize=10, label='正标签'),
    Line2D([0], [0], marker='v', color='w', markerfacecolor='red', markersize=10, label='负标签'),
    Rectangle((0,0),1,1, facecolor=condition_color, alpha=condition_alpha, label=f'条件: {condition_name}')
]
ax_main.legend(handles=legend_elements, loc='upper left')

# 添加统计信息
stats_text = f"""数据统计:
总K线数: {len(ohlcv_plot)}
正标签数: {sum(positive_labels)}
负标签数: {sum(negative_labels)}
满足条件数: {sum(condition)}
条件满足率: {sum(condition)/len(plot_df)*100:.1f}%"""

ax_main.text(0.02, 0.85, stats_text, transform=ax_main.transAxes, 
            verticalalignment='top', fontsize=9, 
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

# 设置x轴标签格式
ax_main.tick_params(axis='x', rotation=45)
ax_volume.tick_params(axis='x', rotation=45)

# 调整布局
plt.tight_layout()

# 保存图片
#output_image = eth_kline_filepath.replace('.csv', f'_{USE_FEATURE_TYPE}_kline_analysis.png')
#plt.savefig(output_image, dpi=300, bbox_inches='tight')
#print(f"[INFO] K线图已保存到: {output_image}")

# 显示图片
plt.show()

# ----------------------------
# 11. 生成详细分析报告
# ----------------------------
print("\n" + "="*50)
print("详细分析报告")
print("="*50)

# 标签分布统计
print(f"\n标签分布:")
print(f"正标签 (rsica_return > 0): {sum(positive_labels)} 个 ({sum(positive_labels)/len(plot_df)*100:.1f}%)")
print(f"负标签 (rsica_return < 0): {sum(negative_labels)} 个 ({sum(negative_labels)/len(plot_df)*100:.1f}%)")
print(f"零标签 (rsica_return = 0): {sum(plot_df['rsica_return'] == 0)} 个")

# 条件分析
print(f"\n条件分析 ({condition_name}):")
print(f"满足条件的K线数: {sum(condition)} 个 ({sum(condition)/len(plot_df)*100:.1f}%)")

# 条件与标签的关联分析
condition_positive = sum(condition & positive_labels)
condition_negative = sum(condition & negative_labels)
print(f"满足条件且为正标签: {condition_positive} 个")
print(f"满足条件且为负标签: {condition_negative} 个")

if sum(condition) > 0:
    print(f"满足条件时正标签比例: {condition_positive/sum(condition)*100:.1f}%")
    print(f"满足条件时负标签比例: {condition_negative/sum(condition)*100:.1f}%")

print("\n" + "="*50)
