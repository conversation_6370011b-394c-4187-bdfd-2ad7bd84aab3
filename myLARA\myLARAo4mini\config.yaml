# config.yaml
data:
  path: "data/ETH_kline.csv"
  train_period: ["2017-01-01", "2019-12-31"]
  valid_period: ["2020-01-01", "2020-06-30"]
  test_period:  ["2020-07-01", "2021-12-31"]
features:
  lookback: 10          # 用过去 10 根 K 线计算简单指标
metric_learning:
  method: "ITML_Supervised"
  num_constraints: 500
  sparsity_param: 0.1
  balance_param: 0.001
  use_multi_label: true
  shuffle: true
knn:
  mode: "rnn"            # knn or rnn
  k: 50
  radius: 100
dual:
  enable: true
  rounds: 5
  ratio: 0.02
lgb:
  params:
    objective: binary
    boosting_type: gbdt
    metric: auc
    verbose: -1
  num_boost_round: 100
evaluation:
  topk: 200
  threshold: 0.0
