import numpy as np
import matplotlib.pyplot as plt
plt.rcParams['font.family'] = ['SimHei']  # 或 ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号'-'显示为方块的问题

# 参数设置
total_steps = 10000  # 总步数（时间长度）
start_value = 100  # 初始y值（比如初始资金）
win_rate = 0.5714   # 胜率
rr = 0.8         # 盈亏比（上涨幅度/下跌幅度）

# 盈亏幅度设置
loss_value = 1    # 每次下跌的绝对值
win_value = rr * loss_value  # 每次上涨的绝对值

# 生成曲线
y = [start_value]
x = [0]
current_step = 0

while current_step < total_steps:
    # 随机间隔
    interval = np.random.randint(1, 100)
    current_step += interval
    if current_step > total_steps:
        current_step = total_steps

    # 胜负判定
    if np.random.rand() < win_rate:
        change = win_value
    else:
        change = -loss_value

    y.append(y[-1] + change)
    x.append(current_step)

# 补齐最后的点
if x[-1] < total_steps:
    x.append(total_steps)
    y.append(y[-1])

# 绘图
plt.figure(figsize=(12, 6))
plt.plot(x, y, marker='o')
plt.title(f"资金曲线（胜率{win_rate*100:.0f}%，盈亏比{rr}）")
plt.xlabel("时间")
plt.ylabel("资金")
plt.grid(True)
plt.show()