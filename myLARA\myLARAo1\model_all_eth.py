"""
示例文件：model_all_eth.py
此文件对应原逻辑中对LARA方法做进一步封装（如度量学习、KNN筛选、最终预测）等流程的函数。
与CSI300版相比，仅在数据的注释以及路径方面做了相应的修改，代码主逻辑和原版相同。
"""

import copy
import numpy as np
import pandas as pd
import lightgbm as lgb
import metric_learn
from sklearn.preprocessing import StandardScaler, MinMaxScaler, MaxAbsScaler, RobustScaler, PowerTransformer, QuantileTransformer, Normalizer
import hnswlib
from scipy.sparse import csc_matrix
import os

def metric_learning(self, train_dataset, test_dataset, ping_dataset, train_label_metric_learning):
    """
    用于度量学习(根据 self.metric_method 的不同选择不同的算法)，
    把原特征映射到新的空间，提高后续的邻近检索效果。
    """
    x_train, y_train = train_dataset
    x_test, y_test = test_dataset
    x_ping, y_ping = ping_dataset

    # 这里与原LARA的思路保持一致，根据use_multi_label决定使用什么样的数据进行度量学习
    if self.use_multi_label:
        # 整合 x_train 与 x_ping
        x_train_all = pd.concat([x_train, x_ping], axis=0)
        y_train_all = pd.concat([y_train, y_ping], axis=0)
        x_train_all.index = np.arange(x_train_all.shape[0])
        y_train_all.index = np.arange(y_train_all.shape[0])
    else:
        x_train_all = x_train.copy()
        y_train_all = y_train.copy()

    # 对训练集进行适合度量学习的处理(如去除异常、选择合适label等等)
    x_train_all, y_train_all = perform_nan_inf(x_train_all, y_train_all)
    # 根据指定的预处理方法来处理特征
    x_train_all, preprocessor = data_preprocess(x_train_all, self.preprocessing, model=None)
    x_train_all, y_train_all = perform_nan_inf(x_train_all, y_train_all)

    # 同理对测试集做相应处理
    x_test_new, y_test_new = perform_nan_inf(x_test, y_test)
    x_test_new = data_preprocess(x_test_new, self.preprocessing, preprocessor)
    x_test_new, y_test_new = perform_nan_inf(x_test_new, y_test_new)

    x_ping_new, y_ping_new = perform_nan_inf(x_ping, y_ping)
    x_ping_new = data_preprocess(x_ping_new, self.preprocessing, preprocessor)
    x_ping_new, y_ping_new = perform_nan_inf(x_ping_new, y_ping_new)

    # 根据 self.metric_method 来确定要使用的度量学习算法
    # 例如：ITML、SDML、LSML、NCA、LMNN、LFDA、MLKR等
    model = None
    if self.metric_method == 'ITML_Supervised':
        model = metric_learn.ITML_Supervised(prior='covariance', num_constraints=1000)
    elif self.metric_method == 'SDML_Supervised':
        model = metric_learn.SDML_Supervised(sparsity_param=0.1, balance_param=0.001, num_constraints=1000)
    elif self.metric_method == 'LSML_Supervised':
        model = metric_learn.LSML_Supervised(prior='covariance', num_constraints=1000)
    elif self.metric_method == 'LMNN':
        model = metric_learn.LMNN(k=5)
    elif self.metric_method == 'MMC_Supervised':
        model = metric_learn.MMC_Supervised()
    elif self.metric_method == 'NCA':
        model = metric_learn.NCA(max_iter=1000)
    elif self.metric_method == 'LFDA':
        model = metric_learn.LFDA(k=2, n_components=2)
    elif self.metric_method == 'RCA_Supervised':
        model = metric_learn.RCA_Supervised(num_chunks=30, chunk_size=2)
    elif self.metric_method == 'MLKR':
        model = metric_learn.MLKR()
    else:
        raise RuntimeError(f"Unknown metric method: {self.metric_method}")

    # 训练度量模型
    if self.metric_method in ['MLKR']:
        # MLKR是用于回归场景，需要 y_train_all[self.out] 作为目标
        model.fit(x_train_all.values, y_train_all[self.out].values)
    else:
        # 其它多数是分类方式，需要 y_train_all['label'] 作为标签
        # 如果遇到奇异矩阵报错时可做异常处理
        model.fit(x_train_all.values, y_train_all['label'].values)

    # 将原始 x_train, x_test, x_ping 通过metric映射到新的空间
    x_train_new = data_preprocess(x_train.copy(), self.preprocessing, preprocessor)
    x_test_new2 = data_preprocess(x_test.copy(), self.preprocessing, preprocessor)
    x_ping_new2 = data_preprocess(x_ping.copy(), self.preprocessing, preprocessor)

    x_train_transform = pd.DataFrame(model.transform(x_train_new.values), 
                                     columns=x_train_new.columns, index=x_train_new.index)
    x_test_transform = pd.DataFrame(model.transform(x_test_new2.values), 
                                    columns=x_test_new2.columns, index=x_test_new2.index)
    x_ping_transform = pd.DataFrame(model.transform(x_ping_new2.values), 
                                    columns=x_ping_new2.columns, index=x_ping_new2.index)

    return (x_train_transform, y_train, 
            x_test_transform, y_test, 
            x_ping_transform, y_ping, 
            model)


def KNN(x_train_transform, y_train_transform, self_model, params, x_test_transform, y_test_transform, path, train_test='train'):
    """
    KNN或RNN邻居筛选过程
    """
    import hnswlib

    x_train_transform.index = np.arange(x_train_transform.shape[0])
    y_train_transform.index = np.arange(y_train_transform.shape[0])
    x_test_transform.index = np.arange(x_test_transform.shape[0])
    y_test_transform.index = np.arange(y_test_transform.shape[0])

    def fit_hnsw_index(features, ef=100, M=16, save_index_file=False):
        """
        用 hnswlib 来构建最近邻搜索图
        """
        num_elements = len(features)
        labels_index = np.arange(num_elements)
        EMBEDDING_SIZE = len(features[0])
        p = hnswlib.Index(space='l2', dim=EMBEDDING_SIZE)
        p.init_index(max_elements=num_elements, random_seed=100, ef_construction=ef, M=M)
        _ = p.add_items(features, labels_index)
        p.set_ef(ef)
        p.set_num_threads(10)
        if save_index_file:
            p.save_index(path)
        return p

    # 如果是训练阶段，则fit生成索引；否则直接load索引
    if train_test == 'train':
        model_hnsw = fit_hnsw_index(x_train_transform.values.tolist(), ef=self_model.points*10)
    else:
        model_hnsw = hnswlib.Index(space='l2', dim=x_train_transform.shape[1])
        model_hnsw.load_index(path, max_elements=x_train_transform.shape[0])
        model_hnsw.set_ef(self_model.points*10)
        model_hnsw.set_num_threads(10)
    
    # 对测试集做knn或rnn
    ann_neighbor_indices, ann_distances = model_hnsw.knn_query(x_test_transform.values.tolist(), self_model.points)
    if self_model.knn_rnn == 'knn':
        data_matrix = np.ones(ann_neighbor_indices.size, dtype=np.float32)
    else:
        # rnn: 用距离的倒数(或其它方式)来赋权
        ann_distances[ann_distances > self_model.radius] = 0
        ann_distances = 1 / ann_distances
        ann_distances[np.isinf(ann_distances)] = 0
        data_matrix = ann_distances.reshape(-1)

    row_idx = np.repeat(np.arange(x_test_transform.shape[0]), self_model.points)
    col_idx = ann_neighbor_indices.reshape(-1)
    arr_test = csc_matrix((data_matrix, (row_idx, col_idx)), shape=(x_test_transform.shape[0], x_train_transform.shape[0]))

    # 将y_train_transform映射为+1/-1，再与arr_test点乘，筛选结果
    label_sign = y_train_transform['label'].copy().replace({0:-1, 1:1}).values
    label_sign = csc_matrix(label_sign.reshape(-1,1))
    screened_score = arr_test.dot(label_sign).toarray().reshape(-1)

    keep_mask_test = (screened_score >= 0)
    x_test_transform = x_test_transform.loc[keep_mask_test].copy()
    y_test_transform = y_test_transform.loc[keep_mask_test].copy()
    x_test_transform.index = np.arange(x_test_transform.shape[0])
    y_test_transform.index = np.arange(y_test_transform.shape[0])

    if train_test == 'test':
        return x_train_transform, y_train_transform, x_test_transform, y_test_transform

    # 再对训练集本身做一次knn/rnn
    ann_neighbor_indices_train, ann_distances_train = model_hnsw.knn_query(x_train_transform.values.tolist(), self_model.points)
    if self_model.knn_rnn == 'knn':
        data_matrix_train = np.ones(ann_neighbor_indices_train.size, dtype=np.float32)
    else:
        ann_distances_train[ann_distances_train > self_model.radius] = 0
        ann_distances_train = 1 / ann_distances_train
        ann_distances_train[np.isinf(ann_distances_train)] = 0
        data_matrix_train = ann_distances_train.reshape(-1)

    row_idx_train = np.repeat(np.arange(x_train_transform.shape[0]), self_model.points)
    col_idx_train = ann_neighbor_indices_train.reshape(-1)
    arr_train = csc_matrix((data_matrix_train, (row_idx_train, col_idx_train)), shape=(x_train_transform.shape[0], x_train_transform.shape[0]))

    label_sign_train = y_train_transform['label'].copy().replace({0:-1, 1:1}).values
    label_sign_train = csc_matrix(label_sign_train.reshape(-1,1))
    screened_score_train = arr_train.dot(label_sign_train).toarray().reshape(-1)

    keep_mask_train = (screened_score_train >= 0)
    x_train_transform = x_train_transform.loc[keep_mask_train].copy()
    y_train_transform = y_train_transform.loc[keep_mask_train].copy()
    x_train_transform.index = np.arange(x_train_transform.shape[0])
    y_train_transform.index = np.arange(y_train_transform.shape[0])

    return x_train_transform, y_train_transform, x_test_transform, y_test_transform


def perform_nan_inf(x, y):
    x = x.replace([np.inf, -np.inf], np.nan)
    x.index = np.arange(x.shape[0])
    y.index = np.arange(y.shape[0])
    x_new = x.dropna(axis=0)
    y_new = y.loc[x_new.index]
    x_new.index = np.arange(x_new.shape[0])
    y_new.index = np.arange(y_new.shape[0])
    return x_new, y_new

def data_preprocess(df, method, model=None):
    """
    针对特征数据进行指定缩放或变换
    """
    if method == 'StandardScaler':
        if model is None:
            processor = StandardScaler().fit(df)
            res = pd.DataFrame(processor.transform(df), columns=df.columns)
            return res, processor
        else:
            res = pd.DataFrame(model.transform(df), columns=df.columns)
            return res
    elif method == 'MinMaxScaler':
        if model is None:
            processor = MinMaxScaler().fit(df)
            res = pd.DataFrame(processor.transform(df), columns=df.columns)
            return res, processor
        else:
            res = pd.DataFrame(model.transform(df), columns=df.columns)
            return res
    elif method == 'MaxAbsScaler':
        if model is None:
            processor = MaxAbsScaler().fit(df)
            res = pd.DataFrame(processor.transform(df), columns=df.columns)
            return res, processor
        else:
            res = pd.DataFrame(model.transform(df), columns=df.columns)
            return res
    elif method == 'RobustScaler':
        if model is None:
            processor = RobustScaler(quantile_range=(25, 75)).fit(df)
            res = pd.DataFrame(processor.transform(df), columns=df.columns)
            return res, processor
        else:
            res = pd.DataFrame(model.transform(df), columns=df.columns)
            return res
    elif method == 'PowerTransformer':
        if model is None:
            processor = PowerTransformer(method='yeo-johnson').fit(df)
            res = pd.DataFrame(processor.transform(df), columns=df.columns)
            return res, processor
        else:
            res = pd.DataFrame(model.transform(df), columns=df.columns)
            return res
    elif method == 'QuantileTransformer_normal':
        if model is None:
            processor = QuantileTransformer(output_distribution='normal').fit(df)
            res = pd.DataFrame(processor.transform(df), columns=df.columns)
            return res, processor
        else:
            res = pd.DataFrame(model.transform(df), columns=df.columns)
            return res
    elif method == 'QuantileTransformer_uniform':
        if model is None:
            processor = QuantileTransformer(output_distribution='uniform').fit(df)
            res = pd.DataFrame(processor.transform(df), columns=df.columns)
            return res, processor
        else:
            res = pd.DataFrame(model.transform(df), columns=df.columns)
            return res
    elif method == 'Normalizer':
        if model is None:
            processor = Normalizer().fit(df)
            res = pd.DataFrame(processor.transform(df), columns=df.columns)
            return res, processor
        else:
            res = pd.DataFrame(model.transform(df), columns=df.columns)
            return res
    elif method == 'None':
        if model is None:
            return df, None
        else:
            return df
    else:
        raise RuntimeError(f"Unknown data preprocessing method: {method}")
