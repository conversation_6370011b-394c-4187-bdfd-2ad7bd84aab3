import os
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from qlib.data.dataset.handler import DataHandlerLP
from qlib.data.dataset import DatasetH
from qlib.log import get_module_logger
import metric_learn
import hnswlib
from scipy.sparse import csc_matrix
import lightgbm as lgb
import pickle
import argparse

parser = argparse.ArgumentParser(description='Hyper-parameters for LARA on ETH')
parser.add_argument('--train', action='store_true', help='Whether to train or test')
parser.add_argument('--dataset', type=str, default='ETH_Kline', help='Dataset name for ETH data')
parser.add_argument('--use_metric', type=str, default='True', help='Whether to use metric learning')
parser.add_argument('--ping', type=str, default='True', help='Whether to use ping sampling')
parser.add_argument('--knn_rnn', type=str, default='knn', help='KNN or RNN sampling method')
parser.add_argument('--points', type=int, default=100, help='Number of nearest neighbors for KNN/RNN')
parser.add_argument('--radius', type=float, default=100.0, help='Radius for RNN sampling')
parser.add_argument('--use_dual', type=bool, default=True, help='Whether to use dual-label strategy')
parser.add_argument('--dual_numbers', type=int, default=5, help='Number of iterations for dual-label')
parser.add_argument('--dual_ratio', type=float, default=0.01, help='Ratio for dual-label adjustment')
args = parser.parse_args()

# 初始化日志
logger = get_module_logger("LARA_ETH")

class ETHDataProcessor:
    def __init__(self, data_path="data/eth_klines.csv", threshold=0.001):
        self.data_path = data_path
        self.threshold = threshold
        self.scaler = StandardScaler()

    def load_data(self):
        """加载ETH K线数据"""
        df = pd.read_csv(self.data_path)
        df['datetime'] = pd.to_datetime(df['timestamp'])
        df.set_index(['datetime'], inplace=True)
        return df

    def generate_features_and_labels(self, df):
        """生成特征和目标标签（未来收益率）"""
        # 提取基础特征：OHLCV
        features = df[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # 计算目标标签：未来1周期收益率
        df['future_return'] = df['close'].shift(-1) / df['close'] - 1
        labels = df['future_return'].to_frame(name='label_raw')
        
        # 标签二分类：涨跌
        labels['label'] = 0
        labels.loc[labels['label_raw'] >= self.threshold, 'label'] = 1
        
        # 多标签用于度量学习（分层）
        labels['metric_label'] = 0
        labels.loc[labels['label_raw'] >= 0.001, 'metric_label'] = 5
        labels.loc[(labels['label_raw'] < 0.001) & (labels['label_raw'] >= 0.0005), 'metric_label'] = 4
        labels.loc[(labels['label_raw'] < 0.0005) & (labels['label_raw'] >= 0), 'metric_label'] = 3
        labels.loc[(labels['label_raw'] < 0) & (labels['label_raw'] >= -0.0005), 'metric_label'] = 2
        labels.loc[(labels['label_raw'] < -0.0005) & (labels['label_raw'] >= -0.001), 'metric_label'] = 1
        
        return features, labels

    def split_data(self, features, labels, train_ratio=0.7):
        """分割训练和测试数据"""
        train_size = int(len(features) * train_ratio)
        train_features, test_features = features.iloc[:train_size], features.iloc[train_size:]
        train_labels, test_labels = labels.iloc[:train_size], labels.iloc[train_size:]
        return train_features, train_labels, test_features, test_labels

    def normalize_features(self, train_features, test_features):
        """标准化特征"""
        train_features_scaled = pd.DataFrame(self.scaler.fit_transform(train_features), 
                                             columns=train_features.columns, 
                                             index=train_features.index)
        test_features_scaled = pd.DataFrame(self.scaler.transform(test_features), 
                                            columns=test_features.columns, 
                                            index=test_features.index)
        return train_features_scaled, test_features_scaled
    
class LARA:
    def __init__(self, args):
        self.logger = get_module_logger("LARA_ETH")
        self.use_metric = args.use_metric == 'True'
        self.ping = args.ping == 'True'
        self.knn_rnn = args.knn_rnn
        self.points = args.points
        self.radius = args.radius
        self.use_dual = args.use_dual
        self.dual_numbers = args.dual_numbers
        self.dual_ratio = args.dual_ratio
        
        # LightGBM参数
        self.params = {
            'objective': 'binary',
            'boosting_type': 'gbdt',
            'metric': {'binary_logloss', 'auc'},
            'silent': 1,
            'random_state': 0
        }
        self.metric_model = None
        self.model = None
        self.predicted = None

    def metric_learning(self, train_features, train_labels, test_features, test_labels, metric_labels):
        """度量学习：调整特征空间距离度量"""
        if not self.use_metric:
            return train_features, train_labels, test_features, test_labels
        
        self.logger.info("Starting Metric Learning with ITML_Supervised")
        try:
            self.metric_model = metric_learn.ITML_Supervised(num_constraints=1000)
            self.metric_model.fit(train_features, metric_labels['metric_label'])
            train_transformed = pd.DataFrame(self.metric_model.transform(train_features), 
                                             columns=train_features.columns, 
                                             index=train_features.index)
            test_transformed = pd.DataFrame(self.metric_model.transform(test_features), 
                                            columns=test_features.columns, 
                                            index=test_features.index)
            return train_transformed, train_labels, test_transformed, test_labels
        except Exception as e:
            self.logger.error(f"Metric Learning Failed: {e}")
            return train_features, train_labels, test_features, test_labels

    def knn_rnn_sampling(self, train_features, train_labels, test_features, test_labels, save_path='ann_graph.bin'):
        """KNN/RNN采样：筛选高质量样本"""
        if self.knn_rnn == 'None':
            return train_features, train_labels, test_features, test_labels
        
        self.logger.info("Starting KNN/RNN Sampling")
        def fit_hnsw_index(features, ef=100, M=16):
            num_elements = len(features)
            p = hnswlib.Index(space='l2', dim=features.shape[1])
            p.init_index(max_elements=num_elements, random_seed=100, ef_construction=ef, M=M)
            p.add_items(features.values, np.arange(num_elements))
            p.set_ef(ef)
            p.set_num_threads(20)
            return p
        
        model = fit_hnsw_index(train_features, ef=self.points * 10)
        model.save_index(save_path)
        
        # 对测试集进行KNN/RNN筛选
        ann_indices, ann_distances = model.knn_query(test_features.values, self.points)
        if self.knn_rnn == 'knn':
            row = np.tile(np.arange(test_features.shape[0])[:, np.newaxis], (1, self.points)).reshape(-1)
            col = ann_indices.reshape(-1)
            data = np.ones(col.shape[0])
            arr = csc_matrix((data, (row, col)), shape=(test_features.shape[0], train_features.shape[0]))
        else:  # rnn
            row = np.tile(np.arange(test_features.shape[0])[:, np.newaxis], (1, self.points)).reshape(-1)
            col = ann_indices.reshape(-1)
            ann_distances[ann_distances > self.radius] = 0
            ann_distances = 1 / ann_distances
            ann_distances[np.isinf(ann_distances)] = 0
            data = ann_distances.reshape(-1)
            arr = csc_matrix((data, (row, col)), shape=(test_features.shape[0], train_features.shape[0]))
        
        # 基于标签筛选
        label_arr = csc_matrix(train_labels['label'].values, shape=(train_features.shape[0], 1))
        label_arr[label_arr < 1] = -1
        arr = csc_matrix.dot(arr, label_arr).toarray().reshape(-1)
        test_selected = arr >= 0
        test_features = test_features.iloc[test_selected]
        test_labels = test_labels.iloc[test_selected]
        
        # 对训练集进行类似筛选
        ann_indices, ann_distances = model.knn_query(train_features.values, self.points)
        if self.knn_rnn == 'knn':
            row = np.tile(np.arange(train_features.shape[0])[:, np.newaxis], (1, self.points)).reshape(-1)
            col = ann_indices.reshape(-1)
            data = np.ones(col.shape[0])
            arr = csc_matrix((data, (row, col)), shape=(train_features.shape[0], train_features.shape[0]))
        else:
            row = np.tile(np.arange(train_features.shape[0])[:, np.newaxis], (1, self.points)).reshape(-1)
            col = ann_indices.reshape(-1)
            ann_distances[ann_distances > self.radius] = 0
            ann_distances = 1 / ann_distances
            ann_distances[np.isinf(ann_distances)] = 0
            data = ann_distances.reshape(-1)
            arr = csc_matrix((data, (row, col)), shape=(train_features.shape[0], train_features.shape[0]))
        
        label_arr = csc_matrix(train_labels['label'].values, shape=(train_features.shape[0], 1))
        label_arr[label_arr < 1] = -1
        arr = csc_matrix.dot(arr, label_arr).toarray().reshape(-1)
        train_selected = arr >= 0
        train_features = train_features.iloc[train_selected]
        train_labels = train_labels.iloc[train_selected]
        
        return train_features, train_labels, test_features, test_labels

    def dual_label_strategy(self, train_features, train_labels, test_features, test_labels, model):
        """双重标签策略：迭代更新标签分布"""
        if not self.use_dual:
            predictions = model.predict(test_features)
            return pd.DataFrame(predictions, columns=['signal'], index=test_features.index)
        
        self.logger.info("Starting Dual-Label Strategy")
        gbm_list = []
        changes = []
        train_labels_copy = train_labels.copy()
        
        for _ in range(self.dual_numbers):
            predictions = model.predict(train_features)
            train_labels_copy['proba'] = predictions
            train_labels_copy['label1'] = 0
            train_labels_copy['label2'] = 0
            
            temp = train_labels_copy['proba'].copy()
            num1 = int((1 - self.dual_ratio) * temp.shape[0])
            num2 = int(self.dual_ratio * temp.shape[0])
            idx1 = temp.sort_values(ascending=False).iloc[:num1].index
            idx2 = temp.sort_values(ascending=False).iloc[:num2].index
            
            train_labels_copy.loc[idx1, 'label1'] = 1
            train_labels_copy.loc[idx2, 'label2'] = 1
            new_label = (train_labels_copy['label'] & train_labels_copy['label1']) |\
                ((1 - train_labels_copy['label']) & train_labels_copy['label2'])
            change_number = sum(train_labels_copy['label'] ^ new_label)
            changes.append(change_number)
            
            train_labels_copy['label'] = new_label
            gbm_list.append(model)
            lgb_train = lgb.Dataset(train_features, train_labels_copy['label'])
            model = lgb.train(self.params, lgb_train)
        
        # 集成所有模型的预测结果
        predicted_df = pd.DataFrame([])
        for i, gbm_instance in enumerate(gbm_list):
            temp = gbm_instance.predict(test_features)
            predicted_df = pd.concat([predicted_df, pd.DataFrame(temp, columns=[f'model{i}'])], axis=1)
        predicted_df['signal'] = predicted_df.mean(axis=1)
        return predicted_df

    def fit(self, train_features, train_labels, test_features, test_labels, metric_labels):
        """训练LARA模型"""
        # 度量学习
        train_features, train_labels, test_features, test_labels = self.metric_learning(
            train_features, train_labels, test_features, test_labels, metric_labels)
        self.logger.info("Metric Learning Completed")
        
        # KNN/RNN采样
        train_features, train_labels, test_features, test_labels = self.knn_rnn_sampling(
            train_features, train_labels, test_features, test_labels)
        self.logger.info("KNN/RNN Sampling Completed")
        
        # 训练LightGBM模型
        lgb_train = lgb.Dataset(train_features, train_labels['label'])
        self.model = lgb.train(self.params, lgb_train)
        self.logger.info("LightGBM Training Completed")
        
        # 双重标签策略
        self.predicted = self.dual_label_strategy(train_features, train_labels, test_features, test_labels, self.model)
        self.logger.info("Dual-Label Strategy Completed")

    def predict(self, test_features):
        """预测"""
        return self.predicted['signal']
    
def train(dataset=args.dataset):
    base_path = os.path.join('experiments', dataset)
    os.makedirs(base_path, exist_ok=True)
    os.chdir(base_path)
    
    # 加载数据
    processor = ETHDataProcessor()
    df = processor.load_data()
    features, labels = processor.generate_features_and_labels(df)
    train_features, train_labels, test_features, test_labels = processor.split_data(features, labels)
    train_features, test_features = processor.normalize_features(train_features, test_features)
    
    # 初始化并训练模型
    model = LARA(args)
    model.fit(train_features, train_labels, test_features, test_labels, train_labels)
    
    # 保存模型和预测结果
    with open(os.path.join(base_path, 'model.pkl'), 'wb') as f:
        pickle.dump(model, f)
    model.predicted.to_pickle(os.path.join(base_path, 'pred.pkl'))
    test_labels.to_pickle(os.path.join(base_path, 'label.pkl'))
    logger.info("Training Completed and Results Saved")

def test(dataset=args.dataset, thresh=0.001, count=1000):
    base_path = os.path.join('experiments', dataset)
    os.chdir(base_path)
    
    # 加载预测结果和真实标签
    pred = pd.read_pickle(os.path.join(base_path, 'pred.pkl'))
    label = pd.read_pickle(os.path.join(base_path, 'label.pkl'))
    
    # 计算评估指标
    prec, wlr, ar = test_index(pred, label, up_or_down='up', thresh=thresh, count=count)
    res = pd.DataFrame([[dataset, thresh, count, prec, wlr, ar]], 
                       columns=['dataset', 'thresh', 'count', 'precision', 'win-loss ratio', 'average return'])
    print(res)
    res.to_csv(os.path.join(base_path, 'results.csv'), index=False)

def test_index(pred, label, up_or_down, thresh, count):
    """计算评估指标：精度、胜负比、平均收益率"""
    df = pd.concat([pred, label['label_raw']], axis=1, join='inner')
    name1, name2 = df.columns
    
    assert up_or_down == 'up', 'up_or_down must be "up" for ETH data!'
    
    df['label'] = 0
    df = df.sort_values(by=[name1], ascending=False).iloc[:count]
    df.loc[df[name2] >= thresh, 'label'] = 1
    prec = df['label'].sum() / count
    
    win = df.loc[df[name2] > 0, name2].mean()
    loss = df.loc[df[name2] < 0, name2].mean()
    wlr = abs(win / loss) if loss != 0 else 0
    
    ar = df[name2].sum() / count
    return prec, wlr, ar

if __name__ == '__main__':
    if args.train:
        train(args.dataset)
    else:
        test(args.dataset)
