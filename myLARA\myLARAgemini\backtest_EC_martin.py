import pandas as pd
import numpy as np
from datetime import datetime
from calculate_indicator import calculate_ema,calculate_macd,calculate_adx,calculate_RSI,calculate_atr,calculate_bollinger_bands,calculate_stochf,calculate_rsica
from tqdm import tqdm

def backtest_EC_strategy(df: pd.DataFrame, look_forward_bars: int = 10, 
                             win_profit: float = 4, loss_cost: float = 5, initial_balance: float = 1000,
                             max_martin_orders:int = 3,martin_multiplier:float=2.0):
    """
    回测RSI积累面积策略
    
    参数:
    df: 包含RSI_cumulative_area和close_price的DataFrame
    area_threshold: RSI积累面积的阈值
    look_forward_bars: 向前看多少根K线
    win_profit: 盈利金额
    loss_cost: 亏损金额
    initial_balance: 初始余额
    martin_multiplier: 马丁格尔倍数，从第二单开始每次盈亏是上次的倍数
    """
    
    # 初始化结果数据
    results = []
    current_balance = initial_balance
    trades = []

    #各个母单之间的最小间隔（分钟）
    order_interval =30
    wait_duration =30
    
    # 用于跟踪补单信息
    active_trades = {}  # 记录活跃的交易及其补单次数
    
    #预留k线数量
    kline_size_before=60
    # 添加进度条
    total_bars = len(df) - look_forward_bars - kline_size_before
    # 遍历数据
    for i in tqdm(range(kline_size_before, len(df) - look_forward_bars), total=total_bars,
                  desc="回测进度", ncols=100):
        row = df.iloc[i]
        # 转换时间字符串为datetime对象
        time_format = "%Y-%m-%d %H:%M:%S"
        #如果row.name是字符串
        if isinstance(row.name, str):
            current_time = datetime.strptime(row.name, time_format)
        else:
            current_time = row.datetime
        
        # 清理过期的交易记录
        active_trades = {t: info for t, info in active_trades.items() 
                        if (current_time - t).total_seconds() <= order_interval * 60}
        
        
        if should_open(df,i)== True and len(active_trades) == 0:
            # 首次开仓
            entry_price = row['close_price']
            future_price = df.iloc[i + look_forward_bars]['close_price']
            
            ST_distance = ((row['high_price']+row['low_price'])/2 +3*row['ATR'])-row['close_price']
            # 创建新的交易记录
            active_trades[current_time] = {
                'follow_orders': 0, 
                'base_price': entry_price, 
                'last_order_price': entry_price,
                'last_martin_distance': ST_distance,
                'current_win_profit': win_profit,
                'current_loss_cost': loss_cost
            }
            trade_info = {
                'datetime': current_time,
                'entry_price': entry_price,
                'future_price': future_price,
                'st_distance': ST_distance,
                'is_win': future_price > entry_price,
                'balance': current_balance,
                'win_profit': win_profit,
                'loss_cost': loss_cost,
                'trade_type': 'initial'
            }
            
            if trade_info['is_win']:
                current_balance += win_profit
            else:
                current_balance -= loss_cost
            trade_info['balance'] = current_balance
                
            trades.append(trade_info)

        elif len(active_trades) > 0:
            for trade_time, active_trade_info in active_trades.items():
                if (
                    (current_time - trade_time).total_seconds() <= wait_duration * 60
                    and should_reopen(df,i,active_trade_info,max_martin_orders)
                ):
                    entry_price = row['close_price']
                    active_trade_info['last_order_price'] = entry_price
                    ST_distance = ((row['high_price']+row['low_price'])/2 +3*row['ATR'])-row['close_price']
                    active_trade_info['last_martin_distance'] = ST_distance
                    future_price = df.iloc[i + look_forward_bars]['close_price']
                    
                    # 更新补单信息
                    active_trades[trade_time]['follow_orders'] += 1

                    # 计算当前补单的盈亏金额
                    follow_order_number = active_trade_info['follow_orders']
                    current_win_profit = active_trade_info['current_win_profit']
                    current_loss_cost = active_trade_info['current_loss_cost']
                    
                    # 从第二单开始，每次盈亏是上次的倍数
                    if follow_order_number >= 1:
                        # 前一单的交易结果是未来信息，不应该判断，这里不能这么设定
                        # 获取前一单的交易结果
                        #previous_trades = [t for t in trades if t.get('initial_trade_time') == trade_time 
                        #                  and t.get('follow_order_number') == follow_order_number - 1]
                        #
                        #if previous_trades and not previous_trades[-1]['is_win']:
                        #    # 如果前一单亏损，则放大盈亏倍数
                        #    current_win_profit = current_win_profit * martin_multiplier
                        #    current_loss_cost = current_loss_cost * martin_multiplier
                        #else:
                        #    # 如果前一单盈利或没有前一单记录，恢复到初始值
                        if follow_order_number == 1:
                            current_win_profit = win_profit * martin_multiplier
                            current_loss_cost = loss_cost * martin_multiplier
                        else:
                            #current_win_profit = win_profit
                            #current_loss_cost = loss_cost
                            current_win_profit = current_win_profit
                            current_loss_cost = current_loss_cost
                        # 更新活跃交易中的盈亏金额
                        active_trades[trade_time]['current_win_profit'] = current_win_profit
                        active_trades[trade_time]['current_loss_cost'] = current_loss_cost

                    trade_info = {
                        'datetime': current_time,
                        'entry_price': entry_price,
                        'future_price': future_price,
                        'st_distance': ST_distance,
                        'is_win': future_price > entry_price,
                        'balance': current_balance,
                        'rsi': row['RSI'],
                        'win_profit': current_win_profit,
                        'loss_cost': current_loss_cost,
                        'trade_type': 'follow',
                        'initial_trade_time': trade_time,
                        'follow_order_number': follow_order_number
                    }
                    
                    if trade_info['is_win']:
                        current_balance += current_win_profit
                    else:
                        current_balance -= current_loss_cost
                    trade_info['balance'] = current_balance
                        
                    trades.append(trade_info)
        
        # 记录每一行的余额
        results.append({
            'datetime': current_time,
            'balance': current_balance
        })
    
    # 转换结果为DataFrame
    results_df = pd.DataFrame(results).set_index('datetime')
    trades_df = pd.DataFrame(trades).set_index('datetime')
    
    # 计算统计数据
    total_trades = len(trades)
    winning_trades = sum(1 for trade in trades if trade['is_win'])
    win_rate = winning_trades / total_trades if total_trades > 0 else 0
    
    stats = {
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'win_rate': win_rate,
        'final_balance': current_balance,
        'profit': current_balance - initial_balance
    }
    
    return results_df, trades_df, stats

def should_open(df: pd.DataFrame,index_now:int):
    # 获取当前和前一个时间点的数据
    current = df.iloc[index_now]
    prev = df.iloc[index_now - 1]
    
    # 条件1：均线系统
    #ema_cross = prev['ema3'] <= prev['ema10'] and current['ema3'] > current['ema10']  # 3EMA上穿10EMA
    #ema_arranged = current['ema3'] > current['ema5'] and current['ema5']> current['ema10']  and current['ema10']> current['ema21']  # 均线依次排列
    #price_above_emas = current['close_price'] > current['ema3'] and current['close_price'] > current['ema10']  # 价格在均线上方
    #ema10_up_trend = current['ema10'] > prev['ema10']  # 10EMA向上倾斜
    #condition1 = ema_arranged and ema_cross

    # 条件2：MACD
    #macd_above_zero = current['macd'] > 0  # MACD在零轴上方
    #macd_increasing = current['macd'] > prev['macd']  # MACD柱状图放大
    #macd_cross = (prev['macd'] <= 0 and current['macd'] > 0) or (df.iloc[index_now-3:index_now+1]['macd'] > 0).all()  # MACD金叉刚形成或形成不久
    #condition2 = macd_above_zero and macd_increasing and macd_cross
    #condition2 = True

    # 条件3：ADX
    #adx_strong = current['adx'] > 25  # ADX高于25
    #di_positive = current['plus_di'] > current['minus_di']  # +DI在-DI上方
    #di_negative = current['plus_di'] < current['minus_di']  # +DI在-DI下方
    #condition3 = adx_strong and di_positive
    #condition3 = (not adx_strong) and di_positive
    
    # 条件4：RSI
    #rsi_not_overbought = current['RSI'] < 70  # RSI不在超买区
    #rsi_strong = current['RSI'] > 50  # RSI保持强势
    #rsi_oversell = current['RSI'] < 30  # RSI在超卖区
    #condition4 = rsi_strong and rsi_not_overbought
    #condition4 = True#rsi_oversell
    
    # 条件51：最近3根K线为红色
    last_3_candles = df.iloc[index_now-2:index_now+1]
    red_candles = (last_3_candles['close'] < last_3_candles['open']).all()
    condition51 = red_candles

    # 条件52：最近3根K线为红色且最多有一个十字星
    last_3_candles = df.iloc[index_now-2:index_now+1]
    red_flags = last_3_candles['close'] < last_3_candles['open']
    red_count = red_flags.sum()

    doji_threshold = 0.1  # 定义十字星的阈值：实体小于K线总范围的10%

    def is_doji(candle):
        if candle['high'] == candle['low']:
            return False
        return abs(candle['close'] - candle['open']) <= doji_threshold * (candle['high'] - candle['low'])

    if red_count == 3:
        condition52 = True
    elif red_count == 2:
        non_red = last_3_candles[~red_flags]
        condition52 = non_red.apply(is_doji, axis=1).all()
    else:
        condition52 = False

    # 条件6：RSI超卖面积
    rsi_cumulative_area = current['RSI_cumulative_area'] > 15
    condition6 = rsi_cumulative_area

    rsi_cumulative_area2 = current['RSI_cumulative_area'] > 35
    condition7 = rsi_cumulative_area2

    # 条件8：当前成交量高于平均
    #avg_volume = df['volume'].rolling(window=20).mean().iloc[index_now]*1.2
    #condition8 = current['volume'] > avg_volume

    # 条件9：成交量放大至少 20%
    #prev_volume = df['volume'].iloc[index_now-1]
    #condition9 = current['volume'] > prev_volume * 1.2

    # 条件10：成交量创新高
    #max_vol = df['volume'].iloc[index_now-5:index_now].max()
    #condition10 = current['volume'] > max_vol
    
    # 条件7：BB
    #bb_lower = current['bb_lower']
    #bb_over_sell = current['close_price'] < bb_lower
    #condition7 = bb_over_sell
    #condition7 = True

    #条件8 stochf
    #fastk = current['fastk']
    #faskk_1 = prev['fastk']
    #stochf_not_over_buy=fastk<80
    #stochf_over_sell_cross=fastk>20 and faskk_1<20
    #condition8 = stochf_not_over_buy
    #condition8 = True
    
    # 条件9：布欧策略
    #bias2 = current['bias2']
    #atrClose=current['atrClose']
    #r=(current['close_price']-current['open_price'])/current['open_price']*100#跌帽
    #atr_threshold=0.0013#atr动能阈值

    #bias2_over_sell = bias2>10
    #if current['high_price']-current['open_price']!=0:
    #    good_shape = current['close_price']<current['open_price'] and abs(current['close_price']-current['low_price'])/abs(current['high_price']-current['open_price'])>2 and abs(current['close_price']-current['low_price'])>abs(current['open_price']-current['close_price'])#下引线是上引线的2倍，且下引线大于实体部分
    #else:
    #    good_shape = False
    #atr_oversell = current['close_price']<current['open_price'] and abs(r)>atrClose*100 #下跌幅度 atr的100倍 大概0.2左右
    #atr_oversell_2=atrClose>atr_threshold
    #condition9 = atr_oversell_2

    # 条件10 atr相关
    #bias2 = current['bias2']
    #bias2_over_sell = bias2>5
    #atr_threshold=0.0013#atr动能阈值
    #atrClose=current['atrClose']
    #r=(current['close_price']-current['open_price'])/current['open_price']*100#跌帽
    #atr_oversell = current['close_price']<current['open_price'] and abs(r)>atrClose*100
    #atr_oversell_2=atrClose>atr_threshold
    #condition10 = atr_oversell_2


    # 所有条件都满足才返回True
    #return (condition5 and condition6)#or condition7
    return (condition52 and condition6)

def should_reopen(df: pd.DataFrame,index_now:int,active_trade_info:dict, max_martin_orders:int):
    #current = df.iloc[index_now]
    #martin_price_gap=0.03
    #break_martin_gap =(current['close_price']-active_trade_info['last_order_price'])/active_trade_info['last_order_price'] < -martin_price_gap 
    #in_martin_order_num_limit = active_trade_info['follow_orders'] < max_martin_orders
    #rsi_oversell= current['RSI'] < 25

    #break_martin_gap_2=active_trade_info['last_order_price']-current['close_price']>active_trade_info['last_martin_distance']*1.2

    # 补单条件2,rsi底背离(效果一般，暂不使用)
    # 获取前几个时间点的数据用于判断背离
    #price_look_back = 10
    #rsi_look_back = 5
    #prev_price_data = df.iloc[index_now-price_look_back:index_now]#这里不能包含当前k线的收盘价，否则无法判断是否创新低
    #prev_rsi_data = df.iloc[index_now-rsi_look_back:index_now]
    ## 超卖区判断 (RSI < 30)
    #rsi_oversold = current['RSI'] < 30
    ## 底背离判断：价格创新低但RSI没有创新低
    #price_new_low = current['close_price'] < prev_price_data['close_price'].min()
    #rsi_not_new_low = current['RSI'] > prev_rsi_data['RSI'].min()
    #bullish_divergence = rsi_oversold and price_new_low and rsi_not_new_low

    #return (break_martin_gap and in_martin_order_num_limit and rsi_oversell) or (break_martin_gap_2 and in_martin_order_num_limit)
    #return (bullish_divergence and in_martin_order_num_limit) or (break_martin_gap_2 and in_martin_order_num_limit and rsi_oversell)
    #return break_martin_gap_2 and in_martin_order_num_limit and rsi_oversell
    current = df.iloc[index_now]
    
    # 基本限制条件
    in_martin_order_num_limit = active_trade_info['follow_orders'] < max_martin_orders
    
    if not in_martin_order_num_limit:
        return False  # 如果已达到最大补单次数，直接返回False
    
    # 条件1：价格跌破止损距离的1.2倍
    break_martin_gap = active_trade_info['last_order_price'] - current['close'] > active_trade_info['last_martin_distance'] * 1.2
    break_martin_gap_2=active_trade_info['last_order_price']-current['close']>active_trade_info['last_martin_distance']*0.3
    
    # RSI超卖
    rsi_oversell = current['RSI'] < 25

    # 补单条件2,rsi底背离
    # 获取前几个时间点的数据用于判断背离
    price_look_back = 30
    rsi_look_back = 30
    prev_price_data = df.iloc[index_now-price_look_back:index_now]#这里不能包含当前k线的收盘价，否则无法判断是否创新低
    prev_rsi_data = df.iloc[index_now-rsi_look_back:index_now]
    # 超卖区判断 (RSI < 30)
    rsi_oversold = current['RSI'] < 30
    price_new_low = current['close'] < prev_price_data['close'].min()
    rsi_not_new_low = current['RSI'] > prev_rsi_data['RSI'].min()

    bullish_divergence = rsi_oversold and price_new_low #and rsi_not_new_low

    # 辅助条件：
    # 判断当前RSI是否是第二次下穿30
    rsi_cross_window = 30  # 预设长度为10,等于waitduration
    rsi_data = df.iloc[index_now-rsi_cross_window:index_now+1]['RSI']  # 获取包含当前点的RSI数据
    # 计算RSI下穿30的次数
    cross_count = 0
    for i in range(1, len(rsi_data)):
        if rsi_data.iloc[i] < 30 and rsi_data.iloc[i-1] >= 30:
            cross_count += 1
    # 判断是否是第二次下穿30
    second_cross_below_30 = cross_count >= 2
    
    
    # 条件4：K线形态 - 检查是否有吞没形态或锤子线
    hammer_pattern = False
    if current['high'] != current['low'] and current['close'] != current['open']:  # 避免除零错误
        body_size = abs(current['close'] - current['open'])
        total_size = current['high'] - current['low']
        lower_shadow = current['close'] - current['low'] if current['close'] > current['open'] else current['open'] - current['low']
        
        # 锤子线：下引线至少是实体的两倍，且实体较小,小于整体的0.3倍
        hammer_pattern = (lower_shadow / body_size > 2) and (body_size / total_size < 0.15)
    
    # 组合条件：
    # 1. 价格跌破止损距离1.2倍 + RSI超卖
    condition1 = break_martin_gap and rsi_oversell
    # 2. RSI底背离
    condition2 = bullish_divergence
    # 3. 锤子线形态
    condition3 = hammer_pattern and rsi_oversell

    last_3_candles = df.iloc[index_now-2:index_now+1]
    condition4 = (last_3_candles['close'] < last_3_candles['open']).all() and current['RSI_cumulative_area'] > 15

    # 条件5：反弹结束后判断condition4
    # 1. 找到上次下单的索引
    last_order_price = active_trade_info['last_order_price']
    # 这里假设last_order_price在df中唯一，若不唯一可用时间戳辅助
    last_order_idx = None
    for idx in range(index_now-1, 0, -1):
        if abs(df.iloc[idx]['close'] - last_order_price) < 1e-8:
            last_order_idx = idx
            break

    condition5 = False
    if last_order_idx is not None and last_order_idx < index_now-3:
        # 2. 判断反弹区间
        rebound = True
        rebound_end_idx = last_order_idx
        for idx in range(last_order_idx+1, index_now):
            if df.iloc[idx]['close'] > df.iloc[idx-1]['close']:
                rebound_end_idx = idx
            else:
                # 反弹结束
                continue
        # 3. 反弹结束后判断condition4
        if index_now > rebound_end_idx + 2:
            if condition4 == True:
                condition5 = True

    last_3_candles = df.iloc[index_now-2:index_now+1]
    red_flags = last_3_candles['close'] < last_3_candles['open']
    red_count = red_flags.sum()
    doji_threshold = 0.1  # 定义十字星的阈值：实体小于K线总范围的10%
    def is_doji(candle):
        if candle['high'] == candle['low']:
            return False
        return abs(candle['close'] - candle['open']) <= doji_threshold * (candle['high'] - candle['low'])
    if red_count == 3:
        shape = True
    elif red_count == 2:
        non_red = last_3_candles[~red_flags]
        shape = non_red.apply(is_doji, axis=1).all()
    else:
        shape = False
    condition5 = shape and current['RSI'] < 30

    # 判断当前K线的时间是否大于2025-05-19 01:50:00
    #if pd.to_datetime(current.name) > pd.to_datetime('2025-05-19 01:50:00'):
    #    pass  # 这里可以根据需要添加后续逻辑

    #conditions = [condition1,condition2,condition3,condition4]
    conditions = [condition1,
                  condition2 and break_martin_gap_2 and second_cross_below_30,
                  condition3 and break_martin_gap_2 and second_cross_below_30,
                  condition5]
    condition_count = sum(1 for cond in conditions if cond)

    #return (condition3 or condition4) and break_martin_gap_2 and second_cross_below_30#condition2 and break_martin_gap_2
    #return ((condition3 or condition4) and break_martin_gap_2 and second_cross_below_30) or condition5
    #return (condition2 or condition3) and break_martin_gap_2 and second_cross_below_30
    return (condition_count >= 2)

def resample_1min_to_nmin(df:pd.DataFrame,n=10,offset=None):
    """将1分钟K线数据合成为10分钟K线"""
    # 重置索引
    df = df.reset_index()
    # 确保datetime列为datetime类型
    df['datetime'] = pd.to_datetime(df['datetime'])
    # 设置datetime为索引
    df = df.set_index('datetime')
    # 定义聚合规则
    agg_dict = {
        'symbol': 'first', 
        'exchange': 'first',
        'interval': 'first',
        'volume': 'sum',
        'turnover': 'sum',
        'open_interest': 'last',
        'open_price': 'first',
        'high_price': 'max',
        'low_price': 'min',
        'close_price': 'last'
    }
    # 按10分钟重采样并聚合
    resampled = df.resample(f'{n}min',offset=offset).agg(agg_dict)
    # 重置索引，使datetime重新成为列
    resampled = resampled.reset_index()
    return resampled


