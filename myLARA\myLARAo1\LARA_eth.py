"""
示例文件：LARA_eth.py
这个文件包含了对LARA模型的定义，与原方法基本一致，但针对eth数据做了一些路径或特征上的调整
"""

import os
import lightgbm as lgb
import numpy as np
import pandas as pd
from typing import Text, Union

from qlib.model.base import Model
from qlib.data.dataset import DatasetH
from qlib.data.dataset.handler import DataHandlerLP
from qlib.log import get_module_logger

import warnings
warnings.filterwarnings('ignore')

import imp
# 假设已经将 model_all_utils_eth.py 和 model_all_eth.py 放置在同级目录下
model_all_utils = imp.load_source('model_all_utils_eth', './model_all_utils_eth.py')
model_all = imp.load_source('model_all_eth', './model_all_eth.py')


class LARA_ETH(Model):
    """LARA模型示例，基于ETH数据的完整实现"""

    def __init__(
        self,
        use_metric='True',
        use_multi_label=True,
        shuffle=True,
        preprocessing='None',
        metric_method='ITML_Supervised',
        ping="True",
        knn_rnn='knn',
        points=100,
        radius=100,
        use_dual=True,
        dual_numbers=5,
        dual_ratio=0.01,
        **kwargs
    ):

        self.logger = get_module_logger("LARA_ETH")

        # 一些关键参数
        self.use_metric = use_metric
        self.use_multi_label = use_multi_label
        self.shuffle = shuffle
        self.preprocessing = preprocessing
        self.metric_method = metric_method
        self.ping = ping
        self.knn_rnn = knn_rnn
        self.points = points
        self.radius = radius
        self.use_dual = use_dual
        self.dual_numbers = dual_numbers
        self.dual_ratio = dual_ratio

        # 这里的 params 是 lightgbm 的参数
        self.params = {
            'objective': 'binary',
            'boosting_type': 'gbdt',
            'metric': {'binary_logloss', 'auc'},
            'silent': 1
        }
        # 设置随机种子
        model_all_utils.set_seed(0, self.params)

    def fit(self, dataset: DatasetH):
        """
        训练过程
        """
        # 划分数据集
        df_train, df_valid, df_test = dataset.prepare(
            ["train", "valid", "test"], col_set=["feature", "label"], data_key=DataHandlerLP.DK_L
        )

        x_valid, y_valid = df_valid["feature"], df_valid["label"]
        x_train, y_train = df_train["feature"], df_train["label"]
        x_test, y_test = df_test["feature"], df_test["label"]

        # 为了后续处理方便，把 instrument、datetime 取出来
        y_train['instrument'] = y_train.index.get_level_values('instrument')
        y_train['datetime'] = y_train.index.get_level_values('datetime')
        y_test['instrument'] = y_test.index.get_level_values('instrument')
        y_test['datetime'] = y_test.index.get_level_values('datetime')

        # x_train.columns 为特征列
        self.usefv = x_train.columns
        # y 列（涨跌幅或收益）
        self.out = y_train.columns[0]

        self.logger.info("Train samples: {}, Valid samples: {}, Test samples: {}".format(
            x_train.shape[0], x_valid.shape[0], x_test.shape[0]))
        self.logger.info("Positive ratio in each segment (标签>0.001): {} / {} / {}".format(
            y_valid[y_valid[self.out] > 0.001].shape[0],
            y_train[y_train[self.out] > 0.001].shape[0],
            y_test[y_test[self.out] > 0.001].shape[0],
        ))

        # 合并特征和标签
        df_train = pd.concat([x_train[self.usefv], y_train], axis=1)
        df_test = pd.concat([x_test[self.usefv], y_test], axis=1)

        # 数据处理（主要是二分类标签打标和多标签打标）
        df_train, df_train_ping, train_label_metric_learning = self.data_processing(df_train)
        df_test = self.data_processing(df_test, train=False)

        # 分成(X, Y)对
        train_dataset = (df_train[self.usefv], df_train[[self.out, 'label', 'instrument', 'datetime']])
        test_dataset = (df_test[self.usefv], df_test[[self.out, 'label', 'instrument', 'datetime']])
        ping_dataset = (df_train_ping[self.usefv], df_train_ping[[self.out, 'label', 'instrument', 'datetime']])

        # 是否进行度量学习
        # 当 use_metric == 'True' 时进行，得到映射后的向量
        if self.use_metric == "True":
            (x_train_transform, y_train_transform, 
             x_test_transform, y_test_transform, 
             x_ping_transform, y_ping_transform, 
             self.metric_model) = model_all.metric_learning(
                self, train_dataset, test_dataset, ping_dataset, train_label_metric_learning
            )
        else:
            x_train_transform, y_train_transform = train_dataset[0], train_dataset[1]
            x_test_transform, y_test_transform = test_dataset[0], test_dataset[1]
            x_ping_transform, y_ping_transform = ping_dataset[0], ping_dataset[1]

        self.logger.info("Complete Metric Learning for ETH")

        # 是否把 ping (即比较接近边缘的样本) 也合并进训练集
        if self.ping == "True":
            x_train_transform = pd.concat([x_train_transform, x_ping_transform], axis=0)
            y_train_transform = pd.concat([y_train_transform, y_ping_transform], axis=0)
            x_train_transform.index = np.arange(x_train_transform.shape[0])
            y_train_transform.index = np.arange(y_train_transform.shape[0])
        
        # 是否执行 KNN 或 RNN 的样本筛选
        if self.knn_rnn != "None":
            ann_path = os.path.join('./', 'ann_graph_eth.bin')
            x_train_transform, y_train_transform, x_test_transform, y_test_transform = model_all.KNN(
                x_train_transform, y_train_transform, self, self.params, x_test_transform, y_test_transform, ann_path, train_test='train')

        # 记录测试集的日期索引，最终输出时要用
        test_date = y_test_transform[['instrument', 'datetime']]
        test_date.index = np.arange(test_date.shape[0])

        self.logger.info("Complete KNN/RNN filtering for ETH")

        # 用LightGBM进行训练
        bst = lgb.train(self.params, 
                        lgb.Dataset(x_train_transform, y_train_transform['label'])) 
        
        # dual label过程
        if self.use_dual:
            changes, gbm_list, predicted_df = model_all_utils.dual_label_one(
                x_train_transform, 
                y_train_transform, 
                x_test_transform, 
                y_test_transform, 
                bst, 
                self, 
                self.params, 
                self.usefv
            )

            predicted_copy = predicted_df.copy()
            # 这里把多个模型预测结果求平均
            predicted_copy['signal'] = predicted_df.mean(1)
            # 恢复多重索引
            predicted_copy.index = pd.MultiIndex.from_frame(test_date[['datetime', 'instrument']])

            self.predicted = predicted_copy
        else:
            # 如果不启用dual label，就只用单模型推断
            pred_score = bst.predict(x_test_transform)
            self.predicted = pd.DataFrame(
                pred_score, 
                columns=['signal'], 
                index=pd.MultiIndex.from_frame(test_date[['datetime', 'instrument']])
            )

    def data_processing(self, df_train, train=True):
        """
        数据处理步骤：包括“是否大于某阈值则为1，否则为0”的基本二分类打标；
        并生成度量学习所需的多档标签
        """

        # 先给 label 列，涨跌幅<=0.001的为0，大于0.001的为1，这个阈值可自己调整
        df_train['label'] = 0
        df_train.loc[df_train[self.out] <= 0.001, 'label'] = 0
        df_train.loc[df_train[self.out] >= 0.001, 'label'] = 1

        if not train:
            return df_train

        # 对度量学习的标签做更细化区分(六档)
        metric_learning_label = pd.DataFrame([])
        metric_learning_label[self.out] = df_train[self.out].copy()
        metric_learning_label['label'] = 0
        metric_learning_label.loc[metric_learning_label[self.out] >= 1e-3, 'label'] = 5
        metric_learning_label.loc[(metric_learning_label[self.out] < 1e-3) & (metric_learning_label[self.out] >= 5e-4), 'label'] = 4
        metric_learning_label.loc[(metric_learning_label[self.out] < 5e-4) & (metric_learning_label[self.out] >= 0), 'label'] = 3
        metric_learning_label.loc[(metric_learning_label[self.out] < 0) & (metric_learning_label[self.out] >= -5e-4), 'label'] = 2
        metric_learning_label.loc[(metric_learning_label[self.out] < -5e-4) & (metric_learning_label[self.out] >= -1e-3), 'label'] = 1
        metric_learning_label.loc[metric_learning_label[self.out] < -1e-3, 'label'] = 0

        # 利用model_all_utils里写的sample_equal方法对数据做选样、平衡等处理
        df_train, df_train_ping, train_label_metric_learning = model_all_utils.sample_equal(
            df_train, metric_learning_label, pcteject=0.001, out=self.out
        )

        return df_train, df_train_ping, train_label_metric_learning

    def predict(self, dataset: DatasetH, segment: Union[Text, slice] = "test"):
        """
        预测过程：将模型训练结束后存储的 self.predicted 结果返回
        """
        return self.predicted['signal']