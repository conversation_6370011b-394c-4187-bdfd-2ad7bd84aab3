{"name": "cifar10_resnet34_multistep", "n_gpu": 1, "seed": 123, "arch": {"type": "resnet34", "args": {"num_classes": 10}}, "num_classes": 10, "data_loader": {"type": "CIFAR10DataLoader", "args": {"data_dir": "./dir_to_data", "batch_size": 128, "shuffle": true, "num_batches": 0, "validation_split": 0, "num_workers": 8, "pin_memory": true}}, "optimizer": {"type": "SGD", "args": {"lr": 0.02, "momentum": 0.9, "weight_decay": 0.001}}, "train_loss": {"type": "CCE_GTLoss", "args": {}}, "val_loss": "CrossEntropyLoss", "metrics": ["my_metric", "my_metric2"], "lr_scheduler": {"type": "MultiStepLR", "args": {"milestones": [40, 80], "gamma": 0.01}}, "trainer": {"epochs": 120, "warmup": 0, "save_dir": "saved/", "save_period": 1, "verbosity": 2, "label_dir": "saved/", "monitor": "max test_my_metric", "early_stop": 2000, "tensorboard": false, "mlflow": true, "_percent": "Percentage of noise", "percent": 0.8, "_begin": "When to begin updating labels", "begin": 0, "_asym": "symmetric noise if false", "asym": false}}