# utils.py
import pandas as pd
import numpy as np
import hnswlib
from sklearn.preprocessing import StandardScaler
import lightgbm as lgb
from metric_learn import ITML_Supervised, SDML_Supervised

def load_eth(path):
    df = pd.read_csv(path, parse_dates=["timestamp"])
    df = df.sort_values("timestamp").reset_index(drop=True)
    return df

def featurize(df, lookback=10):
    # 计算简单的移动平均、波动率、动量等
    X = []
    for i in range(lookback, len(df)):
        window = df.iloc[i-lookback:i]
        features = []
        # OHLCV 原始
        features += window["close"].values.tolist()
        # 均线
        features.append(window["close"].mean())
        features.append(window["close"].std())
        # 动量
        features.append((window["close"].iloc[-1] - window["close"].iloc[0]) / window["close"].iloc[0])
        X.append(features)
    X = pd.DataFrame(X)
    X.columns = [f"c_{i}" for i in range(lookback)] + ["ma", "vol", "mom"]
    # 对齐 timestamp
    ts = df["timestamp"].iloc[lookback:].reset_index(drop=True)
    return X, ts

def compute_labels(df, lookahead=1, threshold=0.001):
    # future return = (close_{t+1} - close_t)/close_t
    ret = (df["close"].shift(-lookahead) - df["close"]) / df["close"]
    return ret

def sample_equal(X, y, pcteject):
    """
    多标签采样：
      正负例剔除绝对值小于 pcteject 的中性样本
      然后正负各采样 min(n_pos, n_neg) 保持平衡
    返回：
      X_bal, y_bal, X_mid, y_mid   # X_mid 用作 ping 数据
    """
    df = X.copy()
    df["ret"] = y
    df["label0"] = (df["ret"] >= pcteject).astype(int)  # up=1/down=0
    mask_mid = df["ret"].abs() < pcteject
    pos = df.loc[df["label0"]==1]
    neg = df.loc[(df["label0"]==0)&(~mask_mid)]
    mid = df.loc[mask_mid]
    m = min(len(pos), len(neg))
    pos = pos.sample(m, random_state=0)
    neg = neg.sample(m, random_state=0)
    X_bal = pd.concat([pos, neg]).reset_index(drop=True)
    y_bal = X_bal["label0"]
    # ping 数据从 mid 中抽样
    m2 = min(m, len(mid))
    mid2 = mid.sample(m2, random_state=1)
    return X_bal.drop(columns=["ret","label0"]), y_bal.values, \
           mid2.drop(columns=["ret","label0"]), mid2["ret"].values

def itml_learn(X_ml, y_ml, n_cons, sp, bp):
    model = ITML_Supervised(num_constraints=n_cons,
                             sparsity_param=sp,
                             balance_param=bp)
    model.fit(X_ml, y_ml)
    return model

def build_hnsw(X_train, space="l2", ef=200, M=16):
    dim = X_train.shape[1]
    p = hnswlib.Index(space=space, dim=dim)
    p.init_index(max_elements=X_train.shape[0], ef_construction=ef, M=M)
    p.add_items(X_train.astype(np.float32))
    p.set_ef(ef)
    return p

def knn_filter(p, X_query, y_train, mode="rnn", k=50, radius=100):
    neigh_idx, dist = p.knn_query(X_query.astype(np.float32), k=k)
    mask_keep = []
    for i, (idx_row, d_row) in enumerate(zip(neigh_idx, dist)):
        if mode=="knn":
            mask_keep.append(True)
        else:  # rnn
            # 如果距离在 radius 内，则用 1/d 强度加权
            # 这里简单：只保留至少一个 neighbor 距离< radius
            mask_keep.append(np.any(d_row<radius))
    mask_keep = np.array(mask_keep)
    return mask_keep

def dual_label(train_X, train_y, model, params, rounds=5, ratio=0.02):
    """
    dual-ratio 循环：每轮用当前模型打分 ，
      取 top (1-ratio) 大样本标记1，小 ratio 标记1 反转，重新训练
    返回：
      models_list, changes_list
    """
    models = []
    changes = []
    df = train_X.copy()
    df["y0"] = train_y
    for r in range(rounds):
        # 预测概率
        proba = model.predict(train_X)
        df["p"] = proba
        # threshold 分割
        n = len(df)
        n1 = int((1-ratio)*n)
        n2 = n - n1
        top_idx = df["p"].nlargest(n1).index
        bot_idx = df["p"].nsmallest(n2).index
        df["label1"] = 0
        df.loc[top_idx, "label1"] = 1
        df.loc[bot_idx, "label1"] = 1
        # 新 label = old_label & label1  或者 (~old_label)&label1
        new_label = (df["y0"] & df["label1"]) | ((1-df["y0"]) & df["label1"])
        changes.append((df["y0"]!= new_label).sum())
        df["y0"] = new_label.astype(int)
        # retrain
        dtrain = lgb.Dataset(train_X, df["y0"])
        model = lgb.train(params, dtrain, verbose_eval=False)
        models.append(model)
    return models, changes

def evaluate(preds, y_true, topk):
    idx = np.argsort(preds)[::-1][:topk]
    ret = y_true[idx]
    precision = (ret>0).mean()
    avg_ret = ret.mean()
    win = ret[ret>0].mean()
    loss = ret[ret<0].mean()
    wlr = abs(win/loss) if loss<0 else np.nan
    return precision, wlr, avg_ret
