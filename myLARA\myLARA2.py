#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
完整复刻LARA方法流程
    1. 数据加载及预处理（针对ETH和CSI300数据可做区分）
    2. 特征工程：计算未来收益率、移动平均、波动率（对于CSI300可增加成交量均值）
    3. 多级标签生成：依据未来收益率生成0～5共六个等级，同时生成二分类标签（上涨为1，否则为0）
    4. 数据划分：按时间划分训练集、辅助ping集和测试集
    5. 度量学习：先用StandardScaler归一化，再利用SDML_Supervised对训练和ping集联合建模，将原始特征映射到新的度量空间
    6. KNN过滤：利用hnswlib构建训练样本索引，对测试数据进行KNN查询，根据邻居标签多数原则过滤低置信样本
    7. 初步LightGBM训练，并采用双标签迭代精炼（Dual Labeling）：迭代更新训练集标签，然后集成多个模型预测
    8. 最后对测试样本输出预测结果并保存到CSV
"""

import os
import random
import numpy as np
import pandas as pd
import argparse
import lightgbm as lgb
import hnswlib
from sklearn.preprocessing import StandardScaler
from metric_learn import SDML_Supervised
from sklearn.metrics import accuracy_score, roc_auc_score

# 1. 设置随机种子，保证可复现
def set_seed(seed=42):
    np.random.seed(seed)
    random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)

# 2. 数据加载（根据data_type选择不同处理逻辑）
def load_data(file_path, data_type='eth'):
    df = pd.read_csv(file_path)
    # 统一转换日期格式
    if 'date' in df.columns:
        try:
            df['date'] = pd.to_datetime(df['date'])
        except:
            df['date'] = pd.to_datetime(df['date'], errors='coerce')
    df = df.dropna(subset=['date'])
    # 对CSI300数据可加入其他处理逻辑
    if data_type.lower() == 'csi300':
        # 如有需要，处理其他特有列
        pass
    return df

# 3. 特征工程：计算未来收益率、移动平均线、波动率等
def feature_engineering(df, data_type='eth'):
    df = df.sort_values(by='date').reset_index(drop=True)
    # 计算未来收益率（例如：用下一个周期close计算相对收益率）
    df['future_return'] = df['close'].shift(-1) / df['close'] - 1
    # 移动平均线
    df['MA5'] = df['close'].rolling(window=5).mean()
    df['MA10'] = df['close'].rolling(window=10).mean()
    # 5日波动率（标准差）
    df['vol_5'] = df['close'].rolling(window=5).std()
    # 对于CSI300数据，增加成交量均值作为特征
    if data_type.lower() == 'csi300' and 'volume' in df.columns:
        df['vol_MA5'] = df['volume'].rolling(window=5).mean()
    df = df.dropna().reset_index(drop=True)
    return df

# 4. 根据未来收益率生成多级标签（0～5）及二分类标签（用于后续训练）
def generate_labels(df, pos_thres=0.001, mid_upper=0.001, mid_lower=0.0005, neg_lower=-0.001, neg_upper=-0.0005):
    # 默认所有样本标签置0
    df['mlabel'] = 0
    # 当未来收益率>=0.001，视为强正（标签5）
    df.loc[df['future_return'] >= pos_thres, 'mlabel'] = 5
    # 当收益在[0.0005, 0.001)区间赋标签4
    df.loc[(df['future_return'] < pos_thres) & (df['future_return'] >= mid_lower), 'mlabel'] = 4
    # [0, 0.0005)赋标签3
    df.loc[(df['future_return'] < mid_lower) & (df['future_return'] >= 0), 'mlabel'] = 3
    # 收益小于0但不太低（[-0.0005, 0)）赋标签2
    df.loc[(df['future_return'] < 0) & (df['future_return'] >= neg_upper), 'mlabel'] = 2
    # 收益进一步下降（[-0.001, -0.0005)）赋标签1
    df.loc[(df['future_return'] < neg_upper) & (df['future_return'] >= neg_lower), 'mlabel'] = 1
    # 低于-0.001赋标签0
    df.loc[df['future_return'] < neg_lower, 'mlabel'] = 0
    # 二分类标签：将多级标签大于0的视为上涨（1），否则为0
    df['bin_label'] = (df['mlabel'] > 0).astype(int)
    return df

# 5. 划分数据集：按时间顺序划分训练集、辅助“ping”集和测试集
def split_data(df, train_ratio=0.7, ping_ratio=0.2):
    total = len(df)
    train_end = int(total * train_ratio)
    train_data = df.iloc[:train_end].reset_index(drop=True)
    test_data = df.iloc[train_end:].reset_index(drop=True)
    # 在训练集中，最后一部分作为ping集（用于度量学习与双标签迭代）
    ping_num = int(len(train_data) * ping_ratio)
    ping_data = train_data.iloc[-ping_num:].reset_index(drop=True)
    main_train = train_data.iloc[:-ping_num].reset_index(drop=True)
    return main_train, ping_data, test_data

# 6. 度量学习：先采用StandardScaler归一化，再用SDML_Supervised对主训练集与ping集联合建模
def apply_metric_learning(X_train, y_train, X_ping, metric_method='SDML_Supervised'):
    scaler = StandardScaler().fit(X_train)
    X_train_scaled = scaler.transform(X_train)
    X_ping_scaled = scaler.transform(X_ping)
    # 合并主训练集和ping集
    X_metric = np.vstack([X_train_scaled, X_ping_scaled])
    # 为增强样本多样性，假设二者标签保持一致（实际使用中可根据实际调整）
    y_metric = np.concatenate([y_train, y_train])
    if metric_method == 'SDML_Supervised':
        ml_model = SDML_Supervised(sparsity_param=0.1, balance_param=0.001, num_constraints=1000)
    else:
        ml_model = SDML_Supervised(sparsity_param=0.1, balance_param=0.001, num_constraints=1000)
    ml_model.fit(X_metric, y_metric)
    # 分别转换训练集和ping集
    X_train_trans = ml_model.transform(X_train_scaled)
    X_ping_trans = ml_model.transform(X_ping_scaled)
    return X_train_trans, X_ping_trans, scaler, ml_model

# 7. 利用hnswlib构造KNN图，对样本进行过滤
def apply_knn_filter(X_train_trans, y_train_bin, X_test_trans, k=10):
    dim = X_train_trans.shape[1]
    num_elements = X_train_trans.shape[0]
    index = hnswlib.Index(space='l2', dim=dim)
    index.init_index(max_elements=num_elements, ef_construction=50, M=16)
    index.add_items(X_train_trans, np.arange(num_elements))
    index.set_ef(10)
    selected_indices = []
    for i in range(X_test_trans.shape[0]):
        nn_indices, _ = index.knn_query(X_test_trans[i], k=k)
        # 将训练样本的二分类标签转换为投票形式（1转为1，0转为-1）
        neighbor_votes = [1 if y_train_bin[j] >= 1 else -1 for j in nn_indices[0]]
        vote_sum = sum(neighbor_votes)
        if vote_sum >= 0:
            selected_indices.append(i)
    X_test_filtered = X_test_trans[selected_indices]
    return X_test_filtered, selected_indices

# 8. 双标签迭代精炼：使用迭代方式更新训练标签并训练多个LightGBM模型，最后集成模型预测
def dual_labeling(X_train_trans, y_train_bin, dual_iterations=5, dual_ratio=0.01):
    models = []
    current_labels = y_train_bin.copy()
    for itr in range(dual_iterations):
        dtrain = lgb.Dataset(X_train_trans, label=current_labels)
        params = {
            'objective': 'binary',
            'boosting': 'gbdt',
            'metric': 'binary_logloss',
            'seed': 42,
            'verbose': -1
        }
        model = lgb.train(params, dtrain, num_boost_round=50)
        preds = model.predict(X_train_trans)
        # 根据预测概率更新标签，取1-dual_ratio分位作为阈值
        threshold = np.quantile(preds, 1 - dual_ratio)
        new_labels = (preds > threshold).astype(int)
        diff = np.sum(current_labels != new_labels)
        print(f"Dual Labeling 迭代 {itr+1}: 标签修改数 = {diff}")
        current_labels = new_labels
        models.append(model)
    return models, current_labels

# 9. 对测试集进行双标签模型集成预测
def dual_label_predict(models, X_test_trans):
    preds = np.mean([model.predict(X_test_trans) for model in models], axis=0)
    return preds

# 10. 定义LARA完整模型类，内部依次完成所有步骤
class LARA:
    def __init__(self, use_metric=True, metric_method='SDML_Supervised', use_dual=True, knn_k=10, dual_iterations=5, dual_ratio=0.01):
        self.use_metric = use_metric
        self.metric_method = metric_method
        self.use_dual = use_dual
        self.knn_k = knn_k
        self.dual_iterations = dual_iterations
        self.dual_ratio = dual_ratio
        self.scaler = None
        self.metric_model = None
        self.lgb_model = None
        self.dual_models = None

    def fit(self, X_train, y_train_bin, X_ping, y_ping_bin):
        # 如果采用度量学习，则用SDML_Supervised对训练数据进行转换
        if self.use_metric:
            X_train_trans, X_ping_trans, scaler, ml_model = apply_metric_learning(X_train, y_train_bin, X_ping, metric_method=self.metric_method)
            self.scaler = scaler
            self.metric_model = ml_model
            X_all_trans = np.vstack([X_train_trans, X_ping_trans])
            y_all_bin = np.concatenate([y_train_bin, y_ping_bin])
        else:
            scaler = StandardScaler().fit(X_train)
            X_train_scaled = scaler.transform(X_train)
            self.scaler = scaler
            X_all_trans = X_train_scaled
            y_all_bin = y_train_bin
        # 对合并数据进行KNN过滤，剔除噪声样本
        X_all_filtered, filtered_indices = apply_knn_filter(X_all_trans, y_all_bin, X_all_trans, k=self.knn_k)
        y_all_filtered = y_all_bin[filtered_indices]
        # 初步训练LightGBM模型
        dtrain = lgb.Dataset(X_all_filtered, label=y_all_filtered)
        params = {
            'objective': 'binary',
            'boosting': 'gbdt',
            'metric': 'binary_logloss',
            'seed': 42,
            'verbose': -1
        }
        self.lgb_model = lgb.train(params, dtrain, num_boost_round=100)
        # 进行双标签迭代精炼
        self.dual_models, updated_labels = dual_labeling(X_all_filtered, y_all_filtered, dual_iterations=self.dual_iterations, dual_ratio=self.dual_ratio)
        self.X_train_trans = X_all_filtered
        self.y_train_final = updated_labels

    def predict(self, X_test):
        # 标准化
        X_test_scaled = self.scaler.transform(X_test)
        if self.use_metric and self.metric_model is not None:
            X_test_trans = self.metric_model.transform(X_test_scaled)
        else:
            X_test_trans = X_test_scaled
        # 对测试样本进行KNN过滤
        X_test_filtered, filtered_indices = apply_knn_filter(self.X_train_trans, self.y_train_final, X_test_trans, k=self.knn_k)
        # 使用双标签集成预测
        preds = dual_label_predict(self.dual_models, X_test_filtered)
        preds_binary = (preds > 0.5).astype(int)
        return preds_binary, filtered_indices

# 11. 主函数：整合上述各流程
def main():
    parser = argparse.ArgumentParser(description="复刻完整LARA方法，适用于ETH或CSI300历史数据")
    parser.add_argument("--data_path", type=str, required=True, help="原始数据CSV文件路径")
    parser.add_argument("--data_type", type=str, default="eth", choices=["eth", "csi300"], help="数据类型：eth或csi300")
    parser.add_argument("--seed", type=int, default=42, help="随机种子")
    args = parser.parse_args()

    set_seed(args.seed)
    # 加载数据
    df = load_data(args.data_path, data_type=args.data_type)
    print(f"原始数据样本数: {len(df)}")
    # 特征工程
    df = feature_engineering(df, data_type=args.data_type)
    print(f"特征工程后数据样本数: {len(df)}")
    # 生成多级标签和二分类标签
    df = generate_labels(df)
    # 根据数据类型选择特征（ETH采用close, MA5, MA10, vol_5；CSI300则额外加入vol_MA5）
    if args.data_type.lower() == 'eth':
        feature_cols = ['close', 'MA5', 'MA10', 'vol_5']
    else:
        feature_cols = ['close', 'MA5', 'MA10', 'vol_5']
        if 'vol_MA5' in df.columns:
            feature_cols.append('vol_MA5')
    X = df[feature_cols].values
    y_bin = df['bin_label'].values
    # 划分训练集、ping集和测试集（时间顺序划分）
    train_df, ping_df, test_df = split_data(df, train_ratio=0.7, ping_ratio=0.2)
    X_train = train_df[feature_cols].values
    y_train_bin = train_df['bin_label'].values
    X_ping = ping_df[feature_cols].values
    y_ping_bin = ping_df['bin_label'].values
    X_test = test_df[feature_cols].values
    y_test_bin = test_df['bin_label'].values
    print(f"训练样本数: {len(X_train)}, Ping样本数: {len(X_ping)}, 测试样本数: {len(X_test)}")

    # 初始化并训练LARA模型（复刻所有步骤）
    model = LARA(use_metric=True, metric_method='SDML_Supervised', use_dual=True, knn_k=10, dual_iterations=5, dual_ratio=0.01)
    model.fit(X_train, y_train_bin, X_ping, y_ping_bin)
    # 对测试集进行预测
    preds_binary, filtered_indices = model.predict(X_test)
    # 由于KNN过滤可能剔除部分测试样本，仅对过滤后的样本计算评价指标
    if len(filtered_indices) > 0:
        y_test_filtered = y_test_bin[filtered_indices]
    else:
        y_test_filtered = y_test_bin
    acc = accuracy_score(y_test_filtered, preds_binary)
    auc_value = roc_auc_score(y_test_filtered, preds_binary) if len(np.unique(y_test_filtered))==2 else 0
    print(f"过滤后测试样本数: {len(y_test_filtered)}")
    print(f"测试准确率: {acc:.4f}, AUC: {auc_value:.4f}")

    # 保存预测结果（包含原始特征、真实标签及预测标签）
    result_df = test_df.iloc[filtered_indices].copy() if len(filtered_indices) > 0 else test_df.copy()
    result_df['prediction'] = preds_binary
    result_df.to_csv("lara_predictions.csv", index=False)
    print("预测结果已保存至 lara_predictions.csv")

if __name__ == "__main__":
    main()