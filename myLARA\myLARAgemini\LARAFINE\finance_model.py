from torch import nn
import torch.nn.functional as F

class FinanceNet(nn.Module):
    def __init__(self, input_dim=158, hidden_dims=[128, 64], num_classes=2):
        """
        金融数据神经网络模型
        
        参数:
            input_dim: 输入特征维度 (默认158)
            hidden_dims: 隐藏层维度列表
            num_classes: 分类类别数
        """
        super(FinanceNet, self).__init__()
        
        self.layers = nn.ModuleList()
        
        # 输入层到第一个隐藏层
        self.layers.append(nn.Linear(input_dim, hidden_dims[0]))
        
        # 隐藏层
        for i in range(len(hidden_dims)-1):
            self.layers.append(nn.Linear(hidden_dims[i], hidden_dims[i+1]))
        
        # 输出层
        self.fc = nn.Linear(hidden_dims[-1], num_classes)
        
    def forward(self, x):
        """
        前向传播
        """
        # 特征提取部分
        for layer in self.layers:
            x = F.relu(layer(x))
            x = F.dropout(x, p=0.2, training=self.training)
        
        # 分类部分
        logits = self.fc(x)
        
        return x, logits  # 返回特征表示和分类结果