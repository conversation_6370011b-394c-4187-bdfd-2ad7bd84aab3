# run_DEnsemble_eth.py
import os
import re
from datetime import datetime, timedelta

import numpy as np
import pandas as pd

from eth_data_processor import ETHDataProcessor
from double_ensemble import DEnsembleModel
from qlib.data.dataset.handler import DataHandlerLP

def main():
    # ------------------------------
    # 1. 配置区
    # ------------------------------
    instrument_name = 'ETHUSDT'
    eth_kline_filepath = '../data/BTCUSDT_BINANCE_2024-01-01_00_00_00_2025-06-01_00_00_00.csv'
    features_pkl = eth_kline_filepath.replace('.csv', '_features.pkl')

    # 读取时间区间，用于8:1:1切分
    match = re.search(
        r'_(\d{4}-\d{2}-\d{2})_\d{2}_\d{2}_\d{2}_(\d{4}-\d{2}-\d{2})_\d{2}_\d{2}_\d{2}',
        eth_kline_filepath
    )
    if not match:
        raise ValueError(f"无法从文件名 {eth_kline_filepath} 提取日期")
    data_start = datetime.strptime(match.group(1), '%Y-%m-%d')
    data_end = datetime.strptime(match.group(2), '%Y-%m-%d')
    total_days = (data_end - data_start).days + 1
    train_days = int(total_days * 0.8)
    valid_days = int(total_days * 0.1)
    test_days = total_days - train_days - valid_days

    train_start = data_start
    train_end   = data_start + timedelta(days=train_days - 1)
    valid_start = train_end + timedelta(days=1)
    valid_end   = valid_start + timedelta(days=valid_days - 1)
    test_start  = valid_end + timedelta(days=1)
    test_end    = data_end

    print("Train :", train_start, "~", train_end)
    print("Valid :", valid_start, "~", valid_end)
    print("Test  :", test_start, "~", test_end)

    # ------------------------------
    # 2. 数据处理
    # ------------------------------
    # 2.1 生成特征（或直接加载）
    if os.path.exists(features_pkl):
        print(f"[INFO] 加载已有特征文件 {features_pkl}")
        df = pd.read_pickle(features_pkl)
        processor = ETHDataProcessor(df, instrument_name=instrument_name)
        processor.feature_columns = [c for c in df.columns if c.startswith('feature_')]
    else:
        print("[INFO] 从 CSV 读取原始 K 线并计算特征")
        raw = pd.read_csv(eth_kline_filepath)
        processor = ETHDataProcessor(raw, instrument_name=instrument_name)
        processor.generate_features_alpha158()
        pd.to_pickle(processor.df, features_pkl)
        print(f"[INFO] 特征已保存到 {features_pkl}")

    # 2.2 生成标签
    prediction_horizon = 2  # 向前多少根K线
    processor.generate_labels(future_periods=prediction_horizon)

    # 2.3 清洗 + 切分
    processed = processor.get_processed_data()
    if processed.empty:
        print("No data after processing. Exit.")
        return

    # 切分得到 MockDatasetH （切分格式与 LARA 中一致）
    dataset = processor.split_data_for_lara(
        processed,
        train_start.strftime('%Y-%m-%d'),
        train_end.strftime('%Y-%m-%d'),
        valid_start.strftime('%Y-%m-%d'),
        valid_end.strftime('%Y-%m-%d'),
        test_start.strftime('%Y-%m-%d'),
        test_end.strftime('%Y-%m-%d'),
    )
    # split_data_for_lara 返回的对象有 train_dict, valid_dict, test_dict，.prepare() 会根据名字返回对应 dict。

    # ------------------------------
    # 3. Double Ensemble 模型训练 & 预测
    # ------------------------------
    # 3.1 指定超参数（可根据需要调优）
    de_params = {
        "base_model": "gbm",
        "loss": "mse",
        "num_models": 3,
        "enable_sr": True,
        "enable_fs": True,
        "alpha1": 1.0,
        "alpha2": 1.0,
        "bins_sr": 10,
        "bins_fs": 5,
        "decay": 0.5,
        "sample_ratios": [0.8, 0.7, 0.6, 0.5, 0.4],
        "sub_weights": [1, 1, 1],
        "epochs": 28,
        # 下面是 LightGBM 参数
        "colsample_bytree": 0.8879,
        "learning_rate": 0.2,
        "subsample": 0.8789,
        "lambda_l1": 205.6999,
        "lambda_l2": 580.9768,
        "max_depth": 8,
        "num_leaves": 210,
        "num_threads": 8,
        "verbosity": -1,
        # 如果希望早停，可加：
        # "early_stopping_rounds": 50,
    }

    print("[INFO] 初始化 Double Ensemble 模型")
    model = DEnsembleModel(**de_params)

    print("[INFO] 开始训练")
    # fit 只会调用 dataset.prepare(["train","valid"],...)
    model.fit(dataset)
    print("[INFO] 训练完成")

    print("[INFO] 对测试集做预测")
    preds = model.predict(dataset, segment="test")  # 返回 Pandas Series，索引=(datetime,instrument)
    print(preds.head())

    # ------------------------------
    # 4. 简单评估（可选）
    # ------------------------------
    test_label_df = dataset.test_dict["label"]
    # 合并信号和真实标签
    df_ = pd.concat([preds.rename("signal"), test_label_df], axis=1, join="inner")
    if df_.shape[0] == 0:
        print("[WARN] 无法合并预测与真实标签")
        return

    # 按 signal 排序，取 top N 做精度评估
    N = min(1000, len(df_))
    thresh_label = 0.001
    top_df = df_.sort_values("signal", ascending=False).iloc[:N].copy()
    top_df["hit"] = (top_df[processor.label_column_name] >= thresh_label).astype(int)
    precision = top_df["hit"].mean()
    # 计算 Win/Loss Ratio (胜率/亏损率)
    win = top_df.loc[top_df[processor.label_column_name] > 0, processor.label_column_name].mean()
    loss = top_df.loc[top_df[processor.label_column_name] < 0, processor.label_column_name].mean()
    wlr = abs(win / loss) if loss and loss != 0 else float('nan')
    avg_ret = top_df[processor.label_column_name].mean()
    print(f"[RESULT] Top {N} 精度: {precision:.2%},Win/Loss Ratio: {wlr:.2f}, 平均真实收益: {avg_ret:.4%}")

    # 如果希望保存结果：
    # os.makedirs("results", exist_ok=True)
    # pd.concat([preds.rename("signal"), test_label_df], axis=1).to_csv("results/de_enseble_eth.csv")

if __name__ == "__main__":
    main()
