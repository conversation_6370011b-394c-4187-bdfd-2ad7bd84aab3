{"name": "webvision_inceptionresnetv2_multistep", "n_gpu": 1, "seed": 123, "arch": {"type": "inceptionresnetv2", "args": {"num_classes": 50}}, "num_classes": 50, "data_loader": {"type": "WebvisionDataLoader", "args": {"data_dir": "/home/<USER>/hdd/webvision/", "batch_size": 64, "shuffle": true, "num_batches": 100, "validation_split": 0, "num_workers": 8, "pin_memory": true}}, "optimizer": {"type": "SGD", "args": {"lr": 0.04, "momentum": 0.9, "weight_decay": 0.0005}}, "train_loss": {"type": "CCELoss"}, "val_loss": "CrossEntropyLoss", "metrics": ["my_metric", "my_metric2"], "lr_scheduler": {"type": "MultiStepLR", "args": {"milestones": [50], "gamma": 0.1}}, "trainer": {"epochs": 100, "warmup": 0, "save_dir": "saved/", "save_period": 1, "verbosity": 2, "label_dir": "saved/", "monitor": "max test_my_metric", "early_stop": 2000, "tensorboard": false, "mlflow": true, "_percent": "Percentage of noise", "percent": 0.8, "_begin": "When to begin updating labels", "begin": 0, "_asym": "symmetric noise if false", "asym": false}}