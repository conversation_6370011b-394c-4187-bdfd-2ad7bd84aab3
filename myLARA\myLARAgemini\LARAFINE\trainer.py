import torch
import torch.nn as nn
import numpy as np
from finance_dataset import FinanceDataset
from torch.utils.data import DataLoader
from finance_model import FinanceNet
from finance_trainer import FinanceTrainer
from torch.nn import functional as F
from robust_loss import GCELoss, ELRLoss, SCELoss

from sklearn.model_selection import train_test_split

def train(df, test_size=0.2, num_epochs=50, batch_size=64, learning_rate=0.001, loss_type='elr'):
    """
    主程序：金融数据清洗与高置信度样本甄别
    
    参数:
        df: 包含158个特征和1个标签的DataFrame
        test_size: 测试集比例
        num_epochs: 训练轮数
        batch_size: 批次大小
        learning_rate: 学习率
        loss_type: 损失函数类型 ('elr', 'sce', 'gce', 'cce')
    
    返回:
        model: 训练好的模型
        clean_indices: 干净样本的索引
    """
    # 分离特征和标签
    X = df.iloc[:, :-1].values  # 158个特征
    y = df.iloc[:, -1].values   # 1个标签
    
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=42)
    
    # 创建数据集
    train_dataset = FinanceDataset(X_train, y_train)
    test_dataset = FinanceDataset(X_test, y_test)
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=4)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
    
    # 创建模型
    input_dim = X_train.shape[1]  # 特征维度
    num_classes = len(np.unique(y))  # 类别数
    model = FinanceNet(input_dim=input_dim, hidden_dims=[128, 64], num_classes=num_classes)
    
    # 创建损失函数
    if loss_type == 'elr':
        criterion = ELRLoss(num_examp=len(train_dataset), num_classes=num_classes, beta=0.7, lambda_=3.0)
    elif loss_type == 'sce':
        criterion = SCELoss(alpha=0.1, beta=1.0, num_classes=num_classes)
    elif loss_type == 'gce':
        criterion = GCELoss(q=0.7, k=0.5, trainset_size=len(train_dataset), truncated=False)
    else:  # cce
        criterion = nn.CrossEntropyLoss()
    
    # 创建优化器
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
    
    # 创建训练器
    trainer = FinanceTrainer(
        model=model,
        criterion=criterion,
        optimizer=optimizer,
        train_loader=train_loader,
        valid_loader=test_loader,
        device='cuda' if torch.cuda.is_available() else 'cpu',
        warmup_epochs=5,
        selection_interval=5
    )
    
    # 训练模型
    trainer.train(num_epochs)
    
    # 绘制训练历史
    trainer.plot_history()
    
    # 选择干净样本
    clean_indices = trainer._select_clean_samples()
    
    return model, clean_indices

def get_high_confidence_samples(df, model, clean_indices, confidence_threshold=0.9):
    """
    获取高置信度样本
    
    参数:
        df: 原始DataFrame
        model: 训练好的模型
        clean_indices: 干净样本的索引
        confidence_threshold: 置信度阈值
    
    返回:
        high_conf_df: 高置信度样本DataFrame
    """
    # 提取特征
    X = df.iloc[:, :-1].values
    
    # 转换为张量
    X_tensor = torch.FloatTensor(X)
    
    # 预测
    model.eval()
    device = next(model.parameters()).device
    with torch.no_grad():
        _, logits = model(X_tensor.to(device))
        probs = F.softmax(logits, dim=1)
        
        # 获取最大概率及其对应的类别
        max_probs, predicted = probs.max(1)
    
    # 选择高置信度样本
    high_conf_mask = (max_probs.cpu().numpy() > confidence_threshold) & np.isin(np.arange(len(df)), clean_indices)
    high_conf_df = df.iloc[high_conf_mask].copy()
    
    # 添加预测标签和置信度列
    high_conf_df['predicted_label'] = predicted[high_conf_mask].cpu().numpy()
    high_conf_df['confidence'] = max_probs[high_conf_mask].cpu().numpy()
    
    return high_conf_df