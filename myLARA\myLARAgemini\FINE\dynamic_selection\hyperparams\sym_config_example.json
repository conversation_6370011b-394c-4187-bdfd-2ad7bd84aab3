{"name": "clothing1m_resnet50_multistep", "n_gpu": 1, "seed": 123, "arch": {"type": "resnet50", "args": {"num_classes": 14}}, "num_classes": 14, "data_loader": {"type": "Clothing1MDataLoader", "args": {"data_dir": "./dir_to_data/clothing1m", "batch_size": 8, "shuffle": true, "num_batches": 2000, "validation_split": 0, "num_workers": 8, "pin_memory": true}}, "optimizer": {"type": "SGD", "args": {"lr": 0.002, "momentum": 0.9, "weight_decay": 0.001}}, "train_loss": {"type": "ELRLoss", "args": {"beta": 0.7, "lambda": 3}}, "val_loss": "CrossEntropyLoss", "metrics": ["my_metric", "my_metric2"], "lr_scheduler": {"type": "MultiStepLR", "args": {"milestones": [7], "gamma": 0.1}}, "trainer": {"epochs": 15, "warmup": 0, "save_dir": "saved/", "save_period": 1, "verbosity": 2, "label_dir": "saved/", "monitor": "max test_my_metric", "early_stop": 2000, "tensorboard": false, "mlflow": true, "_percent": "Percentage of noise", "percent": 0.8, "_begin": "When to begin updating labels", "begin": 0, "_asym": "symmetric noise if false", "asym": false}}