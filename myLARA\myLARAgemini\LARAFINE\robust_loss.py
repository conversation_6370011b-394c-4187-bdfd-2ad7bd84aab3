import torch
from torch import nn
from torch.nn import functional as F
import math

class ELRLoss(nn.Module):
    def __init__(self, num_examp, num_classes=2, beta=0.7, lambda_=3.0):
        """
        ELR (Early Learning Regularization) 损失函数
        适用于带噪标签的学习
        
        参数:
            num_examp: 样本总数
            num_classes: 分类类别数
            beta: ELR更新参数
            lambda_: 正则化强度
        """
        super(ELRLoss, self).__init__()
        self.num_classes = num_classes
        self.beta = beta
        self.lambda_ = lambda_
        self.USE_CUDA = torch.cuda.is_available()
        self.target = torch.zeros(num_examp, self.num_classes).cuda() if self.USE_CUDA else torch.zeros(num_examp, self.num_classes)
        
    def forward(self, output, label, index, mode=None):
        """
        计算ELR损失
        
        参数:
            output: 模型输出的logits
            label: 带噪标签
            index: 样本索引
            mode: 模式选择
        """
        y_pred = F.softmax(output, dim=1)
        y_pred = torch.clamp(y_pred, 1e-4, 1.0-1e-4)
        y_pred_ = y_pred.data.detach()
        self.target[index] = self.beta * self.target[index] + (1-self.beta) * ((y_pred_)/(y_pred_).sum(dim=1,keepdim=True))
        ce_loss = F.cross_entropy(output, label)
        elr_reg = ((1-(self.target[index] * y_pred).sum(dim=1)).log()).mean()
        
        if mode == 'ce':
            final_loss = ce_loss
        else:
            final_loss = ce_loss + self.lambda_ * elr_reg
            
        return final_loss
    
    def update_hist(self, indices, probs):
        """
        更新历史预测概率
        """
        for i, idx in enumerate(indices):
            self.target[idx] = self.beta * self.target[idx] + (1-self.beta) * probs[i]

class ELR_GTLoss(ELRLoss):
    def __init__(self, num_examp, num_classes=2, beta=0.7, lambda_=3.0):
        super(ELR_GTLoss, self).__init__(num_examp, num_classes, beta)
        self.lambda_ = lambda_
        
    def forward(self, output, label, clean_indexs, index):
        """
        计算带有干净样本索引的ELR损失
        
        参数:
            output: 模型输出的logits
            label: 带噪标签
            clean_indexs: 干净样本的布尔索引
            index: 样本索引
        """
        y_pred = F.softmax(output, dim=1)
        y_pred = torch.clamp(y_pred, 1e-4, 1.0-1e-4)
        y_pred_ = y_pred.data.detach()
        self.target[index] = self.beta * self.target[index] + (1-self.beta) * ((y_pred_)/(y_pred_).sum(dim=1,keepdim=True))
        
        ce_loss = F.cross_entropy(output, label, reduction='none')[clean_indexs]
        elr_reg = ((1-(self.target[index] * y_pred).sum(dim=1)).log())[clean_indexs]
        size = output.shape[0] if torch.sum(clean_indexs) == 0 else torch.sum(clean_indexs)
        
        final_loss = torch.sum(ce_loss) + self.lambda_ * torch.sum(elr_reg)
        final_loss /= size
        return final_loss

class SCELoss(nn.Module):
    def __init__(self, alpha=0.1, beta=1.0, num_classes=2):
        """
        SCE (Symmetric Cross Entropy) 损失函数
        
        参数:
            alpha: CE损失权重
            beta: RCE损失权重
            num_classes: 分类类别数
        """
        super(SCELoss, self).__init__()
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.alpha = alpha
        self.beta = beta
        self.num_classes = num_classes
        self.cross_entropy = torch.nn.CrossEntropyLoss()
        self.A = math.exp(-4)
        
    def forward(self, pred, labels, index=None, mode=None):
        # CCE
        ce = self.cross_entropy(pred, labels)

        # RCE
        pred = F.softmax(pred, dim=1)
        pred = torch.clamp(pred, min=1e-7, max=1.0)
        label_one_hot = torch.nn.functional.one_hot(labels, self.num_classes).float().to(self.device)
        label_one_hot = torch.clamp(label_one_hot, min=self.A, max=1.0)
        rce = (-1*torch.sum(pred * torch.log(label_one_hot), dim=1))

        # Loss
        if mode == 'ce':
            loss = ce
        else:
            loss = self.alpha * ce + self.beta * rce.mean()
        return loss

class GCELoss(nn.Module):
    def __init__(self, q=0.7, k=0.5, trainset_size=50000, truncated=False):
        """
        GCE (Generalized Cross Entropy) 损失函数
        
        参数:
            q: 损失函数参数
            k: 截断参数
            trainset_size: 训练集大小
            truncated: 是否使用截断版本
        """
        super(GCELoss, self).__init__()
        self.q = q
        self.k = k
        self.truncated = truncated
        self.weight = torch.nn.Parameter(data=torch.ones(trainset_size, 1), requires_grad=False)
             
    def forward(self, logits, targets, indexes, mode=None):
        p = F.softmax(logits, dim=1)
        Yg = torch.gather(p, 1, torch.unsqueeze(targets, 1))
        
        if self.truncated == True:
            if mode == 'ce':
                ce = nn.CrossEntropyLoss(reduction='none')
                loss = ce(logits, targets)
                loss = torch.mean(loss)
            else:
                loss = ((1-(Yg**self.q))/self.q)*self.weight[indexes] - ((1-(self.k**self.q))/self.q)*self.weight[indexes]
                loss = torch.mean(loss)
        else:
            if mode == 'ce':
                ce = nn.CrossEntropyLoss(reduction='none')
                loss = ce(logits, targets)
                loss = torch.mean(loss)
            else:
                loss = (1-(Yg**self.q))/self.q
                loss = torch.mean(loss)

        return loss