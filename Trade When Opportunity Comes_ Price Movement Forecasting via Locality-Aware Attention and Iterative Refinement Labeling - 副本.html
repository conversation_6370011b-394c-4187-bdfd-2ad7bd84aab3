<section class="ltx_subsection" id="S4.SS2">
<h3 class="ltx_title ltx_title_subsection">
<span class="ltx_tag ltx_tag_subsection">4.2 </span>Iterative <span class="ltx_text ltx_framed ltx_framed_underline" id="S4.SS2.1.1">R</span>efinement L<span class="ltx_text ltx_framed ltx_framed_underline" id="S4.SS2.2.2">a</span>beling (RA-Labeling)</h3>
<div class="ltx_para" id="S4.SS2.p1">
<p class="ltx_p" id="S4.SS2.p1.5">Price movement is notoriously difficult to forecast because financial markets are complex dynamic systems <cite class="ltx_cite ltx_citemacro_cite"><PERSON> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib7" title="">2018</a>); <PERSON> and <PERSON><PERSON><PERSON><PERSON> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib20" title="">2011</a>)</cite>.
Indeed, we observe that the complicated financial markets sometimes mislead ML systems to produce opposite prediction results with extremely high confidence.
For example, some training samples get a high (low) probability of the positive prediction <math alttext="Pr(y=1)" class="ltx_Math" display="inline" id="S4.SS2.p1.1.m1.1"><semantics id="S4.SS2.p1.1.m1.1a"><mrow id="S4.SS2.p1.1.m1.1.1" xref="S4.SS2.p1.1.m1.1.1.cmml"><mi id="S4.SS2.p1.1.m*******" xref="S4.SS2.p1.1.m*******.cmml">P</mi><mo id="S4.SS2.p1.1.m*******" xref="S4.SS2.p1.1.m*******.cmml">⁢</mo><mi id="S4.SS2.p1.1.m1.1.1.4" xref="S4.SS2.p1.1.m1.1.1.4.cmml">r</mi><mo id="S4.SS2.p1.1.m*******a" xref="S4.SS2.p1.1.m*******.cmml">⁢</mo><mrow id="S4.SS2.p1.1.m*******.1" xref="S4.SS2.p1.1.m*******.1.1.cmml"><mo id="S4.SS2.p1.1.m*******.1.2" stretchy="false" xref="S4.SS2.p1.1.m*******.1.1.cmml">(</mo><mrow id="S4.SS2.p1.1.m*******.1.1" xref="S4.SS2.p1.1.m*******.1.1.cmml"><mi id="S4.SS2.p1.1.m*******.1.1.2" xref="S4.SS2.p1.1.m*******.1.1.2.cmml">y</mi><mo id="S4.SS2.p1.1.m*******.1.1.1" xref="S4.SS2.p1.1.m*******.1.1.1.cmml">=</mo><mn id="S4.SS2.p1.1.m*******.1.1.3" xref="S4.SS2.p1.1.m*******.1.1.3.cmml">1</mn></mrow><mo id="S4.SS2.p1.1.m*******.1.3" stretchy="false" xref="S4.SS2.p1.1.m*******.1.1.cmml">)</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p1.1.m1.1b"><apply id="S4.SS2.p1.1.m1.1.1.cmml" xref="S4.SS2.p1.1.m1.1.1"><times id="S4.SS2.p1.1.m*******.cmml" xref="S4.SS2.p1.1.m*******"></times><ci id="S4.SS2.p1.1.m*******.cmml" xref="S4.SS2.p1.1.m*******">𝑃</ci><ci id="S4.SS2.p1.1.m1.1.1.4.cmml" xref="S4.SS2.p1.1.m1.1.1.4">𝑟</ci><apply id="S4.SS2.p1.1.m*******.1.1.cmml" xref="S4.SS2.p1.1.m*******.1"><eq id="S4.SS2.p1.1.m*******.1.1.1.cmml" xref="S4.SS2.p1.1.m*******.1.1.1"></eq><ci id="S4.SS2.p1.1.m*******.1.1.2.cmml" xref="S4.SS2.p1.1.m*******.1.1.2">𝑦</ci><cn id="S4.SS2.p1.1.m*******.1.1.3.cmml" type="integer" xref="S4.SS2.p1.1.m*******.1.1.3">1</cn></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p1.1.m1.1c">Pr(y=1)</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p1.1.m1.1d">italic_P italic_r ( italic_y = 1 )</annotation></semantics></math>, but their labels are negative (positive).
Such <em class="ltx_emph ltx_font_italic" id="S4.SS2.p1.5.1">noisy samples</em> hamper the model generalization in price movement forecasting <cite class="ltx_cite ltx_citemacro_cite">Li <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib17" title="">2018</a>)</cite>.
Thus, LARA should be robust to noisy labels of potentially profitable samples, and we adopt the label refurbishment technique introduced by <cite class="ltx_cite ltx_citemacro_cite">Song <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib25" title="">2022</a>)</cite> to learn a noise-robust prediction model.
Formally, given the training data <math alttext="{\bm{X}}\in\mathbb{R}^{N\times d}" class="ltx_Math" display="inline" id="S4.SS2.p1.2.m2.1"><semantics id="S4.SS2.p1.2.m2.1a"><mrow id="S4.SS2.p1.2.m2.1.1" xref="S4.SS2.p1.2.m2.1.1.cmml"><mi id="S4.SS2.p1.2.m2.1.1.2" xref="S4.SS2.p1.2.m2.1.1.2.cmml">𝑿</mi><mo id="S4.SS2.p1.2.m2.1.1.1" xref="S4.SS2.p1.2.m2.1.1.1.cmml">∈</mo><msup id="S4.SS2.p1.2.m2.1.1.3" xref="S4.SS2.p1.2.m2.1.1.3.cmml"><mi id="S4.SS2.p1.2.m2.*******" xref="S4.SS2.p1.2.m2.*******.cmml">ℝ</mi><mrow id="S4.SS2.p1.2.m2.*******" xref="S4.SS2.p1.2.m2.*******.cmml"><mi id="S4.SS2.p1.2.m2.*******.2" xref="S4.SS2.p1.2.m2.*******.2.cmml">N</mi><mo id="S4.SS2.p1.2.m2.*******.1" lspace="0.222em" rspace="0.222em" xref="S4.SS2.p1.2.m2.*******.1.cmml">×</mo><mi id="S4.SS2.p1.2.m2.*******.3" xref="S4.SS2.p1.2.m2.*******.3.cmml">d</mi></mrow></msup></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p1.2.m2.1b"><apply id="S4.SS2.p1.2.m2.1.1.cmml" xref="S4.SS2.p1.2.m2.1.1"><in id="S4.SS2.p1.2.m2.1.1.1.cmml" xref="S4.SS2.p1.2.m2.1.1.1"></in><ci id="S4.SS2.p1.2.m2.1.1.2.cmml" xref="S4.SS2.p1.2.m2.1.1.2">𝑿</ci><apply id="S4.SS2.p1.2.m2.1.1.3.cmml" xref="S4.SS2.p1.2.m2.1.1.3"><csymbol cd="ambiguous" id="S4.SS2.p1.2.m2.*******.cmml" xref="S4.SS2.p1.2.m2.1.1.3">superscript</csymbol><ci id="S4.SS2.p1.2.m2.*******.cmml" xref="S4.SS2.p1.2.m2.*******">ℝ</ci><apply id="S4.SS2.p1.2.m2.*******.cmml" xref="S4.SS2.p1.2.m2.*******"><times id="S4.SS2.p1.2.m2.*******.1.cmml" xref="S4.SS2.p1.2.m2.*******.1"></times><ci id="S4.SS2.p1.2.m2.*******.2.cmml" xref="S4.SS2.p1.2.m2.*******.2">𝑁</ci><ci id="S4.SS2.p1.2.m2.*******.3.cmml" xref="S4.SS2.p1.2.m2.*******.3">𝑑</ci></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p1.2.m2.1c">{\bm{X}}\in\mathbb{R}^{N\times d}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p1.2.m2.1d">bold_italic_X ∈ blackboard_R start_POSTSUPERSCRIPT italic_N × italic_d end_POSTSUPERSCRIPT</annotation></semantics></math>, our method runs in an iterative manner to obtain adaptively refined labels <math alttext="\{{\bm{y}}^{k}\}_{0\leq k\leq K}" class="ltx_Math" display="inline" id="S4.SS2.p1.3.m3.1"><semantics id="S4.SS2.p1.3.m3.1a"><msub id="S4.SS2.p1.3.m3.1.1" xref="S4.SS2.p1.3.m3.1.1.cmml"><mrow id="S4.SS2.p1.3.m3.*******" xref="S4.SS2.p1.3.m3.*******.cmml"><mo id="S4.SS2.p1.3.m3.*******.2" stretchy="false" xref="S4.SS2.p1.3.m3.*******.cmml">{</mo><msup id="S4.SS2.p1.3.m3.*******.1" xref="S4.SS2.p1.3.m3.*******.1.cmml"><mi id="S4.SS2.p1.3.m3.*******.1.2" xref="S4.SS2.p1.3.m3.*******.1.2.cmml">𝒚</mi><mi id="S4.SS2.p1.3.m3.*******.1.3" xref="S4.SS2.p1.3.m3.*******.1.3.cmml">k</mi></msup><mo id="S4.SS2.p1.3.m3.*******.3" stretchy="false" xref="S4.SS2.p1.3.m3.*******.cmml">}</mo></mrow><mrow id="S4.SS2.p1.3.m3.1.1.3" xref="S4.SS2.p1.3.m3.1.1.3.cmml"><mn id="S4.SS2.p1.3.m3.*******" xref="S4.SS2.p1.3.m3.*******.cmml">0</mn><mo id="S4.SS2.p1.3.m3.*******" xref="S4.SS2.p1.3.m3.*******.cmml">≤</mo><mi id="S4.SS2.p1.3.m3.*******" xref="S4.SS2.p1.3.m3.*******.cmml">k</mi><mo id="S4.SS2.p1.3.m3.*******" xref="S4.SS2.p1.3.m3.*******.cmml">≤</mo><mi id="S4.SS2.p1.3.m3.*******" xref="S4.SS2.p1.3.m3.*******.cmml">K</mi></mrow></msub><annotation-xml encoding="MathML-Content" id="S4.SS2.p1.3.m3.1b"><apply id="S4.SS2.p1.3.m3.1.1.cmml" xref="S4.SS2.p1.3.m3.1.1"><csymbol cd="ambiguous" id="S4.SS2.p1.3.m3.1.1.2.cmml" xref="S4.SS2.p1.3.m3.1.1">subscript</csymbol><set id="S4.SS2.p1.3.m3.*******.cmml" xref="S4.SS2.p1.3.m3.*******"><apply id="S4.SS2.p1.3.m3.*******.1.cmml" xref="S4.SS2.p1.3.m3.*******.1"><csymbol cd="ambiguous" id="S4.SS2.p1.3.m3.*******.1.1.cmml" xref="S4.SS2.p1.3.m3.*******.1">superscript</csymbol><ci id="S4.SS2.p1.3.m3.*******.1.2.cmml" xref="S4.SS2.p1.3.m3.*******.1.2">𝒚</ci><ci id="S4.SS2.p1.3.m3.*******.1.3.cmml" xref="S4.SS2.p1.3.m3.*******.1.3">𝑘</ci></apply></set><apply id="S4.SS2.p1.3.m3.1.1.3.cmml" xref="S4.SS2.p1.3.m3.1.1.3"><and id="S4.SS2.p1.3.m3.1.1.3a.cmml" xref="S4.SS2.p1.3.m3.1.1.3"></and><apply id="S4.SS2.p1.3.m3.1.1.3b.cmml" xref="S4.SS2.p1.3.m3.1.1.3"><leq id="S4.SS2.p1.3.m3.*******.cmml" xref="S4.SS2.p1.3.m3.*******"></leq><cn id="S4.SS2.p1.3.m3.*******.cmml" type="integer" xref="S4.SS2.p1.3.m3.*******">0</cn><ci id="S4.SS2.p1.3.m3.*******.cmml" xref="S4.SS2.p1.3.m3.*******">𝑘</ci></apply><apply id="S4.SS2.p1.3.m3.1.1.3c.cmml" xref="S4.SS2.p1.3.m3.1.1.3"><leq id="S4.SS2.p1.3.m3.*******.cmml" xref="S4.SS2.p1.3.m3.*******"></leq><share href="https://arxiv.org/html/2107.11972v4#S4.SS2.p1.3.m3.*******.cmml" id="S4.SS2.p1.3.m3.1.1.3d.cmml" xref="S4.SS2.p1.3.m3.1.1.3"></share><ci id="S4.SS2.p1.3.m3.*******.cmml" xref="S4.SS2.p1.3.m3.*******">𝐾</ci></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p1.3.m3.1c">\{{\bm{y}}^{k}\}_{0\leq k\leq K}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p1.3.m3.1d">{ bold_italic_y start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT } start_POSTSUBSCRIPT 0 ≤ italic_k ≤ italic_K end_POSTSUBSCRIPT</annotation></semantics></math>, where <math alttext="N" class="ltx_Math" display="inline" id="S4.SS2.p1.4.m4.1"><semantics id="S4.SS2.p1.4.m4.1a"><mi id="S4.SS2.p1.4.m4.1.1" xref="S4.SS2.p1.4.m4.1.1.cmml">N</mi><annotation-xml encoding="MathML-Content" id="S4.SS2.p1.4.m4.1b"><ci id="S4.SS2.p1.4.m4.1.1.cmml" xref="S4.SS2.p1.4.m4.1.1">𝑁</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p1.4.m4.1c">N</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p1.4.m4.1d">italic_N</annotation></semantics></math> is the number of training data and <math alttext="K" class="ltx_Math" display="inline" id="S4.SS2.p1.5.m5.1"><semantics id="S4.SS2.p1.5.m5.1a"><mi id="S4.SS2.p1.5.m5.1.1" xref="S4.SS2.p1.5.m5.1.1.cmml">K</mi><annotation-xml encoding="MathML-Content" id="S4.SS2.p1.5.m5.1b"><ci id="S4.SS2.p1.5.m5.1.1.cmml" xref="S4.SS2.p1.5.m5.1.1">𝐾</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p1.5.m5.1c">K</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p1.5.m5.1d">italic_K</annotation></semantics></math> is the number of rounds in iterative refinement labeling.</p>
</div>
<div class="ltx_para" id="S4.SS2.p2">
<p class="ltx_p" id="S4.SS2.p2.8">We denote the trained predictor at the <math alttext="k" class="ltx_Math" display="inline" id="S4.SS2.p2.1.m1.1"><semantics id="S4.SS2.p2.1.m1.1a"><mi id="S4.SS2.p2.1.m1.1.1" xref="S4.SS2.p2.1.m1.1.1.cmml">k</mi><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.1.m1.1b"><ci id="S4.SS2.p2.1.m1.1.1.cmml" xref="S4.SS2.p2.1.m1.1.1">𝑘</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.1.m1.1c">k</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.1.m1.1d">italic_k</annotation></semantics></math>-th round and the current prediction of the sample <math alttext="j" class="ltx_Math" display="inline" id="S4.SS2.p2.2.m2.1"><semantics id="S4.SS2.p2.2.m2.1a"><mi id="S4.SS2.p2.2.m2.1.1" xref="S4.SS2.p2.2.m2.1.1.cmml">j</mi><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.2.m2.1b"><ci id="S4.SS2.p2.2.m2.1.1.cmml" xref="S4.SS2.p2.2.m2.1.1">𝑗</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.2.m2.1c">j</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.2.m2.1d">italic_j</annotation></semantics></math> at the <math alttext="k" class="ltx_Math" display="inline" id="S4.SS2.p2.3.m3.1"><semantics id="S4.SS2.p2.3.m3.1a"><mi id="S4.SS2.p2.3.m3.1.1" xref="S4.SS2.p2.3.m3.1.1.cmml">k</mi><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.3.m3.1b"><ci id="S4.SS2.p2.3.m3.1.1.cmml" xref="S4.SS2.p2.3.m3.1.1">𝑘</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.3.m3.1c">k</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.3.m3.1d">italic_k</annotation></semantics></math>-th round as <math alttext="\{f_{\theta^{k}}\}_{0\leq k\leq K}" class="ltx_Math" display="inline" id="S4.SS2.p2.4.m4.1"><semantics id="S4.SS2.p2.4.m4.1a"><msub id="S4.SS2.p2.4.m4.1.1" xref="S4.SS2.p2.4.m4.1.1.cmml"><mrow id="S4.SS2.p2.4.m4.*******" xref="S4.SS2.p2.4.m4.*******.cmml"><mo id="S4.SS2.p2.4.m4.*******.2" stretchy="false" xref="S4.SS2.p2.4.m4.*******.cmml">{</mo><msub id="S4.SS2.p2.4.m4.*******.1" xref="S4.SS2.p2.4.m4.*******.1.cmml"><mi id="S4.SS2.p2.4.m4.*******.1.2" xref="S4.SS2.p2.4.m4.*******.1.2.cmml">f</mi><msup id="S4.SS2.p2.4.m4.*******.1.3" xref="S4.SS2.p2.4.m4.*******.1.3.cmml"><mi id="S4.SS2.p2.4.m4.1.1.1.*******" xref="S4.SS2.p2.4.m4.1.1.1.*******.cmml">θ</mi><mi id="S4.SS2.p2.4.m4.1.1.1.*******" xref="S4.SS2.p2.4.m4.1.1.1.*******.cmml">k</mi></msup></msub><mo id="S4.SS2.p2.4.m4.*******.3" stretchy="false" xref="S4.SS2.p2.4.m4.*******.cmml">}</mo></mrow><mrow id="S4.SS2.p2.4.m4.1.1.3" xref="S4.SS2.p2.4.m4.1.1.3.cmml"><mn id="S4.SS2.p2.4.m4.*******" xref="S4.SS2.p2.4.m4.*******.cmml">0</mn><mo id="S4.SS2.p2.4.m4.*******" xref="S4.SS2.p2.4.m4.*******.cmml">≤</mo><mi id="S4.SS2.p2.4.m4.*******" xref="S4.SS2.p2.4.m4.*******.cmml">k</mi><mo id="S4.SS2.p2.4.m4.*******" xref="S4.SS2.p2.4.m4.*******.cmml">≤</mo><mi id="S4.SS2.p2.4.m4.*******" xref="S4.SS2.p2.4.m4.*******.cmml">K</mi></mrow></msub><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.4.m4.1b"><apply id="S4.SS2.p2.4.m4.1.1.cmml" xref="S4.SS2.p2.4.m4.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.4.m4.1.1.2.cmml" xref="S4.SS2.p2.4.m4.1.1">subscript</csymbol><set id="S4.SS2.p2.4.m4.*******.cmml" xref="S4.SS2.p2.4.m4.*******"><apply id="S4.SS2.p2.4.m4.*******.1.cmml" xref="S4.SS2.p2.4.m4.*******.1"><csymbol cd="ambiguous" id="S4.SS2.p2.4.m4.*******.1.1.cmml" xref="S4.SS2.p2.4.m4.*******.1">subscript</csymbol><ci id="S4.SS2.p2.4.m4.*******.1.2.cmml" xref="S4.SS2.p2.4.m4.*******.1.2">𝑓</ci><apply id="S4.SS2.p2.4.m4.*******.1.3.cmml" xref="S4.SS2.p2.4.m4.*******.1.3"><csymbol cd="ambiguous" id="S4.SS2.p2.4.m4.1.1.1.*******.cmml" xref="S4.SS2.p2.4.m4.*******.1.3">superscript</csymbol><ci id="S4.SS2.p2.4.m4.1.1.1.*******.cmml" xref="S4.SS2.p2.4.m4.1.1.1.*******">𝜃</ci><ci id="S4.SS2.p2.4.m4.1.1.1.*******.cmml" xref="S4.SS2.p2.4.m4.1.1.1.*******">𝑘</ci></apply></apply></set><apply id="S4.SS2.p2.4.m4.1.1.3.cmml" xref="S4.SS2.p2.4.m4.1.1.3"><and id="S4.SS2.p2.4.m4.1.1.3a.cmml" xref="S4.SS2.p2.4.m4.1.1.3"></and><apply id="S4.SS2.p2.4.m4.1.1.3b.cmml" xref="S4.SS2.p2.4.m4.1.1.3"><leq id="S4.SS2.p2.4.m4.*******.cmml" xref="S4.SS2.p2.4.m4.*******"></leq><cn id="S4.SS2.p2.4.m4.*******.cmml" type="integer" xref="S4.SS2.p2.4.m4.*******">0</cn><ci id="S4.SS2.p2.4.m4.*******.cmml" xref="S4.SS2.p2.4.m4.*******">𝑘</ci></apply><apply id="S4.SS2.p2.4.m4.1.1.3c.cmml" xref="S4.SS2.p2.4.m4.1.1.3"><leq id="S4.SS2.p2.4.m4.*******.cmml" xref="S4.SS2.p2.4.m4.*******"></leq><share href="https://arxiv.org/html/2107.11972v4#S4.SS2.p2.4.m4.*******.cmml" id="S4.SS2.p2.4.m4.1.1.3d.cmml" xref="S4.SS2.p2.4.m4.1.1.3"></share><ci id="S4.SS2.p2.4.m4.*******.cmml" xref="S4.SS2.p2.4.m4.*******">𝐾</ci></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.4.m4.1c">\{f_{\theta^{k}}\}_{0\leq k\leq K}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.4.m4.1d">{ italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT end_POSTSUBSCRIPT } start_POSTSUBSCRIPT 0 ≤ italic_k ≤ italic_K end_POSTSUBSCRIPT</annotation></semantics></math> and <math alttext="f_{\theta^{k}}({\bm{x}}_{j})" class="ltx_Math" display="inline" id="S4.SS2.p2.5.m5.1"><semantics id="S4.SS2.p2.5.m5.1a"><mrow id="S4.SS2.p2.5.m5.1.1" xref="S4.SS2.p2.5.m5.1.1.cmml"><msub id="S4.SS2.p2.5.m5.1.1.3" xref="S4.SS2.p2.5.m5.1.1.3.cmml"><mi id="S4.SS2.p2.5.m5.*******" xref="S4.SS2.p2.5.m5.*******.cmml">f</mi><msup id="S4.SS2.p2.5.m5.*******" xref="S4.SS2.p2.5.m5.*******.cmml"><mi id="S4.SS2.p2.5.m5.*******.2" xref="S4.SS2.p2.5.m5.*******.2.cmml">θ</mi><mi id="S4.SS2.p2.5.m5.*******.3" xref="S4.SS2.p2.5.m5.*******.3.cmml">k</mi></msup></msub><mo id="S4.SS2.p2.5.m5.1.1.2" xref="S4.SS2.p2.5.m5.1.1.2.cmml">⁢</mo><mrow id="S4.SS2.p2.5.m5.*******" xref="S4.SS2.p2.5.m5.*******.1.cmml"><mo id="S4.SS2.p2.5.m5.*******.2" stretchy="false" xref="S4.SS2.p2.5.m5.*******.1.cmml">(</mo><msub id="S4.SS2.p2.5.m5.*******.1" xref="S4.SS2.p2.5.m5.*******.1.cmml"><mi id="S4.SS2.p2.5.m5.*******.1.2" xref="S4.SS2.p2.5.m5.*******.1.2.cmml">𝒙</mi><mi id="S4.SS2.p2.5.m5.*******.1.3" xref="S4.SS2.p2.5.m5.*******.1.3.cmml">j</mi></msub><mo id="S4.SS2.p2.5.m5.*******.3" stretchy="false" xref="S4.SS2.p2.5.m5.*******.1.cmml">)</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.5.m5.1b"><apply id="S4.SS2.p2.5.m5.1.1.cmml" xref="S4.SS2.p2.5.m5.1.1"><times id="S4.SS2.p2.5.m5.1.1.2.cmml" xref="S4.SS2.p2.5.m5.1.1.2"></times><apply id="S4.SS2.p2.5.m5.1.1.3.cmml" xref="S4.SS2.p2.5.m5.1.1.3"><csymbol cd="ambiguous" id="S4.SS2.p2.5.m5.*******.cmml" xref="S4.SS2.p2.5.m5.1.1.3">subscript</csymbol><ci id="S4.SS2.p2.5.m5.*******.cmml" xref="S4.SS2.p2.5.m5.*******">𝑓</ci><apply id="S4.SS2.p2.5.m5.*******.cmml" xref="S4.SS2.p2.5.m5.*******"><csymbol cd="ambiguous" id="S4.SS2.p2.5.m5.*******.1.cmml" xref="S4.SS2.p2.5.m5.*******">superscript</csymbol><ci id="S4.SS2.p2.5.m5.*******.2.cmml" xref="S4.SS2.p2.5.m5.*******.2">𝜃</ci><ci id="S4.SS2.p2.5.m5.*******.3.cmml" xref="S4.SS2.p2.5.m5.*******.3">𝑘</ci></apply></apply><apply id="S4.SS2.p2.5.m5.*******.1.cmml" xref="S4.SS2.p2.5.m5.*******"><csymbol cd="ambiguous" id="S4.SS2.p2.5.m5.*******.1.1.cmml" xref="S4.SS2.p2.5.m5.*******">subscript</csymbol><ci id="S4.SS2.p2.5.m5.*******.1.2.cmml" xref="S4.SS2.p2.5.m5.*******.1.2">𝒙</ci><ci id="S4.SS2.p2.5.m5.*******.1.3.cmml" xref="S4.SS2.p2.5.m5.*******.1.3">𝑗</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.5.m5.1c">f_{\theta^{k}}({\bm{x}}_{j})</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.5.m5.1d">italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT end_POSTSUBSCRIPT ( bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT )</annotation></semantics></math>, respectively.
The refurbished label <math alttext="y_{j}^{k+1}" class="ltx_Math" display="inline" id="S4.SS2.p2.6.m6.1"><semantics id="S4.SS2.p2.6.m6.1a"><msubsup id="S4.SS2.p2.6.m6.1.1" xref="S4.SS2.p2.6.m6.1.1.cmml"><mi id="S4.SS2.p2.6.m6.*******" xref="S4.SS2.p2.6.m6.*******.cmml">y</mi><mi id="S4.SS2.p2.6.m6.*******" xref="S4.SS2.p2.6.m6.*******.cmml">j</mi><mrow id="S4.SS2.p2.6.m6.1.1.3" xref="S4.SS2.p2.6.m6.1.1.3.cmml"><mi id="S4.SS2.p2.6.m6.*******" xref="S4.SS2.p2.6.m6.*******.cmml">k</mi><mo id="S4.SS2.p2.6.m6.*******" xref="S4.SS2.p2.6.m6.*******.cmml">+</mo><mn id="S4.SS2.p2.6.m6.*******" xref="S4.SS2.p2.6.m6.*******.cmml">1</mn></mrow></msubsup><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.6.m6.1b"><apply id="S4.SS2.p2.6.m6.1.1.cmml" xref="S4.SS2.p2.6.m6.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.6.m6.1.1.1.cmml" xref="S4.SS2.p2.6.m6.1.1">superscript</csymbol><apply id="S4.SS2.p2.6.m6.1.1.2.cmml" xref="S4.SS2.p2.6.m6.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.6.m6.*******.cmml" xref="S4.SS2.p2.6.m6.1.1">subscript</csymbol><ci id="S4.SS2.p2.6.m6.*******.cmml" xref="S4.SS2.p2.6.m6.*******">𝑦</ci><ci id="S4.SS2.p2.6.m6.*******.cmml" xref="S4.SS2.p2.6.m6.*******">𝑗</ci></apply><apply id="S4.SS2.p2.6.m6.1.1.3.cmml" xref="S4.SS2.p2.6.m6.1.1.3"><plus id="S4.SS2.p2.6.m6.*******.cmml" xref="S4.SS2.p2.6.m6.*******"></plus><ci id="S4.SS2.p2.6.m6.*******.cmml" xref="S4.SS2.p2.6.m6.*******">𝑘</ci><cn id="S4.SS2.p2.6.m6.*******.cmml" type="integer" xref="S4.SS2.p2.6.m6.*******">1</cn></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.6.m6.1c">y_{j}^{k+1}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.6.m6.1d">italic_y start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k + 1 end_POSTSUPERSCRIPT</annotation></semantics></math> can be obtained by a convex combination of the current label <math alttext="y_{j}^{k}" class="ltx_Math" display="inline" id="S4.SS2.p2.7.m7.1"><semantics id="S4.SS2.p2.7.m7.1a"><msubsup id="S4.SS2.p2.7.m7.1.1" xref="S4.SS2.p2.7.m7.1.1.cmml"><mi id="S4.SS2.p2.7.m7.*******" xref="S4.SS2.p2.7.m7.*******.cmml">y</mi><mi id="S4.SS2.p2.7.m7.*******" xref="S4.SS2.p2.7.m7.*******.cmml">j</mi><mi id="S4.SS2.p2.7.m7.1.1.3" xref="S4.SS2.p2.7.m7.1.1.3.cmml">k</mi></msubsup><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.7.m7.1b"><apply id="S4.SS2.p2.7.m7.1.1.cmml" xref="S4.SS2.p2.7.m7.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.7.m7.1.1.1.cmml" xref="S4.SS2.p2.7.m7.1.1">superscript</csymbol><apply id="S4.SS2.p2.7.m7.1.1.2.cmml" xref="S4.SS2.p2.7.m7.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.7.m7.*******.cmml" xref="S4.SS2.p2.7.m7.1.1">subscript</csymbol><ci id="S4.SS2.p2.7.m7.*******.cmml" xref="S4.SS2.p2.7.m7.*******">𝑦</ci><ci id="S4.SS2.p2.7.m7.*******.cmml" xref="S4.SS2.p2.7.m7.*******">𝑗</ci></apply><ci id="S4.SS2.p2.7.m7.1.1.3.cmml" xref="S4.SS2.p2.7.m7.1.1.3">𝑘</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.7.m7.1c">y_{j}^{k}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.7.m7.1d">italic_y start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT</annotation></semantics></math> and the associated prediction <math alttext="f_{\theta^{k}}({\bm{x}}_{j})" class="ltx_Math" display="inline" id="S4.SS2.p2.8.m8.1"><semantics id="S4.SS2.p2.8.m8.1a"><mrow id="S4.SS2.p2.8.m8.1.1" xref="S4.SS2.p2.8.m8.1.1.cmml"><msub id="S4.SS2.p2.8.m8.1.1.3" xref="S4.SS2.p2.8.m8.1.1.3.cmml"><mi id="S4.SS2.p2.8.m8.*******" xref="S4.SS2.p2.8.m8.*******.cmml">f</mi><msup id="S4.SS2.p2.8.m8.*******" xref="S4.SS2.p2.8.m8.*******.cmml"><mi id="S4.SS2.p2.8.m8.*******.2" xref="S4.SS2.p2.8.m8.*******.2.cmml">θ</mi><mi id="S4.SS2.p2.8.m8.*******.3" xref="S4.SS2.p2.8.m8.*******.3.cmml">k</mi></msup></msub><mo id="S4.SS2.p2.8.m8.1.1.2" xref="S4.SS2.p2.8.m8.1.1.2.cmml">⁢</mo><mrow id="S4.SS2.p2.8.m8.*******" xref="S4.SS2.p2.8.m8.*******.1.cmml"><mo id="S4.SS2.p2.8.m8.*******.2" stretchy="false" xref="S4.SS2.p2.8.m8.*******.1.cmml">(</mo><msub id="S4.SS2.p2.8.m8.*******.1" xref="S4.SS2.p2.8.m8.*******.1.cmml"><mi id="S4.SS2.p2.8.m8.*******.1.2" xref="S4.SS2.p2.8.m8.*******.1.2.cmml">𝒙</mi><mi id="S4.SS2.p2.8.m8.*******.1.3" xref="S4.SS2.p2.8.m8.*******.1.3.cmml">j</mi></msub><mo id="S4.SS2.p2.8.m8.*******.3" stretchy="false" xref="S4.SS2.p2.8.m8.*******.1.cmml">)</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.8.m8.1b"><apply id="S4.SS2.p2.8.m8.1.1.cmml" xref="S4.SS2.p2.8.m8.1.1"><times id="S4.SS2.p2.8.m8.1.1.2.cmml" xref="S4.SS2.p2.8.m8.1.1.2"></times><apply id="S4.SS2.p2.8.m8.1.1.3.cmml" xref="S4.SS2.p2.8.m8.1.1.3"><csymbol cd="ambiguous" id="S4.SS2.p2.8.m8.*******.cmml" xref="S4.SS2.p2.8.m8.1.1.3">subscript</csymbol><ci id="S4.SS2.p2.8.m8.*******.cmml" xref="S4.SS2.p2.8.m8.*******">𝑓</ci><apply id="S4.SS2.p2.8.m8.*******.cmml" xref="S4.SS2.p2.8.m8.*******"><csymbol cd="ambiguous" id="S4.SS2.p2.8.m8.*******.1.cmml" xref="S4.SS2.p2.8.m8.*******">superscript</csymbol><ci id="S4.SS2.p2.8.m8.*******.2.cmml" xref="S4.SS2.p2.8.m8.*******.2">𝜃</ci><ci id="S4.SS2.p2.8.m8.*******.3.cmml" xref="S4.SS2.p2.8.m8.*******.3">𝑘</ci></apply></apply><apply id="S4.SS2.p2.8.m8.*******.1.cmml" xref="S4.SS2.p2.8.m8.*******"><csymbol cd="ambiguous" id="S4.SS2.p2.8.m8.*******.1.1.cmml" xref="S4.SS2.p2.8.m8.*******">subscript</csymbol><ci id="S4.SS2.p2.8.m8.*******.1.2.cmml" xref="S4.SS2.p2.8.m8.*******.1.2">𝒙</ci><ci id="S4.SS2.p2.8.m8.*******.1.3.cmml" xref="S4.SS2.p2.8.m8.*******.1.3">𝑗</ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.8.m8.1c">f_{\theta^{k}}({\bm{x}}_{j})</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.8.m8.1d">italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT end_POSTSUBSCRIPT ( bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT )</annotation></semantics></math>:</p>
<table class="ltx_equation ltx_eqn_table" id="S4.E6">
<tbody><tr class="ltx_equation ltx_eqn_row ltx_align_baseline">
<td class="ltx_eqn_cell ltx_eqn_center_padleft"></td>
<td class="ltx_eqn_cell ltx_align_center"><math alttext="y_{j}^{k+1}=\alpha_{j}^{k}y_{j}^{k}+(1-\alpha_{j}^{k})f_{\theta^{k}}({\bm{x}}_%
{j})," class="ltx_Math" display="block" id="S4.E6.m1.1"><semantics id="S4.E6.m1.1a"><mrow id="S4.E6.m*******" xref="S4.E6.m*******.1.cmml"><mrow id="S4.E6.m*******.1" xref="S4.E6.m*******.1.cmml"><msubsup id="S4.E6.m*******.1.4" xref="S4.E6.m*******.1.4.cmml"><mi id="S4.E6.m*******.1.4.2.2" xref="S4.E6.m*******.1.4.2.2.cmml">y</mi><mi id="S4.E6.m*******.1.4.2.3" xref="S4.E6.m*******.1.4.2.3.cmml">j</mi><mrow id="S4.E6.m*******.1.4.3" xref="S4.E6.m*******.1.4.3.cmml"><mi id="S4.E6.m*******.1.4.3.2" xref="S4.E6.m*******.1.4.3.2.cmml">k</mi><mo id="S4.E6.m*******.1.4.3.1" xref="S4.E6.m*******.1.4.3.1.cmml">+</mo><mn id="S4.E6.m*******.1.4.3.3" xref="S4.E6.m*******.1.4.3.3.cmml">1</mn></mrow></msubsup><mo id="S4.E6.m*******.1.3" xref="S4.E6.m*******.1.3.cmml">=</mo><mrow id="S4.E6.m*******.1.2" xref="S4.E6.m*******.1.2.cmml"><mrow id="S4.E6.m*******.1.2.4" xref="S4.E6.m*******.1.2.4.cmml"><msubsup id="S4.E6.m*******.1.2.4.2" xref="S4.E6.m*******.1.2.4.2.cmml"><mi id="S4.E6.m*******.1.*******.2" xref="S4.E6.m*******.1.*******.2.cmml">α</mi><mi id="S4.E6.m*******.1.*******.3" xref="S4.E6.m*******.1.*******.3.cmml">j</mi><mi id="S4.E6.m*******.1.*******" xref="S4.E6.m*******.1.*******.cmml">k</mi></msubsup><mo id="S4.E6.m*******.1.2.4.1" xref="S4.E6.m*******.1.2.4.1.cmml">⁢</mo><msubsup id="S4.E6.m*******.1.2.4.3" xref="S4.E6.m*******.1.2.4.3.cmml"><mi id="S4.E6.m*******.1.*******.2" xref="S4.E6.m*******.1.*******.2.cmml">y</mi><mi id="S4.E6.m*******.1.*******.3" xref="S4.E6.m*******.1.*******.3.cmml">j</mi><mi id="S4.E6.m*******.1.*******" xref="S4.E6.m*******.1.*******.cmml">k</mi></msubsup></mrow><mo id="S4.E6.m*******.1.2.3" xref="S4.E6.m*******.1.2.3.cmml">+</mo><mrow id="S4.E6.m*******.1.2.2" xref="S4.E6.m*******.1.2.2.cmml"><mrow id="S4.E6.m*******.*******.1" xref="S4.E6.m*******.*******.1.1.cmml"><mo id="S4.E6.m*******.*******.1.2" stretchy="false" xref="S4.E6.m*******.*******.1.1.cmml">(</mo><mrow id="S4.E6.m*******.*******.1.1" xref="S4.E6.m*******.*******.1.1.cmml"><mn id="S4.E6.m*******.*******.1.1.2" xref="S4.E6.m*******.*******.1.1.2.cmml">1</mn><mo id="S4.E6.m*******.*******.1.1.1" xref="S4.E6.m*******.*******.1.1.1.cmml">−</mo><msubsup id="S4.E6.m*******.*******.1.1.3" xref="S4.E6.m*******.*******.1.1.3.cmml"><mi id="S4.E6.m*******.*******.*******.2" xref="S4.E6.m*******.*******.*******.2.cmml">α</mi><mi id="S4.E6.m*******.*******.*******.3" xref="S4.E6.m*******.*******.*******.3.cmml">j</mi><mi id="S4.E6.m*******.*******.*******" xref="S4.E6.m*******.*******.*******.cmml">k</mi></msubsup></mrow><mo id="S4.E6.m*******.*******.1.3" stretchy="false" xref="S4.E6.m*******.*******.1.1.cmml">)</mo></mrow><mo id="S4.E6.m*******.1.2.2.3" xref="S4.E6.m*******.1.2.2.3.cmml">⁢</mo><msub id="S4.E6.m*******.1.2.2.4" xref="S4.E6.m*******.1.2.2.4.cmml"><mi id="S4.E6.m*******.1.*******" xref="S4.E6.m*******.1.*******.cmml">f</mi><msup id="S4.E6.m*******.1.*******" xref="S4.E6.m*******.1.*******.cmml"><mi id="S4.E6.m*******.1.2.*******" xref="S4.E6.m*******.1.2.*******.cmml">θ</mi><mi id="S4.E6.m*******.1.2.*******" xref="S4.E6.m*******.1.2.*******.cmml">k</mi></msup></msub><mo id="S4.E6.m*******.1.2.2.3a" xref="S4.E6.m*******.1.2.2.3.cmml">⁢</mo><mrow id="S4.E6.m*******.1.*******" xref="S4.E6.m*******.1.2.*******.cmml"><mo id="S4.E6.m*******.1.*******.2" stretchy="false" xref="S4.E6.m*******.1.2.*******.cmml">(</mo><msub id="S4.E6.m*******.1.2.*******" xref="S4.E6.m*******.1.2.*******.cmml"><mi id="S4.E6.m*******.1.2.*******.2" xref="S4.E6.m*******.1.2.*******.2.cmml">𝒙</mi><mi id="S4.E6.m*******.1.2.*******.3" xref="S4.E6.m*******.1.2.*******.3.cmml">j</mi></msub><mo id="S4.E6.m*******.1.*******.3" stretchy="false" xref="S4.E6.m*******.1.2.*******.cmml">)</mo></mrow></mrow></mrow></mrow><mo id="S4.E6.m*******.2" xref="S4.E6.m*******.1.cmml">,</mo></mrow><annotation-xml encoding="MathML-Content" id="S4.E6.m1.1b"><apply id="S4.E6.m*******.1.cmml" xref="S4.E6.m*******"><eq id="S4.E6.m*******.1.3.cmml" xref="S4.E6.m*******.1.3"></eq><apply id="S4.E6.m*******.1.4.cmml" xref="S4.E6.m*******.1.4"><csymbol cd="ambiguous" id="S4.E6.m*******.1.4.1.cmml" xref="S4.E6.m*******.1.4">superscript</csymbol><apply id="S4.E6.m*******.1.4.2.cmml" xref="S4.E6.m*******.1.4"><csymbol cd="ambiguous" id="S4.E6.m*******.1.4.2.1.cmml" xref="S4.E6.m*******.1.4">subscript</csymbol><ci id="S4.E6.m*******.1.4.2.2.cmml" xref="S4.E6.m*******.1.4.2.2">𝑦</ci><ci id="S4.E6.m*******.1.4.2.3.cmml" xref="S4.E6.m*******.1.4.2.3">𝑗</ci></apply><apply id="S4.E6.m*******.1.4.3.cmml" xref="S4.E6.m*******.1.4.3"><plus id="S4.E6.m*******.1.4.3.1.cmml" xref="S4.E6.m*******.1.4.3.1"></plus><ci id="S4.E6.m*******.1.4.3.2.cmml" xref="S4.E6.m*******.1.4.3.2">𝑘</ci><cn id="S4.E6.m*******.1.4.3.3.cmml" type="integer" xref="S4.E6.m*******.1.4.3.3">1</cn></apply></apply><apply id="S4.E6.m*******.1.2.cmml" xref="S4.E6.m*******.1.2"><plus id="S4.E6.m*******.1.2.3.cmml" xref="S4.E6.m*******.1.2.3"></plus><apply id="S4.E6.m*******.1.2.4.cmml" xref="S4.E6.m*******.1.2.4"><times id="S4.E6.m*******.1.2.4.1.cmml" xref="S4.E6.m*******.1.2.4.1"></times><apply id="S4.E6.m*******.1.2.4.2.cmml" xref="S4.E6.m*******.1.2.4.2"><csymbol cd="ambiguous" id="S4.E6.m*******.1.*******.cmml" xref="S4.E6.m*******.1.2.4.2">superscript</csymbol><apply id="S4.E6.m*******.1.*******.cmml" xref="S4.E6.m*******.1.2.4.2"><csymbol cd="ambiguous" id="S4.E6.m*******.1.*******.1.cmml" xref="S4.E6.m*******.1.2.4.2">subscript</csymbol><ci id="S4.E6.m*******.1.*******.2.cmml" xref="S4.E6.m*******.1.*******.2">𝛼</ci><ci id="S4.E6.m*******.1.*******.3.cmml" xref="S4.E6.m*******.1.*******.3">𝑗</ci></apply><ci id="S4.E6.m*******.1.*******.cmml" xref="S4.E6.m*******.1.*******">𝑘</ci></apply><apply id="S4.E6.m*******.1.2.4.3.cmml" xref="S4.E6.m*******.1.2.4.3"><csymbol cd="ambiguous" id="S4.E6.m*******.1.*******.cmml" xref="S4.E6.m*******.1.2.4.3">superscript</csymbol><apply id="S4.E6.m*******.1.*******.cmml" xref="S4.E6.m*******.1.2.4.3"><csymbol cd="ambiguous" id="S4.E6.m*******.1.*******.1.cmml" xref="S4.E6.m*******.1.2.4.3">subscript</csymbol><ci id="S4.E6.m*******.1.*******.2.cmml" xref="S4.E6.m*******.1.*******.2">𝑦</ci><ci id="S4.E6.m*******.1.*******.3.cmml" xref="S4.E6.m*******.1.*******.3">𝑗</ci></apply><ci id="S4.E6.m*******.1.*******.cmml" xref="S4.E6.m*******.1.*******">𝑘</ci></apply></apply><apply id="S4.E6.m*******.1.2.2.cmml" xref="S4.E6.m*******.1.2.2"><times id="S4.E6.m*******.1.2.2.3.cmml" xref="S4.E6.m*******.1.2.2.3"></times><apply id="S4.E6.m*******.*******.1.1.cmml" xref="S4.E6.m*******.*******.1"><minus id="S4.E6.m*******.*******.1.1.1.cmml" xref="S4.E6.m*******.*******.1.1.1"></minus><cn id="S4.E6.m*******.*******.1.1.2.cmml" type="integer" xref="S4.E6.m*******.*******.1.1.2">1</cn><apply id="S4.E6.m*******.*******.1.1.3.cmml" xref="S4.E6.m*******.*******.1.1.3"><csymbol cd="ambiguous" id="S4.E6.m*******.*******.*******.cmml" xref="S4.E6.m*******.*******.1.1.3">superscript</csymbol><apply id="S4.E6.m*******.*******.*******.cmml" xref="S4.E6.m*******.*******.1.1.3"><csymbol cd="ambiguous" id="S4.E6.m*******.*******.*******.1.cmml" xref="S4.E6.m*******.*******.1.1.3">subscript</csymbol><ci id="S4.E6.m*******.*******.*******.2.cmml" xref="S4.E6.m*******.*******.*******.2">𝛼</ci><ci id="S4.E6.m*******.*******.*******.3.cmml" xref="S4.E6.m*******.*******.*******.3">𝑗</ci></apply><ci id="S4.E6.m*******.*******.*******.cmml" xref="S4.E6.m*******.*******.*******">𝑘</ci></apply></apply><apply id="S4.E6.m*******.1.2.2.4.cmml" xref="S4.E6.m*******.1.2.2.4"><csymbol cd="ambiguous" id="S4.E6.m*******.1.*******.cmml" xref="S4.E6.m*******.1.2.2.4">subscript</csymbol><ci id="S4.E6.m*******.1.*******.cmml" xref="S4.E6.m*******.1.*******">𝑓</ci><apply id="S4.E6.m*******.1.*******.cmml" xref="S4.E6.m*******.1.*******"><csymbol cd="ambiguous" id="S4.E6.m*******.1.2.*******.cmml" xref="S4.E6.m*******.1.*******">superscript</csymbol><ci id="S4.E6.m*******.1.2.*******.cmml" xref="S4.E6.m*******.1.2.*******">𝜃</ci><ci id="S4.E6.m*******.1.2.*******.cmml" xref="S4.E6.m*******.1.2.*******">𝑘</ci></apply></apply><apply id="S4.E6.m*******.1.2.*******.cmml" xref="S4.E6.m*******.1.*******"><csymbol cd="ambiguous" id="S4.E6.m*******.1.2.*******.1.cmml" xref="S4.E6.m*******.1.*******">subscript</csymbol><ci id="S4.E6.m*******.1.2.*******.2.cmml" xref="S4.E6.m*******.1.2.*******.2">𝒙</ci><ci id="S4.E6.m*******.1.2.*******.3.cmml" xref="S4.E6.m*******.1.2.*******.3">𝑗</ci></apply></apply></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.E6.m1.1c">y_{j}^{k+1}=\alpha_{j}^{k}y_{j}^{k}+(1-\alpha_{j}^{k})f_{\theta^{k}}({\bm{x}}_%
{j}),</annotation><annotation encoding="application/x-llamapun" id="S4.E6.m1.1d">italic_y start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k + 1 end_POSTSUPERSCRIPT = italic_α start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT italic_y start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT + ( 1 - italic_α start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT ) italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT end_POSTSUBSCRIPT ( bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT ) ,</annotation></semantics></math></td>
<td class="ltx_eqn_cell ltx_eqn_center_padright"></td>
<td class="ltx_eqn_cell ltx_eqn_eqno ltx_align_middle ltx_align_right" rowspan="1"><span class="ltx_tag ltx_tag_equation ltx_align_right">(6)</span></td>
</tr></tbody>
</table>
<p class="ltx_p" id="S4.SS2.p2.15">where <math alttext="\alpha_{j}^{k}\in[0,1]" class="ltx_Math" display="inline" id="S4.SS2.p2.9.m1.2"><semantics id="S4.SS2.p2.9.m1.2a"><mrow id="S4.SS2.p2.9.m1.2.3" xref="S4.SS2.p2.9.m1.2.3.cmml"><msubsup id="S4.SS2.p2.9.m*******" xref="S4.SS2.p2.9.m*******.cmml"><mi id="S4.SS2.p2.9.m*******.2.2" xref="S4.SS2.p2.9.m*******.2.2.cmml">α</mi><mi id="S4.SS2.p2.9.m*******.2.3" xref="S4.SS2.p2.9.m*******.2.3.cmml">j</mi><mi id="S4.SS2.p2.9.m*******.3" xref="S4.SS2.p2.9.m*******.3.cmml">k</mi></msubsup><mo id="S4.SS2.p2.9.m*******" xref="S4.SS2.p2.9.m*******.cmml">∈</mo><mrow id="S4.SS2.p2.9.m1.*******" xref="S4.SS2.p2.9.m1.*******.cmml"><mo id="S4.SS2.p2.9.m1.*******.1" stretchy="false" xref="S4.SS2.p2.9.m1.*******.cmml">[</mo><mn id="S4.SS2.p2.9.m1.1.1" xref="S4.SS2.p2.9.m1.1.1.cmml">0</mn><mo id="S4.SS2.p2.9.m1.2.*******" xref="S4.SS2.p2.9.m1.*******.cmml">,</mo><mn id="S4.SS2.p2.9.m1.2.2" xref="S4.SS2.p2.9.m1.2.2.cmml">1</mn><mo id="S4.SS2.p2.9.m1.*******.3" stretchy="false" xref="S4.SS2.p2.9.m1.*******.cmml">]</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.9.m1.2b"><apply id="S4.SS2.p2.9.m1.2.3.cmml" xref="S4.SS2.p2.9.m1.2.3"><in id="S4.SS2.p2.9.m*******.cmml" xref="S4.SS2.p2.9.m*******"></in><apply id="S4.SS2.p2.9.m*******.cmml" xref="S4.SS2.p2.9.m*******"><csymbol cd="ambiguous" id="S4.SS2.p2.9.m*******.1.cmml" xref="S4.SS2.p2.9.m*******">superscript</csymbol><apply id="S4.SS2.p2.9.m*******.2.cmml" xref="S4.SS2.p2.9.m*******"><csymbol cd="ambiguous" id="S4.SS2.p2.9.m*******.2.1.cmml" xref="S4.SS2.p2.9.m*******">subscript</csymbol><ci id="S4.SS2.p2.9.m*******.2.2.cmml" xref="S4.SS2.p2.9.m*******.2.2">𝛼</ci><ci id="S4.SS2.p2.9.m*******.2.3.cmml" xref="S4.SS2.p2.9.m*******.2.3">𝑗</ci></apply><ci id="S4.SS2.p2.9.m*******.3.cmml" xref="S4.SS2.p2.9.m*******.3">𝑘</ci></apply><interval closure="closed" id="S4.SS2.p2.9.m1.*******.cmml" xref="S4.SS2.p2.9.m1.*******"><cn id="S4.SS2.p2.9.m1.1.1.cmml" type="integer" xref="S4.SS2.p2.9.m1.1.1">0</cn><cn id="S4.SS2.p2.9.m1.2.2.cmml" type="integer" xref="S4.SS2.p2.9.m1.2.2">1</cn></interval></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.9.m1.2c">\alpha_{j}^{k}\in[0,1]</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.9.m1.2d">italic_α start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT ∈ [ 0 , 1 ]</annotation></semantics></math> is the label confidence and <math alttext="y_{j}^{0}" class="ltx_Math" display="inline" id="S4.SS2.p2.10.m2.1"><semantics id="S4.SS2.p2.10.m2.1a"><msubsup id="S4.SS2.p2.10.m2.1.1" xref="S4.SS2.p2.10.m2.1.1.cmml"><mi id="S4.SS2.p2.10.m2.*******" xref="S4.SS2.p2.10.m2.*******.cmml">y</mi><mi id="S4.SS2.p2.10.m2.*******" xref="S4.SS2.p2.10.m2.*******.cmml">j</mi><mn id="S4.SS2.p2.10.m2.1.1.3" xref="S4.SS2.p2.10.m2.1.1.3.cmml">0</mn></msubsup><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.10.m2.1b"><apply id="S4.SS2.p2.10.m2.1.1.cmml" xref="S4.SS2.p2.10.m2.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.10.m2.1.1.1.cmml" xref="S4.SS2.p2.10.m2.1.1">superscript</csymbol><apply id="S4.SS2.p2.10.m2.1.1.2.cmml" xref="S4.SS2.p2.10.m2.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.10.m2.*******.cmml" xref="S4.SS2.p2.10.m2.1.1">subscript</csymbol><ci id="S4.SS2.p2.10.m2.*******.cmml" xref="S4.SS2.p2.10.m2.*******">𝑦</ci><ci id="S4.SS2.p2.10.m2.*******.cmml" xref="S4.SS2.p2.10.m2.*******">𝑗</ci></apply><cn id="S4.SS2.p2.10.m2.1.1.3.cmml" type="integer" xref="S4.SS2.p2.10.m2.1.1.3">0</cn></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.10.m2.1c">y_{j}^{0}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.10.m2.1d">italic_y start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT 0 end_POSTSUPERSCRIPT</annotation></semantics></math> is the initial noisy label of training data. As shown in Eq. (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#S4.E6" title="Equation 6 ‣ 4.2 Iterative Refinement Labeling (RA-Labeling) ‣ 4 LARA: The Proposed Framework ‣ Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling"><span class="ltx_text ltx_ref_tag">6</span></a>), we apply the exponential moving average to mitigate the damage of under-predicted labels of <em class="ltx_emph ltx_font_italic" id="S4.SS2.p2.15.1">noisy samples</em> and thus the confidence score <math alttext="\alpha_{j}^{k}" class="ltx_Math" display="inline" id="S4.SS2.p2.11.m3.1"><semantics id="S4.SS2.p2.11.m3.1a"><msubsup id="S4.SS2.p2.11.m3.1.1" xref="S4.SS2.p2.11.m3.1.1.cmml"><mi id="S4.SS2.p2.11.m3.*******" xref="S4.SS2.p2.11.m3.*******.cmml">α</mi><mi id="S4.SS2.p2.11.m3.*******" xref="S4.SS2.p2.11.m3.*******.cmml">j</mi><mi id="S4.SS2.p2.11.m3.1.1.3" xref="S4.SS2.p2.11.m3.1.1.3.cmml">k</mi></msubsup><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.11.m3.1b"><apply id="S4.SS2.p2.11.m3.1.1.cmml" xref="S4.SS2.p2.11.m3.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.11.m3.1.1.1.cmml" xref="S4.SS2.p2.11.m3.1.1">superscript</csymbol><apply id="S4.SS2.p2.11.m3.1.1.2.cmml" xref="S4.SS2.p2.11.m3.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.11.m3.*******.cmml" xref="S4.SS2.p2.11.m3.1.1">subscript</csymbol><ci id="S4.SS2.p2.11.m3.*******.cmml" xref="S4.SS2.p2.11.m3.*******">𝛼</ci><ci id="S4.SS2.p2.11.m3.*******.cmml" xref="S4.SS2.p2.11.m3.*******">𝑗</ci></apply><ci id="S4.SS2.p2.11.m3.1.1.3.cmml" xref="S4.SS2.p2.11.m3.1.1.3">𝑘</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.11.m3.1c">\alpha_{j}^{k}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.11.m3.1d">italic_α start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT</annotation></semantics></math> is a crucial parameter in this self-adaptive training scheme. We propose to update <math alttext="\alpha_{j}^{k}" class="ltx_Math" display="inline" id="S4.SS2.p2.12.m4.1"><semantics id="S4.SS2.p2.12.m4.1a"><msubsup id="S4.SS2.p2.12.m4.1.1" xref="S4.SS2.p2.12.m4.1.1.cmml"><mi id="S4.SS2.p2.12.m4.*******" xref="S4.SS2.p2.12.m4.*******.cmml">α</mi><mi id="S4.SS2.p2.12.m4.*******" xref="S4.SS2.p2.12.m4.*******.cmml">j</mi><mi id="S4.SS2.p2.12.m4.1.1.3" xref="S4.SS2.p2.12.m4.1.1.3.cmml">k</mi></msubsup><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.12.m4.1b"><apply id="S4.SS2.p2.12.m4.1.1.cmml" xref="S4.SS2.p2.12.m4.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.12.m4.1.1.1.cmml" xref="S4.SS2.p2.12.m4.1.1">superscript</csymbol><apply id="S4.SS2.p2.12.m4.1.1.2.cmml" xref="S4.SS2.p2.12.m4.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.12.m4.*******.cmml" xref="S4.SS2.p2.12.m4.1.1">subscript</csymbol><ci id="S4.SS2.p2.12.m4.*******.cmml" xref="S4.SS2.p2.12.m4.*******">𝛼</ci><ci id="S4.SS2.p2.12.m4.*******.cmml" xref="S4.SS2.p2.12.m4.*******">𝑗</ci></apply><ci id="S4.SS2.p2.12.m4.1.1.3.cmml" xref="S4.SS2.p2.12.m4.1.1.3">𝑘</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.12.m4.1c">\alpha_{j}^{k}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.12.m4.1d">italic_α start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT</annotation></semantics></math> according to the training loss at the current step to alleviate the instability issue of using instantaneous prediction under <em class="ltx_emph ltx_font_italic" id="S4.SS2.p2.15.2">noisy samples</em>. <math alttext="\alpha_{j}^{k}" class="ltx_Math" display="inline" id="S4.SS2.p2.13.m5.1"><semantics id="S4.SS2.p2.13.m5.1a"><msubsup id="S4.SS2.p2.13.m5.1.1" xref="S4.SS2.p2.13.m5.1.1.cmml"><mi id="S4.SS2.p2.13.m5.*******" xref="S4.SS2.p2.13.m5.*******.cmml">α</mi><mi id="S4.SS2.p2.13.m5.*******" xref="S4.SS2.p2.13.m5.*******.cmml">j</mi><mi id="S4.SS2.p2.13.m5.1.1.3" xref="S4.SS2.p2.13.m5.1.1.3.cmml">k</mi></msubsup><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.13.m5.1b"><apply id="S4.SS2.p2.13.m5.1.1.cmml" xref="S4.SS2.p2.13.m5.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.13.m5.1.1.1.cmml" xref="S4.SS2.p2.13.m5.1.1">superscript</csymbol><apply id="S4.SS2.p2.13.m5.1.1.2.cmml" xref="S4.SS2.p2.13.m5.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.13.m5.*******.cmml" xref="S4.SS2.p2.13.m5.1.1">subscript</csymbol><ci id="S4.SS2.p2.13.m5.*******.cmml" xref="S4.SS2.p2.13.m5.*******">𝛼</ci><ci id="S4.SS2.p2.13.m5.*******.cmml" xref="S4.SS2.p2.13.m5.*******">𝑗</ci></apply><ci id="S4.SS2.p2.13.m5.1.1.3.cmml" xref="S4.SS2.p2.13.m5.1.1.3">𝑘</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.13.m5.1c">\alpha_{j}^{k}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.13.m5.1d">italic_α start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT</annotation></semantics></math> of the sample <math alttext="{\bm{x}}_{j}" class="ltx_Math" display="inline" id="S4.SS2.p2.14.m6.1"><semantics id="S4.SS2.p2.14.m6.1a"><msub id="S4.SS2.p2.14.m6.1.1" xref="S4.SS2.p2.14.m6.1.1.cmml"><mi id="S4.SS2.p2.14.m6.1.1.2" xref="S4.SS2.p2.14.m6.1.1.2.cmml">𝒙</mi><mi id="S4.SS2.p2.14.m6.1.1.3" xref="S4.SS2.p2.14.m6.1.1.3.cmml">j</mi></msub><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.14.m6.1b"><apply id="S4.SS2.p2.14.m6.1.1.cmml" xref="S4.SS2.p2.14.m6.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.14.m6.1.1.1.cmml" xref="S4.SS2.p2.14.m6.1.1">subscript</csymbol><ci id="S4.SS2.p2.14.m6.1.1.2.cmml" xref="S4.SS2.p2.14.m6.1.1.2">𝒙</ci><ci id="S4.SS2.p2.14.m6.1.1.3.cmml" xref="S4.SS2.p2.14.m6.1.1.3">𝑗</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.14.m6.1c">{\bm{x}}_{j}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.14.m6.1d">bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT</annotation></semantics></math> at the <math alttext="k" class="ltx_Math" display="inline" id="S4.SS2.p2.15.m7.1"><semantics id="S4.SS2.p2.15.m7.1a"><mi id="S4.SS2.p2.15.m7.1.1" xref="S4.SS2.p2.15.m7.1.1.cmml">k</mi><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.15.m7.1b"><ci id="S4.SS2.p2.15.m7.1.1.cmml" xref="S4.SS2.p2.15.m7.1.1">𝑘</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.15.m7.1c">k</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.15.m7.1d">italic_k</annotation></semantics></math>-th step is calculated by:</p>
<table class="ltx_equation ltx_eqn_table" id="S4.E7">
<tbody><tr class="ltx_equation ltx_eqn_row ltx_align_baseline">
<td class="ltx_eqn_cell ltx_eqn_center_padleft"></td>
<td class="ltx_eqn_cell ltx_align_center"><math alttext="\alpha_{j}^{k}=1\left\{\mathcal{L}\left(f_{\theta^{k}}({\bm{x}}_{j}),y_{j}^{k}%
\right)\leq\epsilon^{k}\right\}," class="ltx_Math" display="block" id="S4.E7.m1.1"><semantics id="S4.E7.m1.1a"><mrow id="S4.E7.m*******" xref="S4.E7.m*******.1.cmml"><mrow id="S4.E7.m*******.1" xref="S4.E7.m*******.1.cmml"><msubsup id="S4.E7.m*******.1.3" xref="S4.E7.m*******.1.3.cmml"><mi id="S4.E7.m1.1.1.*******.2" xref="S4.E7.m1.1.1.*******.2.cmml">α</mi><mi id="S4.E7.m1.1.1.*******.3" xref="S4.E7.m1.1.1.*******.3.cmml">j</mi><mi id="S4.E7.m1.1.1.*******" xref="S4.E7.m1.1.1.*******.cmml">k</mi></msubsup><mo id="S4.E7.m*******.1.2" xref="S4.E7.m*******.1.2.cmml">=</mo><mrow id="S4.E7.m*******.1.1" xref="S4.E7.m*******.1.1.cmml"><mn id="S4.E7.m*******.1.1.3" xref="S4.E7.m*******.1.1.3.cmml">1</mn><mo id="S4.E7.m*******.1.1.2" xref="S4.E7.m*******.1.1.2.cmml">⁢</mo><mrow id="S4.E7.m*******.*******" xref="S4.E7.m*******.*******.cmml"><mo id="S4.E7.m*******.*******.2" xref="S4.E7.m*******.*******.cmml">{</mo><mrow id="S4.E7.m*******.*******.1" xref="S4.E7.m*******.*******.1.cmml"><mrow id="S4.E7.m*******.*******.1.2" xref="S4.E7.m*******.*******.1.2.cmml"><mi class="ltx_font_mathcaligraphic" id="S4.E7.m*******.*******.1.2.4" xref="S4.E7.m*******.*******.1.2.4.cmml">ℒ</mi><mo id="S4.E7.m*******.*******.1.2.3" xref="S4.E7.m*******.*******.1.2.3.cmml">⁢</mo><mrow id="S4.E7.m*******.*******.1.2.2.2" xref="S4.E7.m*******.*******.1.2.2.3.cmml"><mo id="S4.E7.m*******.*******.1.2.2.2.3" xref="S4.E7.m*******.*******.1.2.2.3.cmml">(</mo><mrow id="S4.E7.m*******.*******.*******.1" xref="S4.E7.m*******.*******.*******.1.cmml"><msub id="S4.E7.m*******.*******.*******.1.3" xref="S4.E7.m*******.*******.*******.1.3.cmml"><mi id="S4.E7.m*******.*******.1.1.1.*******" xref="S4.E7.m*******.*******.1.1.1.*******.cmml">f</mi><msup id="S4.E7.m*******.*******.1.1.1.*******" xref="S4.E7.m*******.*******.1.1.1.*******.cmml"><mi id="S4.E7.m*******.*******.1.1.1.*******.2" xref="S4.E7.m*******.*******.1.1.1.*******.2.cmml">θ</mi><mi id="S4.E7.m*******.*******.1.1.1.*******.3" xref="S4.E7.m*******.*******.1.1.1.*******.3.cmml">k</mi></msup></msub><mo id="S4.E7.m*******.*******.*******.1.2" xref="S4.E7.m*******.*******.*******.1.2.cmml">⁢</mo><mrow id="S4.E7.m*******.*******.*******.1.1.1" xref="S4.E7.m*******.*******.*******.*******.cmml"><mo id="S4.E7.m*******.*******.*******.*******" stretchy="false" xref="S4.E7.m*******.*******.*******.*******.cmml">(</mo><msub id="S4.E7.m*******.*******.*******.*******" xref="S4.E7.m*******.*******.*******.*******.cmml"><mi id="S4.E7.m*******.*******.*******.*******.2" xref="S4.E7.m*******.*******.*******.*******.2.cmml">𝒙</mi><mi id="S4.E7.m*******.*******.*******.*******.3" xref="S4.E7.m*******.*******.*******.*******.3.cmml">j</mi></msub><mo id="S4.E7.m*******.*******.*******.*******" stretchy="false" xref="S4.E7.m*******.*******.*******.*******.cmml">)</mo></mrow></mrow><mo id="S4.E7.m*******.*******.1.2.2.2.4" xref="S4.E7.m*******.*******.1.2.2.3.cmml">,</mo><msubsup id="S4.E7.m*******.*******.1.*******" xref="S4.E7.m*******.*******.1.*******.cmml"><mi id="S4.E7.m*******.*******.1.*******.2.2" xref="S4.E7.m*******.*******.1.*******.2.2.cmml">y</mi><mi id="S4.E7.m*******.*******.1.*******.2.3" xref="S4.E7.m*******.*******.1.*******.2.3.cmml">j</mi><mi id="S4.E7.m*******.*******.1.*******.3" xref="S4.E7.m*******.*******.1.*******.3.cmml">k</mi></msubsup><mo id="S4.E7.m*******.*******.1.2.2.2.5" xref="S4.E7.m*******.*******.1.2.2.3.cmml">)</mo></mrow></mrow><mo id="S4.E7.m*******.*******.1.3" xref="S4.E7.m*******.*******.1.3.cmml">≤</mo><msup id="S4.E7.m*******.*******.1.4" xref="S4.E7.m*******.*******.1.4.cmml"><mi id="S4.E7.m*******.*******.1.4.2" xref="S4.E7.m*******.*******.1.4.2.cmml">ϵ</mi><mi id="S4.E7.m*******.*******.1.4.3" xref="S4.E7.m*******.*******.1.4.3.cmml">k</mi></msup></mrow><mo id="S4.E7.m*******.*******.3" xref="S4.E7.m*******.*******.cmml">}</mo></mrow></mrow></mrow><mo id="S4.E7.m*******.2" xref="S4.E7.m*******.1.cmml">,</mo></mrow><annotation-xml encoding="MathML-Content" id="S4.E7.m1.1b"><apply id="S4.E7.m*******.1.cmml" xref="S4.E7.m*******"><eq id="S4.E7.m*******.1.2.cmml" xref="S4.E7.m*******.1.2"></eq><apply id="S4.E7.m*******.1.3.cmml" xref="S4.E7.m*******.1.3"><csymbol cd="ambiguous" id="S4.E7.m1.1.1.*******.cmml" xref="S4.E7.m*******.1.3">superscript</csymbol><apply id="S4.E7.m1.1.1.*******.cmml" xref="S4.E7.m*******.1.3"><csymbol cd="ambiguous" id="S4.E7.m1.1.1.*******.1.cmml" xref="S4.E7.m*******.1.3">subscript</csymbol><ci id="S4.E7.m1.1.1.*******.2.cmml" xref="S4.E7.m1.1.1.*******.2">𝛼</ci><ci id="S4.E7.m1.1.1.*******.3.cmml" xref="S4.E7.m1.1.1.*******.3">𝑗</ci></apply><ci id="S4.E7.m1.1.1.*******.cmml" xref="S4.E7.m1.1.1.*******">𝑘</ci></apply><apply id="S4.E7.m*******.1.1.cmml" xref="S4.E7.m*******.1.1"><times id="S4.E7.m*******.1.1.2.cmml" xref="S4.E7.m*******.1.1.2"></times><cn id="S4.E7.m*******.1.1.3.cmml" type="integer" xref="S4.E7.m*******.1.1.3">1</cn><set id="S4.E7.m*******.*******.cmml" xref="S4.E7.m*******.*******"><apply id="S4.E7.m*******.*******.1.cmml" xref="S4.E7.m*******.*******.1"><leq id="S4.E7.m*******.*******.1.3.cmml" xref="S4.E7.m*******.*******.1.3"></leq><apply id="S4.E7.m*******.*******.1.2.cmml" xref="S4.E7.m*******.*******.1.2"><times id="S4.E7.m*******.*******.1.2.3.cmml" xref="S4.E7.m*******.*******.1.2.3"></times><ci id="S4.E7.m*******.*******.1.2.4.cmml" xref="S4.E7.m*******.*******.1.2.4">ℒ</ci><interval closure="open" id="S4.E7.m*******.*******.1.2.2.3.cmml" xref="S4.E7.m*******.*******.1.2.2.2"><apply id="S4.E7.m*******.*******.*******.1.cmml" xref="S4.E7.m*******.*******.*******.1"><times id="S4.E7.m*******.*******.*******.1.2.cmml" xref="S4.E7.m*******.*******.*******.1.2"></times><apply id="S4.E7.m*******.*******.*******.1.3.cmml" xref="S4.E7.m*******.*******.*******.1.3"><csymbol cd="ambiguous" id="S4.E7.m*******.*******.1.1.1.*******.cmml" xref="S4.E7.m*******.*******.*******.1.3">subscript</csymbol><ci id="S4.E7.m*******.*******.1.1.1.*******.cmml" xref="S4.E7.m*******.*******.1.1.1.*******">𝑓</ci><apply id="S4.E7.m*******.*******.1.1.1.*******.cmml" xref="S4.E7.m*******.*******.1.1.1.*******"><csymbol cd="ambiguous" id="S4.E7.m*******.*******.1.1.1.*******.1.cmml" xref="S4.E7.m*******.*******.1.1.1.*******">superscript</csymbol><ci id="S4.E7.m*******.*******.1.1.1.*******.2.cmml" xref="S4.E7.m*******.*******.1.1.1.*******.2">𝜃</ci><ci id="S4.E7.m*******.*******.1.1.1.*******.3.cmml" xref="S4.E7.m*******.*******.1.1.1.*******.3">𝑘</ci></apply></apply><apply id="S4.E7.m*******.*******.*******.*******.cmml" xref="S4.E7.m*******.*******.*******.1.1.1"><csymbol cd="ambiguous" id="S4.E7.m*******.*******.*******.*******.1.cmml" xref="S4.E7.m*******.*******.*******.1.1.1">subscript</csymbol><ci id="S4.E7.m*******.*******.*******.*******.2.cmml" xref="S4.E7.m*******.*******.*******.*******.2">𝒙</ci><ci id="S4.E7.m*******.*******.*******.*******.3.cmml" xref="S4.E7.m*******.*******.*******.*******.3">𝑗</ci></apply></apply><apply id="S4.E7.m*******.*******.1.*******.cmml" xref="S4.E7.m*******.*******.1.*******"><csymbol cd="ambiguous" id="S4.E7.m*******.*******.1.*******.1.cmml" xref="S4.E7.m*******.*******.1.*******">superscript</csymbol><apply id="S4.E7.m*******.*******.1.*******.2.cmml" xref="S4.E7.m*******.*******.1.*******"><csymbol cd="ambiguous" id="S4.E7.m*******.*******.1.*******.2.1.cmml" xref="S4.E7.m*******.*******.1.*******">subscript</csymbol><ci id="S4.E7.m*******.*******.1.*******.2.2.cmml" xref="S4.E7.m*******.*******.1.*******.2.2">𝑦</ci><ci id="S4.E7.m*******.*******.1.*******.2.3.cmml" xref="S4.E7.m*******.*******.1.*******.2.3">𝑗</ci></apply><ci id="S4.E7.m*******.*******.1.*******.3.cmml" xref="S4.E7.m*******.*******.1.*******.3">𝑘</ci></apply></interval></apply><apply id="S4.E7.m*******.*******.1.4.cmml" xref="S4.E7.m*******.*******.1.4"><csymbol cd="ambiguous" id="S4.E7.m*******.*******.1.4.1.cmml" xref="S4.E7.m*******.*******.1.4">superscript</csymbol><ci id="S4.E7.m*******.*******.1.4.2.cmml" xref="S4.E7.m*******.*******.1.4.2">italic-ϵ</ci><ci id="S4.E7.m*******.*******.1.4.3.cmml" xref="S4.E7.m*******.*******.1.4.3">𝑘</ci></apply></apply></set></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.E7.m1.1c">\alpha_{j}^{k}=1\left\{\mathcal{L}\left(f_{\theta^{k}}({\bm{x}}_{j}),y_{j}^{k}%
\right)\leq\epsilon^{k}\right\},</annotation><annotation encoding="application/x-llamapun" id="S4.E7.m1.1d">italic_α start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT = 1 { caligraphic_L ( italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT end_POSTSUBSCRIPT ( bold_italic_x start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT ) , italic_y start_POSTSUBSCRIPT italic_j end_POSTSUBSCRIPT start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT ) ≤ italic_ϵ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT } ,</annotation></semantics></math></td>
<td class="ltx_eqn_cell ltx_eqn_center_padright"></td>
<td class="ltx_eqn_cell ltx_eqn_eqno ltx_align_middle ltx_align_right" rowspan="1"><span class="ltx_tag ltx_tag_equation ltx_align_right">(7)</span></td>
</tr></tbody>
</table>
<p class="ltx_p" id="S4.SS2.p2.21">where <math alttext="1\left\{\cdot\right\}" class="ltx_Math" display="inline" id="S4.SS2.p2.16.m1.1"><semantics id="S4.SS2.p2.16.m1.1a"><mrow id="S4.SS2.p2.16.m1.1.2" xref="S4.SS2.p2.16.m1.1.2.cmml"><mn id="S4.SS2.p2.16.m*******" xref="S4.SS2.p2.16.m*******.cmml">1</mn><mo id="S4.SS2.p2.16.m*******" xref="S4.SS2.p2.16.m*******.cmml">⁢</mo><mrow id="S4.SS2.p2.16.m*******.2" xref="S4.SS2.p2.16.m*******.1.cmml"><mo id="S4.SS2.p2.16.m*******.2.1" xref="S4.SS2.p2.16.m*******.1.cmml">{</mo><mo id="S4.SS2.p2.16.m1.1.1" lspace="0em" rspace="0em" xref="S4.SS2.p2.16.m1.1.1.cmml">⋅</mo><mo id="S4.SS2.p2.16.m*******.2.2" xref="S4.SS2.p2.16.m*******.1.cmml">}</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.16.m1.1b"><apply id="S4.SS2.p2.16.m1.1.2.cmml" xref="S4.SS2.p2.16.m1.1.2"><times id="S4.SS2.p2.16.m*******.cmml" xref="S4.SS2.p2.16.m*******"></times><cn id="S4.SS2.p2.16.m*******.cmml" type="integer" xref="S4.SS2.p2.16.m*******">1</cn><set id="S4.SS2.p2.16.m*******.1.cmml" xref="S4.SS2.p2.16.m*******.2"><ci id="S4.SS2.p2.16.m1.1.1.cmml" xref="S4.SS2.p2.16.m1.1.1">⋅</ci></set></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.16.m1.1c">1\left\{\cdot\right\}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.16.m1.1d">1 { ⋅ }</annotation></semantics></math> denotes the indicator function, <math alttext="\epsilon^{k}" class="ltx_Math" display="inline" id="S4.SS2.p2.17.m2.1"><semantics id="S4.SS2.p2.17.m2.1a"><msup id="S4.SS2.p2.17.m2.1.1" xref="S4.SS2.p2.17.m2.1.1.cmml"><mi id="S4.SS2.p2.17.m2.1.1.2" xref="S4.SS2.p2.17.m2.1.1.2.cmml">ϵ</mi><mi id="S4.SS2.p2.17.m2.1.1.3" xref="S4.SS2.p2.17.m2.1.1.3.cmml">k</mi></msup><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.17.m2.1b"><apply id="S4.SS2.p2.17.m2.1.1.cmml" xref="S4.SS2.p2.17.m2.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.17.m2.1.1.1.cmml" xref="S4.SS2.p2.17.m2.1.1">superscript</csymbol><ci id="S4.SS2.p2.17.m2.1.1.2.cmml" xref="S4.SS2.p2.17.m2.1.1.2">italic-ϵ</ci><ci id="S4.SS2.p2.17.m2.1.1.3.cmml" xref="S4.SS2.p2.17.m2.1.1.3">𝑘</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.17.m2.1c">\epsilon^{k}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.17.m2.1d">italic_ϵ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT</annotation></semantics></math> is a hyper-parameter to control label confidence, and <math alttext="\mathcal{L}(\cdot,\cdot)" class="ltx_Math" display="inline" id="S4.SS2.p2.18.m3.2"><semantics id="S4.SS2.p2.18.m3.2a"><mrow id="S4.SS2.p2.18.m3.2.3" xref="S4.SS2.p2.18.m3.2.3.cmml"><mi class="ltx_font_mathcaligraphic" id="S4.SS2.p2.18.m3.2.3.2" xref="S4.SS2.p2.18.m3.2.3.2.cmml">ℒ</mi><mo id="S4.SS2.p2.18.m3.2.3.1" xref="S4.SS2.p2.18.m3.2.3.1.cmml">⁢</mo><mrow id="S4.SS2.p2.18.m3.*******" xref="S4.SS2.p2.18.m3.*******.cmml"><mo id="S4.SS2.p2.18.m3.*******.1" stretchy="false" xref="S4.SS2.p2.18.m3.*******.cmml">(</mo><mo id="S4.SS2.p2.18.m3.1.1" lspace="0em" rspace="0em" xref="S4.SS2.p2.18.m3.1.1.cmml">⋅</mo><mo id="S4.SS2.p2.18.m3.2.*******" rspace="0em" xref="S4.SS2.p2.18.m3.*******.cmml">,</mo><mo id="S4.SS2.p2.18.m3.2.2" lspace="0em" rspace="0em" xref="S4.SS2.p2.18.m3.2.2.cmml">⋅</mo><mo id="S4.SS2.p2.18.m3.*******.3" stretchy="false" xref="S4.SS2.p2.18.m3.*******.cmml">)</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.18.m3.2b"><apply id="S4.SS2.p2.18.m3.2.3.cmml" xref="S4.SS2.p2.18.m3.2.3"><times id="S4.SS2.p2.18.m3.2.3.1.cmml" xref="S4.SS2.p2.18.m3.2.3.1"></times><ci id="S4.SS2.p2.18.m3.2.3.2.cmml" xref="S4.SS2.p2.18.m3.2.3.2">ℒ</ci><interval closure="open" id="S4.SS2.p2.18.m3.*******.cmml" xref="S4.SS2.p2.18.m3.*******"><ci id="S4.SS2.p2.18.m3.1.1.cmml" xref="S4.SS2.p2.18.m3.1.1">⋅</ci><ci id="S4.SS2.p2.18.m3.2.2.cmml" xref="S4.SS2.p2.18.m3.2.2">⋅</ci></interval></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.18.m3.2c">\mathcal{L}(\cdot,\cdot)</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.18.m3.2d">caligraphic_L ( ⋅ , ⋅ )</annotation></semantics></math> denotes the supervised loss function, <em class="ltx_emph ltx_font_italic" id="S4.SS2.p2.21.1">e.g.</em>, the cross-entropy loss.
In order to develop a more coherent predictor that improves its ability to evaluate the consistency of <em class="ltx_emph ltx_font_italic" id="S4.SS2.p2.21.2">noisy samples</em>, we adaptively set the value of <math alttext="\epsilon^{k}" class="ltx_Math" display="inline" id="S4.SS2.p2.19.m4.1"><semantics id="S4.SS2.p2.19.m4.1a"><msup id="S4.SS2.p2.19.m4.1.1" xref="S4.SS2.p2.19.m4.1.1.cmml"><mi id="S4.SS2.p2.19.m4.1.1.2" xref="S4.SS2.p2.19.m4.1.1.2.cmml">ϵ</mi><mi id="S4.SS2.p2.19.m4.1.1.3" xref="S4.SS2.p2.19.m4.1.1.3.cmml">k</mi></msup><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.19.m4.1b"><apply id="S4.SS2.p2.19.m4.1.1.cmml" xref="S4.SS2.p2.19.m4.1.1"><csymbol cd="ambiguous" id="S4.SS2.p2.19.m4.1.1.1.cmml" xref="S4.SS2.p2.19.m4.1.1">superscript</csymbol><ci id="S4.SS2.p2.19.m4.1.1.2.cmml" xref="S4.SS2.p2.19.m4.1.1.2">italic-ϵ</ci><ci id="S4.SS2.p2.19.m4.1.1.3.cmml" xref="S4.SS2.p2.19.m4.1.1.3">𝑘</ci></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.19.m4.1c">\epsilon^{k}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.19.m4.1d">italic_ϵ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT</annotation></semantics></math> to roughly adjust the ratio of labels in training data, denoted by <math alttext="r" class="ltx_Math" display="inline" id="S4.SS2.p2.20.m5.1"><semantics id="S4.SS2.p2.20.m5.1a"><mi id="S4.SS2.p2.20.m5.1.1" xref="S4.SS2.p2.20.m5.1.1.cmml">r</mi><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.20.m5.1b"><ci id="S4.SS2.p2.20.m5.1.1.cmml" xref="S4.SS2.p2.20.m5.1.1">𝑟</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.20.m5.1c">r</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.20.m5.1d">italic_r</annotation></semantics></math>.
We also perform the hyperparameter study in Sec. <a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#S5.SS3" title="5.3 Experimental Analysis ‣ 5 Experiments ‣ Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling"><span class="ltx_text ltx_ref_tag">5.3</span></a> and empirically identify that RA-Labeling achieves a relatively stable performance when <math alttext="r&lt;10\%" class="ltx_Math" display="inline" id="S4.SS2.p2.21.m6.1"><semantics id="S4.SS2.p2.21.m6.1a"><mrow id="S4.SS2.p2.21.m6.1.1" xref="S4.SS2.p2.21.m6.1.1.cmml"><mi id="S4.SS2.p2.21.m6.1.1.2" xref="S4.SS2.p2.21.m6.1.1.2.cmml">r</mi><mo id="S4.SS2.p2.21.m6.1.1.1" xref="S4.SS2.p2.21.m6.1.1.1.cmml">&lt;</mo><mrow id="S4.SS2.p2.21.m6.1.1.3" xref="S4.SS2.p2.21.m6.1.1.3.cmml"><mn id="S4.SS2.p2.21.m6.*******" xref="S4.SS2.p2.21.m6.*******.cmml">10</mn><mo id="S4.SS2.p2.21.m6.*******" xref="S4.SS2.p2.21.m6.*******.cmml">%</mo></mrow></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p2.21.m6.1b"><apply id="S4.SS2.p2.21.m6.1.1.cmml" xref="S4.SS2.p2.21.m6.1.1"><lt id="S4.SS2.p2.21.m6.1.1.1.cmml" xref="S4.SS2.p2.21.m6.1.1.1"></lt><ci id="S4.SS2.p2.21.m6.1.1.2.cmml" xref="S4.SS2.p2.21.m6.1.1.2">𝑟</ci><apply id="S4.SS2.p2.21.m6.1.1.3.cmml" xref="S4.SS2.p2.21.m6.1.1.3"><csymbol cd="latexml" id="S4.SS2.p2.21.m6.*******.cmml" xref="S4.SS2.p2.21.m6.*******">percent</csymbol><cn id="S4.SS2.p2.21.m6.*******.cmml" type="integer" xref="S4.SS2.p2.21.m6.*******">10</cn></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p2.21.m6.1c">r&lt;10\%</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p2.21.m6.1d">italic_r &lt; 10 %</annotation></semantics></math>.</p>
</div>
<div class="ltx_para" id="S4.SS2.p3">
<p class="ltx_p" id="S4.SS2.p3.6">Different from the conventional noisy labeling methods <cite class="ltx_cite ltx_citemacro_cite">Song <span class="ltx_text ltx_font_italic">et al.</span> (<a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#bib.bib25" title="">2022</a>)</cite>, we further propose a combination method to integrate the learned predictor. Specifically, after running <math alttext="K" class="ltx_Math" display="inline" id="S4.SS2.p3.1.m1.1"><semantics id="S4.SS2.p3.1.m1.1a"><mi id="S4.SS2.p3.1.m1.1.1" xref="S4.SS2.p3.1.m1.1.1.cmml">K</mi><annotation-xml encoding="MathML-Content" id="S4.SS2.p3.1.m1.1b"><ci id="S4.SS2.p3.1.m1.1.1.cmml" xref="S4.SS2.p3.1.m1.1.1">𝐾</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p3.1.m1.1c">K</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p3.1.m1.1d">italic_K</annotation></semantics></math> iterations of iterative refinement labeling, we obtain <math alttext="K+1" class="ltx_Math" display="inline" id="S4.SS2.p3.2.m2.1"><semantics id="S4.SS2.p3.2.m2.1a"><mrow id="S4.SS2.p3.2.m2.1.1" xref="S4.SS2.p3.2.m2.1.1.cmml"><mi id="S4.SS2.p3.2.m2.1.1.2" xref="S4.SS2.p3.2.m2.1.1.2.cmml">K</mi><mo id="S4.SS2.p3.2.m2.1.1.1" xref="S4.SS2.p3.2.m2.1.1.1.cmml">+</mo><mn id="S4.SS2.p3.2.m2.1.1.3" xref="S4.SS2.p3.2.m2.1.1.3.cmml">1</mn></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p3.2.m2.1b"><apply id="S4.SS2.p3.2.m2.1.1.cmml" xref="S4.SS2.p3.2.m2.1.1"><plus id="S4.SS2.p3.2.m2.1.1.1.cmml" xref="S4.SS2.p3.2.m2.1.1.1"></plus><ci id="S4.SS2.p3.2.m2.1.1.2.cmml" xref="S4.SS2.p3.2.m2.1.1.2">𝐾</ci><cn id="S4.SS2.p3.2.m2.1.1.3.cmml" type="integer" xref="S4.SS2.p3.2.m2.1.1.3">1</cn></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p3.2.m2.1c">K+1</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p3.2.m2.1d">italic_K + 1</annotation></semantics></math> iterated predictors <math alttext="\{f_{\theta_{0}},\cdot\cdot\cdot,f_{\theta_{K}}\}" class="ltx_Math" display="inline" id="S4.SS2.p3.3.m3.3"><semantics id="S4.SS2.p3.3.m3.3a"><mrow id="S4.SS2.p3.3.m3.3.3.2" xref="S4.SS2.p3.3.m3.3.3.3.cmml"><mo id="S4.SS2.p3.3.m3.3.3.2.3" stretchy="false" xref="S4.SS2.p3.3.m3.3.3.3.cmml">{</mo><msub id="S4.SS2.p3.3.m3.*******" xref="S4.SS2.p3.3.m3.*******.cmml"><mi id="S4.SS2.p3.3.m3.*******.2" xref="S4.SS2.p3.3.m3.*******.2.cmml">f</mi><msub id="S4.SS2.p3.3.m3.*******.3" xref="S4.SS2.p3.3.m3.*******.3.cmml"><mi id="S4.SS2.p3.3.m3.2.2.*******" xref="S4.SS2.p3.3.m3.2.2.*******.cmml">θ</mi><mn id="S4.SS2.p3.3.m3.2.2.*******" xref="S4.SS2.p3.3.m3.2.2.*******.cmml">0</mn></msub></msub><mo id="S4.SS2.p3.3.m3.3.3.2.4" xref="S4.SS2.p3.3.m3.3.3.3.cmml">,</mo><mi id="S4.SS2.p3.3.m3.1.1" mathvariant="normal" xref="S4.SS2.p3.3.m3.1.1.cmml">⋯</mi><mo id="S4.SS2.p3.3.m3.3.3.2.5" xref="S4.SS2.p3.3.m3.3.3.3.cmml">,</mo><msub id="S4.SS2.p3.3.m3.*******" xref="S4.SS2.p3.3.m3.*******.cmml"><mi id="S4.SS2.p3.3.m3.*******.2" xref="S4.SS2.p3.3.m3.*******.2.cmml">f</mi><msub id="S4.SS2.p3.3.m3.*******.3" xref="S4.SS2.p3.3.m3.*******.3.cmml"><mi id="S4.SS2.p3.3.m3.*******.3.2" xref="S4.SS2.p3.3.m3.*******.3.2.cmml">θ</mi><mi id="S4.SS2.p3.3.m3.*******.3.3" xref="S4.SS2.p3.3.m3.*******.3.3.cmml">K</mi></msub></msub><mo id="S4.SS2.p3.3.m3.*******" stretchy="false" xref="S4.SS2.p3.3.m3.3.3.3.cmml">}</mo></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p3.3.m3.3b"><set id="S4.SS2.p3.3.m3.3.3.3.cmml" xref="S4.SS2.p3.3.m3.3.3.2"><apply id="S4.SS2.p3.3.m3.*******.cmml" xref="S4.SS2.p3.3.m3.*******"><csymbol cd="ambiguous" id="S4.SS2.p3.3.m3.*******.1.cmml" xref="S4.SS2.p3.3.m3.*******">subscript</csymbol><ci id="S4.SS2.p3.3.m3.*******.2.cmml" xref="S4.SS2.p3.3.m3.*******.2">𝑓</ci><apply id="S4.SS2.p3.3.m3.*******.3.cmml" xref="S4.SS2.p3.3.m3.*******.3"><csymbol cd="ambiguous" id="S4.SS2.p3.3.m3.2.2.*******.cmml" xref="S4.SS2.p3.3.m3.*******.3">subscript</csymbol><ci id="S4.SS2.p3.3.m3.2.2.*******.cmml" xref="S4.SS2.p3.3.m3.2.2.*******">𝜃</ci><cn id="S4.SS2.p3.3.m3.2.2.*******.cmml" type="integer" xref="S4.SS2.p3.3.m3.2.2.*******">0</cn></apply></apply><ci id="S4.SS2.p3.3.m3.1.1.cmml" xref="S4.SS2.p3.3.m3.1.1">⋯</ci><apply id="S4.SS2.p3.3.m3.*******.cmml" xref="S4.SS2.p3.3.m3.*******"><csymbol cd="ambiguous" id="S4.SS2.p3.3.m3.*******.1.cmml" xref="S4.SS2.p3.3.m3.*******">subscript</csymbol><ci id="S4.SS2.p3.3.m3.*******.2.cmml" xref="S4.SS2.p3.3.m3.*******.2">𝑓</ci><apply id="S4.SS2.p3.3.m3.*******.3.cmml" xref="S4.SS2.p3.3.m3.*******.3"><csymbol cd="ambiguous" id="S4.SS2.p3.3.m3.*******.3.1.cmml" xref="S4.SS2.p3.3.m3.*******.3">subscript</csymbol><ci id="S4.SS2.p3.3.m3.*******.3.2.cmml" xref="S4.SS2.p3.3.m3.*******.3.2">𝜃</ci><ci id="S4.SS2.p3.3.m3.*******.3.3.cmml" xref="S4.SS2.p3.3.m3.*******.3.3">𝐾</ci></apply></apply></set></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p3.3.m3.3c">\{f_{\theta_{0}},\cdot\cdot\cdot,f_{\theta_{K}}\}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p3.3.m3.3d">{ italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUBSCRIPT 0 end_POSTSUBSCRIPT end_POSTSUBSCRIPT , ⋯ , italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUBSCRIPT italic_K end_POSTSUBSCRIPT end_POSTSUBSCRIPT }</annotation></semantics></math>. We integrate all predictors using the following two different combination schemes: (1) <math alttext="C=C_{\text{Last}}" class="ltx_Math" display="inline" id="S4.SS2.p3.4.m4.1"><semantics id="S4.SS2.p3.4.m4.1a"><mrow id="S4.SS2.p3.4.m4.1.1" xref="S4.SS2.p3.4.m4.1.1.cmml"><mi id="S4.SS2.p3.4.m4.1.1.2" xref="S4.SS2.p3.4.m4.1.1.2.cmml">C</mi><mo id="S4.SS2.p3.4.m4.1.1.1" xref="S4.SS2.p3.4.m4.1.1.1.cmml">=</mo><msub id="S4.SS2.p3.4.m4.1.1.3" xref="S4.SS2.p3.4.m4.1.1.3.cmml"><mi id="S4.SS2.p3.4.m4.*******" xref="S4.SS2.p3.4.m4.*******.cmml">C</mi><mtext id="S4.SS2.p3.4.m4.*******" xref="S4.SS2.p3.4.m4.*******a.cmml">Last</mtext></msub></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p3.4.m4.1b"><apply id="S4.SS2.p3.4.m4.1.1.cmml" xref="S4.SS2.p3.4.m4.1.1"><eq id="S4.SS2.p3.4.m4.1.1.1.cmml" xref="S4.SS2.p3.4.m4.1.1.1"></eq><ci id="S4.SS2.p3.4.m4.1.1.2.cmml" xref="S4.SS2.p3.4.m4.1.1.2">𝐶</ci><apply id="S4.SS2.p3.4.m4.1.1.3.cmml" xref="S4.SS2.p3.4.m4.1.1.3"><csymbol cd="ambiguous" id="S4.SS2.p3.4.m4.*******.cmml" xref="S4.SS2.p3.4.m4.1.1.3">subscript</csymbol><ci id="S4.SS2.p3.4.m4.*******.cmml" xref="S4.SS2.p3.4.m4.*******">𝐶</ci><ci id="S4.SS2.p3.4.m4.*******a.cmml" xref="S4.SS2.p3.4.m4.*******"><mtext id="S4.SS2.p3.4.m4.*******.cmml" mathsize="70%" xref="S4.SS2.p3.4.m4.*******">Last</mtext></ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p3.4.m4.1c">C=C_{\text{Last}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p3.4.m4.1d">italic_C = italic_C start_POSTSUBSCRIPT Last end_POSTSUBSCRIPT</annotation></semantics></math>: making a prediction according to the last iterated predictor. (2) <math alttext="C=C_{\text{Vote}}" class="ltx_Math" display="inline" id="S4.SS2.p3.5.m5.1"><semantics id="S4.SS2.p3.5.m5.1a"><mrow id="S4.SS2.p3.5.m5.1.1" xref="S4.SS2.p3.5.m5.1.1.cmml"><mi id="S4.SS2.p3.5.m5.1.1.2" xref="S4.SS2.p3.5.m5.1.1.2.cmml">C</mi><mo id="S4.SS2.p3.5.m5.1.1.1" xref="S4.SS2.p3.5.m5.1.1.1.cmml">=</mo><msub id="S4.SS2.p3.5.m5.1.1.3" xref="S4.SS2.p3.5.m5.1.1.3.cmml"><mi id="S4.SS2.p3.5.m5.*******" xref="S4.SS2.p3.5.m5.*******.cmml">C</mi><mtext id="S4.SS2.p3.5.m5.*******" xref="S4.SS2.p3.5.m5.*******a.cmml">Vote</mtext></msub></mrow><annotation-xml encoding="MathML-Content" id="S4.SS2.p3.5.m5.1b"><apply id="S4.SS2.p3.5.m5.1.1.cmml" xref="S4.SS2.p3.5.m5.1.1"><eq id="S4.SS2.p3.5.m5.1.1.1.cmml" xref="S4.SS2.p3.5.m5.1.1.1"></eq><ci id="S4.SS2.p3.5.m5.1.1.2.cmml" xref="S4.SS2.p3.5.m5.1.1.2">𝐶</ci><apply id="S4.SS2.p3.5.m5.1.1.3.cmml" xref="S4.SS2.p3.5.m5.1.1.3"><csymbol cd="ambiguous" id="S4.SS2.p3.5.m5.*******.cmml" xref="S4.SS2.p3.5.m5.1.1.3">subscript</csymbol><ci id="S4.SS2.p3.5.m5.*******.cmml" xref="S4.SS2.p3.5.m5.*******">𝐶</ci><ci id="S4.SS2.p3.5.m5.*******a.cmml" xref="S4.SS2.p3.5.m5.*******"><mtext id="S4.SS2.p3.5.m5.*******.cmml" mathsize="70%" xref="S4.SS2.p3.5.m5.*******">Vote</mtext></ci></apply></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p3.5.m5.1c">C=C_{\text{Vote}}</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p3.5.m5.1d">italic_C = italic_C start_POSTSUBSCRIPT Vote end_POSTSUBSCRIPT</annotation></semantics></math>: making a prediction that averages all iterated predictors. Then the output prediction model <math alttext="F" class="ltx_Math" display="inline" id="S4.SS2.p3.6.m6.1"><semantics id="S4.SS2.p3.6.m6.1a"><mi id="S4.SS2.p3.6.m6.1.1" xref="S4.SS2.p3.6.m6.1.1.cmml">F</mi><annotation-xml encoding="MathML-Content" id="S4.SS2.p3.6.m6.1b"><ci id="S4.SS2.p3.6.m6.1.1.cmml" xref="S4.SS2.p3.6.m6.1.1">𝐹</ci></annotation-xml><annotation encoding="application/x-tex" id="S4.SS2.p3.6.m6.1c">F</annotation><annotation encoding="application/x-llamapun" id="S4.SS2.p3.6.m6.1d">italic_F</annotation></semantics></math> is defined as follows:</p>
<table class="ltx_equationgroup ltx_eqn_table" id="S4.E8">
<tbody>
<tr class="ltx_equation ltx_eqn_row ltx_align_baseline" id="S4.E8X">
<td class="ltx_eqn_cell ltx_eqn_center_padleft"></td>
<td class="ltx_td ltx_eqn_cell"></td>
<td class="ltx_td ltx_align_left ltx_eqn_cell"><math alttext="\displaystyle\quad F({\bm{x}}):=" class="ltx_Math" display="inline" id="S4.E8X.2.1.1.m1.1"><semantics id="S4.E8X.2.1.1.m1.1a"><mrow id="S4.E8X.2.1.1.m1.1.2" xref="S4.E8X.2.1.1.m1.1.2.cmml"><mrow id="S4.E8X.2.1.1.m*******" xref="S4.E8X.2.1.1.m*******.cmml"><mi id="S4.E8X.2.1.1.m*******.2" xref="S4.E8X.2.1.1.m*******.2.cmml">F</mi><mo id="S4.E8X.2.1.1.m*******.1" xref="S4.E8X.2.1.1.m*******.1.cmml">⁢</mo><mrow id="S4.E8X.2.1.1.m*******.3.2" xref="S4.E8X.2.1.1.m*******.cmml"><mo id="S4.E8X.2.1.1.m*******.3.2.1" stretchy="false" xref="S4.E8X.2.1.1.m*******.cmml">(</mo><mi id="S4.E8X.2.1.1.m1.1.1" xref="S4.E8X.2.1.1.m1.1.1.cmml">𝒙</mi><mo id="S4.E8X.2.1.1.m*******.3.2.2" rspace="0.278em" stretchy="false" xref="S4.E8X.2.1.1.m*******.cmml">)</mo></mrow></mrow><mo id="S4.E8X.2.1.1.m*******" rspace="0.278em" xref="S4.E8X.2.1.1.m*******.cmml">:=</mo><mi id="S4.E8X.2.1.1.m*******" xref="S4.E8X.2.1.1.m*******.cmml"></mi></mrow><annotation-xml encoding="MathML-Content" id="S4.E8X.2.1.1.m1.1b"><apply id="S4.E8X.2.1.1.m1.1.2.cmml" xref="S4.E8X.2.1.1.m1.1.2"><csymbol cd="latexml" id="S4.E8X.2.1.1.m*******.cmml" xref="S4.E8X.2.1.1.m*******">assign</csymbol><apply id="S4.E8X.2.1.1.m*******.cmml" xref="S4.E8X.2.1.1.m*******"><times id="S4.E8X.2.1.1.m*******.1.cmml" xref="S4.E8X.2.1.1.m*******.1"></times><ci id="S4.E8X.2.1.1.m*******.2.cmml" xref="S4.E8X.2.1.1.m*******.2">𝐹</ci><ci id="S4.E8X.2.1.1.m1.1.1.cmml" xref="S4.E8X.2.1.1.m1.1.1">𝒙</ci></apply><csymbol cd="latexml" id="S4.E8X.2.1.1.m*******.cmml" xref="S4.E8X.2.1.1.m*******">absent</csymbol></apply></annotation-xml><annotation encoding="application/x-tex" id="S4.E8X.2.1.1.m1.1c">\displaystyle\quad F({\bm{x}}):=</annotation><annotation encoding="application/x-llamapun" id="S4.E8X.2.1.1.m1.1d">italic_F ( bold_italic_x ) :=</annotation></semantics></math></td>
<td class="ltx_eqn_cell ltx_eqn_center_padright"></td>
<td class="ltx_eqn_cell ltx_eqn_eqno ltx_align_middle ltx_align_right" rowspan="2"><span class="ltx_tag ltx_tag_equationgroup ltx_align_right">(8)</span></td>
</tr>
<tr class="ltx_equation ltx_eqn_row ltx_align_baseline" id="S4.E8Xa">
<td class="ltx_eqn_cell ltx_eqn_center_padleft"></td>
<td class="ltx_td ltx_eqn_cell"></td>
<td class="ltx_td ltx_align_left ltx_eqn_cell"><math alttext="\displaystyle\left\{\begin{aligned} C_{\text{Last}}\left(\{f_{\theta^{k}}\}_{0%
\leq k\leq K}\right)({\bm{x}})&amp;=f_{\theta^{K}}({\bm{x}}),\qquad\quad&amp;C=C_{%
\text{Last}}\\
C_{\text{Vote}}\left(\{f_{\theta^{k}}\}_{0\leq k\leq K}\right)({\bm{x}})&amp;=%
\mathbb{E}_{k}\left[f_{\theta^{k}}({\bm{x}})\right],&amp;C=C_{\text{Vote}}\end{%
aligned}\right.." class="ltx_math_unparsed" display="inline" id="S4.E8Xa.2.1.1.m1.8"><semantics id="S4.E8Xa.2.1.1.m1.8a"><mrow id="S4.E8Xa.2.1.1.m1.8b"><mo id="S4.E8Xa.2.1.1.m1.8.9">{</mo><mtable columnspacing="0pt" id="S4.E8Xa.2.1.1.m1.8.8" rowspacing="0pt"><mtr id="S4.E8Xa.2.1.1.m1.8.8a"><mtd class="ltx_align_right" columnalign="right" id="S4.E8Xa.2.1.1.m1.8.8b"><mrow id="S4.E8Xa.2.1.1.m1.*******.2"><msub id="S4.E8Xa.2.1.1.m1.*******.2.4"><mi id="S4.E8Xa.2.1.1.m1.*******.2.4.2">C</mi><mtext id="S4.E8Xa.2.1.1.m1.*******.2.4.3">Last</mtext></msub><mo id="S4.E8Xa.2.1.1.m1.*******.2.3">⁢</mo><mrow id="S4.E8Xa.2.1.1.m1.*******.2.2.1"><mo id="S4.E8Xa.2.1.1.m1.*******.2.2.1.2">(</mo><msub id="S4.E8Xa.2.1.1.m1.*******.*******"><mrow id="S4.E8Xa.2.1.1.m1.*******.2.2.*******"><mo id="S4.E8Xa.2.1.1.m1.*******.2.2.*******.2" stretchy="false">{</mo><msub id="S4.E8Xa.2.1.1.m1.*******.2.2.*******.1"><mi id="S4.E8Xa.2.1.1.m1.*******.2.2.*******.1.2">f</mi><msup id="S4.E8Xa.2.1.1.m1.*******.2.2.*******.1.3"><mi id="S4.E8Xa.2.1.1.m1.*******.*******.1.*******">θ</mi><mi id="S4.E8Xa.2.1.1.m1.*******.*******.1.*******">k</mi></msup></msub><mo id="S4.E8Xa.2.1.1.m1.*******.2.2.*******.3" stretchy="false">}</mo></mrow><mrow id="S4.E8Xa.2.1.1.m1.*******.*******.3"><mn id="S4.E8Xa.2.1.1.m1.*******.2.2.*******">0</mn><mo id="S4.E8Xa.2.1.1.m1.*******.2.2.*******">≤</mo><mi id="S4.E8Xa.2.1.1.m1.*******.2.2.*******">k</mi><mo id="S4.E8Xa.2.1.1.m1.*******.2.2.*******">≤</mo><mi id="S4.E8Xa.2.1.1.m1.*******.2.2.*******">K</mi></mrow></msub><mo id="S4.E8Xa.2.1.1.m1.*******.2.2.1.3">)</mo></mrow><mo id="S4.E8Xa.2.1.1.m1.*******.2.3a">⁢</mo><mrow id="S4.E8Xa.2.1.1.m1.*******.2.5.2"><mo id="S4.E8Xa.2.1.1.m1.*******.2.5.2.1" stretchy="false">(</mo><mi id="S4.E8Xa.2.1.1.m*******.1.1.1">𝒙</mi><mo id="S4.E8Xa.2.1.1.m1.*******.2.5.2.2" stretchy="false">)</mo></mrow></mrow></mtd><mtd class="ltx_align_left" columnalign="left" id="S4.E8Xa.2.1.1.m1.8.8c"><mrow id="S4.E8Xa.2.1.1.m1.4.4.4.4.2.2"><mrow id="S4.E8Xa.2.1.1.m1.4.4.4.4.2.2.1"><mi id="S4.E8Xa.2.1.1.m1.4.4.4.4.2.2.1.2"></mi><mo id="S4.E8Xa.2.1.1.m1.4.4.4.4.*******">=</mo><mrow id="S4.E8Xa.2.1.1.m1.4.4.4.4.2.2.1.3"><msub id="S4.E8Xa.2.1.1.m1.4.4.4.4.2.2.1.3.2"><mi id="S4.E8Xa.2.1.1.m1.4.4.4.4.2.2.*******">f</mi><msup id="S4.E8Xa.2.1.1.m1.4.4.4.4.2.2.*******"><mi id="S4.E8Xa.2.1.1.m1.4.4.4.4.2.2.*******.2">θ</mi><mi id="S4.E8Xa.2.1.1.m1.4.4.4.4.2.2.*******.3">K</mi></msup></msub><mo id="S4.E8Xa.2.1.1.m1.4.4.4.4.2.2.1.3.1">⁢</mo><mrow id="S4.E8Xa.2.1.1.m1.4.4.4.4.2.2.1.3.3.2"><mo id="S4.E8Xa.2.1.1.m1.4.4.4.4.2.2.1.3.3.2.1" stretchy="false">(</mo><mi id="S4.E8Xa.2.1.1.m1.3.3.3.3.1.1">𝒙</mi><mo id="S4.E8Xa.2.1.1.m1.4.4.4.4.2.2.1.*******" stretchy="false">)</mo></mrow></mrow></mrow><mo id="S4.E8Xa.2.1.1.m1.4.4.4.4.2.2.2">,</mo></mrow></mtd><mtd class="ltx_align_right" columnalign="right" id="S4.E8Xa.2.1.1.m1.8.8d"><mrow id="S4.E8Xa.2.1.1.m1.4.4.4.5.1"><mi id="S4.E8Xa.2.1.1.m1.4.4.4.5.1.2">C</mi><mo id="S4.E8Xa.2.1.1.m1.4.4.4.5.1.1">=</mo><msub id="S4.E8Xa.2.1.1.m1.4.4.4.5.1.3"><mi id="S4.E8Xa.2.1.1.m1.4.4.4.5.1.3.2">C</mi><mtext id="S4.E8Xa.2.1.1.m1.4.4.4.5.1.3.3">Last</mtext></msub></mrow></mtd></mtr><mtr id="S4.E8Xa.2.1.1.m1.8.8e"><mtd class="ltx_align_right" columnalign="right" id="S4.E8Xa.2.1.1.m1.8.8f"><mrow id="S4.E8Xa.2.1.1.m1.6.6.6.2.2"><msub id="S4.E8Xa.2.1.1.m1.6.6.6.2.2.4"><mi id="S4.E8Xa.2.1.1.m1.6.6.6.*******">C</mi><mtext id="S4.E8Xa.2.1.1.m1.6.6.6.*******">Vote</mtext></msub><mo id="S4.E8Xa.2.1.1.m1.6.6.6.2.2.3">⁢</mo><mrow id="S4.E8Xa.2.1.1.m1.6.6.6.*******"><mo id="S4.E8Xa.2.1.1.m1.6.6.6.*******.2">(</mo><msub id="S4.E8Xa.2.1.1.m1.6.6.6.2.*******"><mrow id="S4.E8Xa.2.1.1.m1.6.6.6.2.2.2.*******"><mo id="S4.E8Xa.2.1.1.m1.6.6.6.2.2.2.*******.2" stretchy="false">{</mo><msub id="S4.E8Xa.2.1.1.m1.6.6.6.2.2.2.*******.1"><mi id="S4.E8Xa.2.1.1.m1.6.6.6.2.2.2.*******.1.2">f</mi><msup id="S4.E8Xa.2.1.1.m1.6.6.6.2.2.2.*******.1.3"><mi id="S4.E8Xa.2.1.1.m1.6.6.6.2.*******.1.*******">θ</mi><mi id="S4.E8Xa.2.1.1.m1.6.6.6.2.*******.1.*******">k</mi></msup></msub><mo id="S4.E8Xa.2.1.1.m1.6.6.6.2.2.2.*******.3" stretchy="false">}</mo></mrow><mrow id="S4.E8Xa.2.1.1.m1.6.6.6.2.*******.3"><mn id="S4.E8Xa.2.1.1.m1.6.6.6.2.2.2.*******">0</mn><mo id="S4.E8Xa.2.1.1.m1.6.6.6.2.2.2.*******">≤</mo><mi id="S4.E8Xa.2.1.1.m1.6.6.6.2.2.2.*******">k</mi><mo id="S4.E8Xa.2.1.1.m1.6.6.6.2.2.2.*******">≤</mo><mi id="S4.E8Xa.2.1.1.m1.6.6.6.2.2.2.*******">K</mi></mrow></msub><mo id="S4.E8Xa.2.1.1.m1.6.6.6.*******.3">)</mo></mrow><mo id="S4.E8Xa.2.1.1.m1.6.6.6.2.2.3a">⁢</mo><mrow id="S4.E8Xa.2.1.1.m1.6.6.6.2.2.5.2"><mo id="S4.E8Xa.2.1.1.m1.6.6.6.2.2.5.2.1" stretchy="false">(</mo><mi id="S4.E8Xa.2.1.1.m1.5.5.5.1.1.1">𝒙</mi><mo id="S4.E8Xa.2.1.1.m1.6.6.6.2.2.5.2.2" stretchy="false">)</mo></mrow></mrow></mtd><mtd class="ltx_align_left" columnalign="left" id="S4.E8Xa.2.1.1.m1.8.8g"><mrow id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2"><mrow id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2.1"><mi id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2.1.3"></mi><mo id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2.1.2">=</mo><mrow id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******"><msub id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.3"><mi id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2.*******">𝔼</mi><mi id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2.*******">k</mi></msub><mo id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.2">⁢</mo><mrow id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2.*******"><mo id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2.*******.2">[</mo><mrow id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2.*******.1"><msub id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2.*******.1.2"><mi id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2.*******.1.2.2">f</mi><msup id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2.*******.1.2.3"><mi id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2.*******.*******">θ</mi><mi id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2.*******.1.2.3.3">k</mi></msup></msub><mo id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2.*******.1.1">⁢</mo><mrow id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.1.*******"><mo id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.1.*******.1" stretchy="false">(</mo><mi id="S4.E8Xa.2.1.1.m1.7.7.7.3.1.1">𝒙</mi><mo id="S4.E8Xa.2.1.1.m1.8.8.8.4.*******.1.*******.2" stretchy="false">)</mo></mrow></mrow><mo id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2.*******.3">]</mo></mrow></mrow></mrow><mo id="S4.E8Xa.2.1.1.m1.8.8.8.4.2.2.2">,</mo></mrow></mtd><mtd class="ltx_align_right" columnalign="right" id="S4.E8Xa.2.1.1.m1.8.8h"><mrow id="S4.E8Xa.2.1.1.m1.8.8.8.5.1"><mi id="S4.E8Xa.2.1.1.m1.8.8.8.5.1.2">C</mi><mo id="S4.E8Xa.2.1.1.m1.8.8.8.5.1.1">=</mo><msub id="S4.E8Xa.2.1.1.m1.8.8.8.5.1.3"><mi id="S4.E8Xa.2.1.1.m1.8.8.8.5.1.3.2">C</mi><mtext id="S4.E8Xa.2.1.1.m1.8.8.8.5.1.3.3">Vote</mtext></msub></mrow></mtd></mtr></mtable><mo id="S4.E8Xa.2.1.1.m1.8.10" lspace="0em">.</mo></mrow><annotation encoding="application/x-tex" id="S4.E8Xa.2.1.1.m1.8c">\displaystyle\left\{\begin{aligned} C_{\text{Last}}\left(\{f_{\theta^{k}}\}_{0%
\leq k\leq K}\right)({\bm{x}})&amp;=f_{\theta^{K}}({\bm{x}}),\qquad\quad&amp;C=C_{%
\text{Last}}\\
C_{\text{Vote}}\left(\{f_{\theta^{k}}\}_{0\leq k\leq K}\right)({\bm{x}})&amp;=%
\mathbb{E}_{k}\left[f_{\theta^{k}}({\bm{x}})\right],&amp;C=C_{\text{Vote}}\end{%
aligned}\right..</annotation><annotation encoding="application/x-llamapun" id="S4.E8Xa.2.1.1.m1.8d">{ start_ROW start_CELL italic_C start_POSTSUBSCRIPT Last end_POSTSUBSCRIPT ( { italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT end_POSTSUBSCRIPT } start_POSTSUBSCRIPT 0 ≤ italic_k ≤ italic_K end_POSTSUBSCRIPT ) ( bold_italic_x ) end_CELL start_CELL = italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUPERSCRIPT italic_K end_POSTSUPERSCRIPT end_POSTSUBSCRIPT ( bold_italic_x ) , end_CELL start_CELL italic_C = italic_C start_POSTSUBSCRIPT Last end_POSTSUBSCRIPT end_CELL end_ROW start_ROW start_CELL italic_C start_POSTSUBSCRIPT Vote end_POSTSUBSCRIPT ( { italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT end_POSTSUBSCRIPT } start_POSTSUBSCRIPT 0 ≤ italic_k ≤ italic_K end_POSTSUBSCRIPT ) ( bold_italic_x ) end_CELL start_CELL = blackboard_E start_POSTSUBSCRIPT italic_k end_POSTSUBSCRIPT [ italic_f start_POSTSUBSCRIPT italic_θ start_POSTSUPERSCRIPT italic_k end_POSTSUPERSCRIPT end_POSTSUBSCRIPT ( bold_italic_x ) ] , end_CELL start_CELL italic_C = italic_C start_POSTSUBSCRIPT Vote end_POSTSUBSCRIPT end_CELL end_ROW .</annotation></semantics></math></td>
<td class="ltx_eqn_cell ltx_eqn_center_padright"></td>
</tr>
</tbody>
</table>
<p class="ltx_p" id="S4.SS2.p3.7">Note that RA-Labeling can be seamlessly incorporated into existing machine learning methods to mitigate the negative consequences of learning from noisy labels. The pseudocode of RA-Labeling is shown in Algorithm <a class="ltx_ref" href="https://arxiv.org/html/2107.11972v4#alg2" title="Algorithm 2 ‣ 4.2 Iterative Refinement Labeling (RA-Labeling) ‣ 4 LARA: The Proposed Framework ‣ Trade When Opportunity Comes: Price Movement Forecasting via Locality-Aware Attention and Iterative Refinement Labeling"><span class="ltx_text ltx_ref_tag">2</span></a>.</p>
</div>
</section>
</section>
<section class="ltx_section" id="S6">
<h2 class="ltx_title ltx_title_section">
<span class="ltx_tag ltx_tag_section">6 </span>Conclusion</h2>
<div class="ltx_para" id="S6.p1">
<p class="ltx_p" id="S6.p1.1">We study the problem of price movement forecasting and propose the LARA (<span class="ltx_text ltx_framed ltx_framed_underline" id="S6.p1.1.1">L</span>ocality-Aware <span class="ltx_text ltx_framed ltx_framed_underline" id="S6.p1.1.2">A</span>ttention and Iterative <span class="ltx_text ltx_framed ltx_framed_underline" id="S6.p1.1.3">R</span>efinement L<span class="ltx_text ltx_framed ltx_framed_underline" id="S6.p1.1.4">a</span>beling) framework. In LARA, we introduce LA-Attention to extract potentially profitable samples and RA-Labeling to adaptively refine the noisy labels of potentially profitable samples.
Extensive experiments on three real-world financial markets showcase its superior performance over time-series analysis methods, machine learning based models and noisy labels methods.
Besides, we also illustrate how each component works by comprehensive ablation studies, which indicates that LARA indeed captures more reliable trading opportunities.
We expect our work to pave the way for price movement forecasting in more realistic quantitative trading scenarios.</p>
</div>
</section>
<section class="ltx_section" id="Sx1">
<h2 class="ltx_title ltx_title_section">Acknowledgements</h2>
<div class="ltx_para" id="Sx1.p1">
<p class="ltx_p" id="Sx1.p1.1">Jian Li, Liang Zeng, Lei Wang and Hui Niu were supported in part by the National Natural Science Foundation of China Grant 62161146004.</p>
</div>
</section>
<section class="ltx_section" id="Sx2">
<h2 class="ltx_title ltx_title_section">Contribution Statement</h2>
<div class="ltx_para" id="Sx2.p1">
<p class="ltx_p" id="Sx2.p1.1">Liang Zeng and Lei Wang have made equal and significant contributions to this work, including the methods, implementation, and paper writing.
Hui Niu conducted the literature review and implemented the evaluation framework.
Ruchen Zhang and Ling Wang provided the source data to support this work.
Jian Li, as the corresponding author, contributed to the idea and paper writing, as well as providing computing resources.</p>
</div>
</section>