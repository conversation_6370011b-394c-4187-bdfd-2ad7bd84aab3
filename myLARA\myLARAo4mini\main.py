# main.py
import yaml
import numpy as np
from utils import load_eth, featurize, compute_labels, evaluate
from lara import LARA

def split_by_dates(ts, periods):
    train_mask = (ts>=periods[0])&(ts<=periods[1])
    valid_mask = (ts>=periods[2])&(ts<=periods[3])
    return train_mask, valid_mask

def main():
    # 0) load config
    cfg = yaml.safe_load(open("config.yaml"))
    # 1) load data → features X, timestamps ts
    df = load_eth(cfg["data"]["path"])
    X_all, ts = featurize(df, cfg["features"]["lookback"])
    # 2) compute labels
    ret = compute_labels(df, lookahead=1)
    ret = ret.iloc[cfg["features"]["lookback"]:].reset_index(drop=True).values
    # 3) split train/valid/test
    p = cfg["data"]
    train_mask = (ts>=p["train_period"][0])&(ts<=p["train_period"][1])
    valid_mask = (ts>=p["valid_period"][0])&(ts<=p["valid_period"][1])
    test_mask  = (ts>=p["test_period"][0])&(ts<=p["test_period"][1])
    # 4) prepare ping (mid) for train
    X_train_all = X_all[train_mask]
    y_train_all = ret[train_mask]
    # 首次 equal-sample + ping 由 LARA 内部处理
    # 5) valid/test 用全部样本，不做 equal
    X_valid = X_all[valid_mask]
    y_valid = ret[valid_mask]
    X_test  = X_all[test_mask]
    y_test  = ret[test_mask]
    # 6) fit LARA
    lara = LARA(cfg)
    lara.fit(X_train_all, y_train_all, None, None)
    # 7) 预测 valid/test
    pred_valid = lara.predict(X_valid)
    pred_test  = lara.predict(X_test)
    # 8) 评价
    prec_v, wlr_v, ar_v = evaluate(pred_valid, y_valid, cfg["evaluation"]["topk"])
    prec_t, wlr_t, ar_t = evaluate(pred_test,  y_test,  cfg["evaluation"]["topk"])
    print("VALID: precision=%.4f, wlr=%.4f, avg_ret=%.4f" % (prec_v, wlr_v, ar_v))
    print("TEST : precision=%.4f, wlr=%.4f, avg_ret=%.4f" % (prec_t, wlr_t, ar_t))

if __name__=="__main__":
    main()
