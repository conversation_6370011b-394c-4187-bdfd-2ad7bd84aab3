import numpy as np
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
plt.rcParams['font.family'] = ['SimHei']  # 或 ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号'-'显示为方块的问题

def visualize_samples_(X, y,sample_num=1000,binary_y=False):
    """
    将 158 维的 X 使用 PCA 降维至二维，
    并利用原始的 y 值作为第三个维度进行 3D 散点图绘制
    """
    # 随机抽样
    n = len(y)
    if n > sample_num:
        idx = np.random.choice(n, sample_num, replace=False)
        X = X[idx]
        y = y[idx]

    # 二值化y并设置颜色
    if binary_y:
        y_bin = (y > 0).astype(int)
        colors = ['red' if v == 0 else 'green' for v in y_bin]
        color_map = None
        c = colors
    else:
        color_map = 'jet'
        c = y

    # 使用 PCA 降维到 2 维
    pca = PCA(n_components=2)
    X_2d = pca.fit_transform(X)
    
    # 创建 3D 图形，并将 PCA 结果和 y 值组合进行绘图
    fig = plt.figure(figsize=(8, 6))
    ax = fig.add_subplot(111, projection='3d')
    # 使用 y 值作为颜色映射，也可以直接用它作为 z 坐标
    sc = ax.scatter(X_2d[:, 0], X_2d[:, 1], y, c=c, cmap=color_map, marker='o')
    if not binary_y:
        plt.colorbar(sc, ax=ax, pad=0.1, label='y 值')

    ax.set_xlabel("PCA组件1")
    ax.set_ylabel("PCA组件2")
    ax.set_zlabel("y 值")
    ax.set_title("样本 3D 可视化结果")
    plt.show()



def visualize_samples(processed_df,label_name,sample_num, binary_y=False):
    feature_cols = [col for col in processed_df.columns if col.startswith('feature_')]
    X = processed_df[feature_cols].values
    y = processed_df[label_name].values  # 替换成实际的y列名
    visualize_samples_(X, y, sample_num, binary_y)


def main():
    # 示例：生成随机数据
    num_samples = 1000      # 样本数量
    num_features = 158     # 维度数
    X = np.random.rand(num_samples, num_features)  # 随机生成的 158 维数据
    y = np.random.uniform(-1, 1, num_samples)        # y 在 -1 到 1 之间

    # 调用可视化函数
    visualize_samples_(X, y, binary_y=False)  # 彩色
    visualize_samples_(X, y, binary_y=True)   # 红绿

if __name__ == '__main__':
    main()