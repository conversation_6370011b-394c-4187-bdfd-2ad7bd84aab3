import torch
import numpy as np
from torch.utils.data import Dataset

class FinanceDataset(Dataset):
    def __init__(self, features, labels, gt_labels=None, indices=None, transform=None):
        """
        金融数据集类
        
        参数:
            features: 金融特征数据 (158个特征)
            labels: 标签数据（可能包含噪声）
            gt_labels: 真实标签（如果有）
            indices: 样本索引
            transform: 数据转换函数
        """
        self.features = torch.FloatTensor(features)
        self.labels = torch.LongTensor(labels)
        self.indices = indices if indices is not None else np.arange(len(features))
        self.transform = transform
        
        # 如果有真实标签，使用它；否则假设标签是正确的
        if gt_labels is not None:
            self.labels_gt = torch.LongTensor(gt_labels)
        else:
            self.labels_gt = self.labels.clone()
        
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        feature = self.features[idx]
        label = self.labels[idx]
        index = self.indices[idx]
        label_gt = self.labels_gt[idx]
        
        if self.transform:
            feature = self.transform(feature)
            
        return feature, label, index, label_gt