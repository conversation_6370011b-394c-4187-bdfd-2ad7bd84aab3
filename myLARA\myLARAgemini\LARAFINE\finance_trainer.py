import torch
import torch.nn.functional as F
import matplotlib.pyplot as plt
from finance_dataset import FinanceDataset
from sample_selection import get_features, get_score, get_singular_vector, select_clean_samples
from torch.utils.data import DataLoader
from tqdm import tqdm
class FinanceTrainer:
    def __init__(self, model, criterion, optimizer, train_loader, valid_loader=None, 
                 device='cuda', warmup_epochs=5, selection_interval=5):
        """
        金融数据动态训练器
        
        参数:
            model: 神经网络模型
            criterion: 损失函数
            optimizer: 优化器
            train_loader: 训练数据加载器
            valid_loader: 验证数据加载器
            device: 训练设备
            warmup_epochs: 预热训练轮数
            selection_interval: 样本选择间隔轮数
        """
        self.model = model.to(device)
        self.criterion = criterion
        self.optimizer = optimizer
        self.train_loader = train_loader
        self.valid_loader = valid_loader
        self.device = device
        self.warmup_epochs = warmup_epochs
        self.selection_interval = selection_interval
        
        # 保存原始数据加载器，用于样本选择
        self.orig_loader = train_loader
        
        # 当前选择的干净样本索引
        self.clean_indices = None
        
        # 训练历史记录
        self.train_loss_history = []
        self.train_acc_history = []
        self.valid_loss_history = []
        self.valid_acc_history = []
        
    def train(self, epochs):
        """
        训练模型
        
        参数:
            epochs: 训练轮数
        """
        for epoch in range(1, epochs + 1):
            # 预热阶段或动态选择阶段
            if epoch <= self.warmup_epochs:
                # 预热阶段：使用所有数据
                train_loss, train_acc = self._train_epoch(epoch, self.train_loader)
                print(f'Epoch {epoch}/{epochs} - Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}')
            else:
                # 动态选择阶段：每隔selection_interval轮选择一次干净样本
                if (epoch - self.warmup_epochs) % self.selection_interval == 1 or self.clean_indices is None:
                    self.clean_indices = self._select_clean_samples()
                    clean_loader = self._create_clean_loader(self.clean_indices)
                else:
                    clean_loader = self.train_loader
                
                # 使用选择的干净样本训练
                train_loss, train_acc = self._train_epoch(epoch, clean_loader)
                print(f'Epoch {epoch}/{epochs} - Selected {len(self.clean_indices)} clean samples out of {len(self.orig_loader.dataset)}')
                print(f'Epoch {epoch}/{epochs} - Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}')
            
            # 验证
            if self.valid_loader:
                valid_loss, valid_acc = self._validate_epoch(epoch)
                print(f'Epoch {epoch}/{epochs} - Valid Loss: {valid_loss:.4f}, Valid Acc: {valid_acc:.4f}')
                
                # 记录历史
                self.train_loss_history.append(train_loss)
                self.train_acc_history.append(train_acc)
                self.valid_loss_history.append(valid_loss)
                self.valid_acc_history.append(valid_acc)
            else:
                # 记录历史
                self.train_loss_history.append(train_loss)
                self.train_acc_history.append(train_acc)
    
    def _train_epoch(self, epoch, dataloader):
        """
        单轮训练
        """
        self.model.train()
        total_loss = 0
        correct = 0
        total = 0
        
        with tqdm(dataloader, desc=f'Train epoch {epoch}') as progress:
            for data, label, indices, _ in progress:
                data, label = data.to(self.device), label.to(self.device)
                
                # 前向传播
                _, logits = self.model(data)
                
                # 计算损失
                loss = self.criterion(logits, label, indices)
                
                # 反向传播和优化
                self.optimizer.zero_grad()
                loss.backward()
                self.optimizer.step()
                
                # 统计
                total_loss += loss.item()
                _, predicted = logits.max(1)
                total += label.size(0)
                correct += predicted.eq(label).sum().item()
                
                # 更新进度条
                progress.set_postfix(loss=loss.item(), acc=100.*correct/total)
        
        return total_loss / len(dataloader), correct / total
    
    def _validate_epoch(self, epoch):
        """
        验证
        """
        self.model.eval()
        total_loss = 0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data, label, _, _ in self.valid_loader:
                data, label = data.to(self.device), label.to(self.device)
                
                # 前向传播
                _, logits = self.model(data)
                
                # 计算损失
                loss = F.cross_entropy(logits, label)
                
                # 统计
                total_loss += loss.item()
                _, predicted = logits.max(1)
                total += label.size(0)
                correct += predicted.eq(label).sum().item()
        
        return total_loss / len(self.valid_loader), correct / total
    
    def _select_clean_samples(self):
        """
        选择干净样本
        """
        # 获取特征和标签
        features, labels = get_features(self.model, self.orig_loader)
        
        # 计算每个类别的主奇异向量
        singular_vector_dict = get_singular_vector(features, labels)
        
        # 计算样本分数
        scores = get_score(singular_vector_dict, features, labels)
        
        # 选择干净样本
        clean_indices = select_clean_samples(scores, labels, method='gmm', p_threshold=0.5)
        
        return clean_indices
    
    def _create_clean_loader(self, clean_indices):
        """
        创建只包含干净样本的数据加载器
        """
        # 获取原始数据集
        dataset = self.orig_loader.dataset
        
        # 创建新的数据集，只包含干净样本
        clean_features = dataset.features[clean_indices]
        clean_labels = dataset.labels[clean_indices]
        
        # 创建新的数据集
        clean_dataset = FinanceDataset(
            clean_features.numpy(), 
            clean_labels.numpy(),
            gt_labels=dataset.labels_gt[clean_indices].numpy() if hasattr(dataset, 'labels_gt') else None,
            indices=clean_indices
        )
        
        # 创建新的数据加载器
        clean_loader = DataLoader(
            clean_dataset,
            batch_size=self.orig_loader.batch_size,
            shuffle=True,
            num_workers=self.orig_loader.num_workers if hasattr(self.orig_loader, 'num_workers') else 0
        )
        
        return clean_loader
    
    def plot_history(self):
        """
        绘制训练历史
        """
        plt.figure(figsize=(12, 5))
        
        # 绘制损失
        plt.subplot(1, 2, 1)
        plt.plot(self.train_loss_history, label='Train Loss')
        if self.valid_loader:
            plt.plot(self.valid_loss_history, label='Valid Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        
        # 绘制准确率
        plt.subplot(1, 2, 2)
        plt.plot(self.train_acc_history, label='Train Acc')
        if self.valid_loader:
            plt.plot(self.valid_acc_history, label='Valid Acc')
        plt.xlabel('Epoch')
        plt.ylabel('Accuracy')
        plt.legend()
        
        plt.tight_layout()
        plt.show()