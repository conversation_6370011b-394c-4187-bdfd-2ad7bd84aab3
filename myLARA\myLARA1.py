import os
import numpy as np
import pandas as pd
import lightgbm as lgb
import hnswlib
import random
import argparse
from sklearn.preprocessing import StandardScaler
from metric_learn import SDML_Supervised

def set_seed(seed):
    # 设置随机种子，保证结果可复现
    np.random.seed(seed)
    random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)

def compute_features(df):
    """
    对原始K线数据进行特征工程：
      • 按日期排序
      • 计算“下一日收益率”（利用close价格的后移）
      • 计算移动平均线MA5、MA10
      • 计算近5日波动率（标准差）
    """
    df = df.sort_values(by='date').reset_index(drop=True)
    df['return'] = df['close'].shift(-1) / df['close'] - 1
    df['MA5'] = df['close'].rolling(window=5).mean()
    df['MA10'] = df['close'].rolling(window=10).mean()
    df['vol'] = df['close'].rolling(window=5).std()
    df = df.dropna().reset_index(drop=True)
    return df

def generate_labels(df, threshold=0.001):
    """
    根据收益率生成二元标签：
      收益率大于阈值则视为上涨（1），否则为0
    """
    df['label'] = (df['return'] > threshold).astype(int)
    return df

def apply_metric_learning_with_scaler(X_train, y_train, X_test, metric_method='SDML_Supervised'):
    """
    先使用StandardScaler对数据进行归一化，
    然后采用metric_learn中的SDML_Supervised对训练数据拟合度量模型，
    并将训练集和测试集转换到新的度量空间中。
    """
    scaler = StandardScaler().fit(X_train)
    X_train_scaled = scaler.transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    if metric_method == 'SDML_Supervised':
        model = SDML_Supervised(sparsity_param=0.1, balance_param=0.001, num_constraints=1000)
    else:
        model = SDML_Supervised(sparsity_param=0.1, balance_param=0.001, num_constraints=1000)
    model.fit(X_train_scaled, y_train)
    X_train_trans = model.transform(X_train_scaled)
    X_test_trans = model.transform(X_test_scaled)
    return X_train_trans, X_test_trans, model, scaler

def apply_knn_filter(X_train_trans, y_train, X_test_trans, k=10, space='l2'):
    """
    基于hnswlib构建近似最近邻索引，对测试样本执行KNN查询，
    按照训练样本中近邻多数为正标签（≥0.5均值）的原则过滤测试样本。
    """
    dim = X_train_trans.shape[1]
    num_elements = X_train_trans.shape[0]
    index = hnswlib.Index(space=space, dim=dim)
    index.init_index(max_elements=num_elements, ef_construction=50, M=16)
    index.add_items(X_train_trans, np.arange(num_elements))
    index.set_ef(10)
    filtered_indices = []
    for i in range(X_test_trans.shape[0]):
        nn, _ = index.knn_query(X_test_trans[i], k=k)
        # 取出近邻样本对应的标签（y_train在训练集中保持原序）
        neighbor_labels = y_train[nn[0]]
        if np.mean(neighbor_labels) >= 0.5:  # 保留近邻中正标签占多数的样本
            filtered_indices.append(i)
    X_test_filtered = X_test_trans[filtered_indices]
    return X_test_filtered, filtered_indices

def dual_labeling(X_train_trans, train_preds, X_test_trans, iterations=5, dual_ratio=0.01):
    """
    模拟Dual Labeling过程：
      • 初始时采用当前模型对训练样本预测概率生成标签
      • 迭代训练LightGBM模型，并依据预测分布调整标签（例如取预测概率的上(dual_ratio)分位作为阈值）
      • 最后对测试集预测时将所有模型的预测结果取平均
    """
    gbm_list = []
    # 初始标签采用当前模型预测 (二值化)
    train_labels = (train_preds > 0.5).astype(int)
    train_data = X_train_trans.copy()
    for i in range(iterations):
        dtrain = lgb.Dataset(train_data, label=train_labels)
        params = {
            'objective': 'binary',
            'boosting_type': 'gbdt',
            'metric': 'binary_logloss',
            'seed': 0,
            'verbose': -1
        }
        gbm = lgb.train(params, dtrain, num_boost_round=50)
        preds = gbm.predict(train_data)
        threshold = np.quantile(preds, 1 - dual_ratio)
        new_labels = (preds > threshold).astype(int)
        changes = np.sum(train_labels != new_labels)
        print(f"Dual labeling iteration {i+1}, 标签变化数: {changes}")
        train_labels = new_labels
        gbm_list.append(gbm)
    test_preds = np.mean([gbm.predict(X_test_trans) for gbm in gbm_list], axis=0)
    return test_preds

class LARA:
    """
    LARA模型复刻（含度量学习、KNN过滤、LightGBM训练、Dual Labeling）
    """
    def __init__(self, use_metric=True, metric_method='SDML_Supervised', use_dual=True, k=10, dual_iterations=5, dual_ratio=0.01):
        self.use_metric = use_metric
        self.metric_method = metric_method
        self.use_dual = use_dual
        self.k = k
        self.dual_iterations = dual_iterations
        self.dual_ratio = dual_ratio
        self.metric_model = None
        self.scaler = None
        self.lgb_model = None
        self.X_train_trans = None
        self.y_train = None
        
    def fit(self, X_train, y_train):
        self.y_train = y_train
        if self.use_metric:
            X_train_trans, _, metric_model, scaler = apply_metric_learning_with_scaler(X_train, y_train, X_train, metric_method=self.metric_method)
            self.metric_model = metric_model
            self.scaler = scaler
            self.X_train_trans = X_train_trans
            X_used = X_train_trans
        else:
            self.scaler = StandardScaler().fit(X_train)
            X_train_trans = self.scaler.transform(X_train)
            self.X_train_trans = X_train_trans
            X_used = X_train_trans
        dtrain = lgb.Dataset(X_used, label=y_train)
        params = {
            'objective': 'binary',
            'boosting_type': 'gbdt',
            'metric': 'binary_logloss',
            'seed': 0,
            'verbose': -1
        }
        self.lgb_model = lgb.train(params, dtrain, num_boost_round=100)
        
    def predict(self, X_test):
        X_test_scaled = self.scaler.transform(X_test)
        if self.use_metric and self.metric_model is not None:
            X_test_trans = self.metric_model.transform(X_test_scaled)
        else:
            X_test_trans = X_test_scaled
        # 应用KNN过滤：根据训练样本的标签分布过滤测试样本
        X_test_filtered, filtered_indices = apply_knn_filter(self.X_train_trans, self.y_train, X_test_trans, k=self.k, space='l2')
        # 使用Dual Labeling对测试集进行预测精炼
        if self.use_dual:
            train_preds = self.lgb_model.predict(self.X_train_trans)
            preds = dual_labeling(self.X_train_trans, train_preds, X_test_filtered, iterations=self.dual_iterations, dual_ratio=self.dual_ratio)
        else:
            preds = self.lgb_model.predict(X_test_trans)
        return preds, filtered_indices

def main():
    parser = argparse.ArgumentParser(description="完整复刻LARA方法用于ETH历史K线数据")
    parser.add_argument("--data_path", type=str, default="eth_kline.csv", help="ETH历史K线数据CSV文件路径")
    args = parser.parse_args()
    
    # 加载数据（CSV文件中要求包含date, open, high, low, close, volume字段）
    df = pd.read_csv(args.data_path)
    print("原始数据维度:", df.shape)
    df = compute_features(df)
    df = generate_labels(df, threshold=0.001)
    print("特征工程后数据维度:", df.shape)
    
    # 选择特征列（例如：close价格、MA5、MA10、波动率）
    features = ['close', 'MA5', 'MA10', 'vol']
    target = 'label'
    X = df[features].values
    y = df[target].values
    
    # 按时间顺序分割训练集与测试集（例如70%训练，30%测试）
    split_idx = int(len(X) * 0.7)
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]
    print(f"训练样本数: {len(X_train)}, 测试样本数: {len(X_test)}")
    
    set_seed(0)
    # 初始化并训练LARA模型（复刻了度量学习、KNN过滤、LightGBM、Dual Labeling所有流程）
    model = LARA(use_metric=True, metric_method='SDML_Supervised', use_dual=True, k=10, dual_iterations=5, dual_ratio=0.01)
    model.fit(X_train, y_train)
    
    # 对测试集进行预测
    preds, filtered_indices = model.predict(X_test)
    preds_binary = (preds > 0.5).astype(int)
    
    # 针对KNN过滤后保留的测试样本计算准确率
    if filtered_indices:
        y_test_filtered = y_test[filtered_indices]
    else:
        y_test_filtered = y_test
    accuracy = np.mean(preds_binary == y_test_filtered)
    print("测试集准确率:", accuracy)
    
    # 保存预测结果
    result_df = pd.DataFrame({
        "actual": y_test_filtered,
        "prediction": preds_binary
    })
    result_df.to_csv("lara_eth_predictions.csv", index=False)
    print("预测结果已保存到 lara_eth_predictions.csv")

if __name__ == "__main__":
    main()