# 假设df是您的金融数据DataFrame，包含158个特征和1个标签列
# 标签列应该是最后一列
import pandas as pd

# 加载数据
# df = pd.read_csv('finance_data.csv')

# 示例：创建一个随机的金融数据集
import numpy as np
from trainer import train,get_high_confidence_samples


def main():
    # 创建随机特征和标签
    num_samples = 10000
    num_features = 158
    #X = np.random.randn(num_samples, num_features)
    ## 根据要求，X大于3时y为1，X小于-3时y为0，其余随机
    #y = np.random.randint(0, 2, num_samples)
    #y[np.any(X > 2, axis=1)] = 1
    #y[np.any(X < -2, axis=1)] = 0
    # 从pkl文件读取数据
    df = pd.read_pickle('data/BTCUSDT_BINANCE_2024-01-01_00_00_00_2025-06-01_00_00_00_features.pkl')
    future_periods=96
    label_column_name = f'label_fwd_return_{future_periods}'
    df[label_column_name] = (df['close'].shift(-future_periods) / df['close']) - 1
    feature_cols = [col for col in df.columns if col.startswith('feature_')]
    X = df[feature_cols].values
    y = (df[label_column_name].values > 0).astype(int)
    

    # 添加一些噪声标签（约20%）
    #noise_indices = np.random.choice(num_samples, size=int(num_samples*0.2), replace=False)
    #y[noise_indices] = 1 - y[noise_indices]

    # 创建DataFrame
    columns = [f'feature_{i}' for i in range(num_features)] + ['label']
    df = pd.DataFrame(np.column_stack([X, y]), columns=columns)

    # 训练模型并获取干净样本
    model, clean_indices = train(df, num_epochs=2, loss_type='elr')

    # 获取高置信度样本
    high_conf_df = get_high_confidence_samples(df, model, clean_indices, confidence_threshold=0.9)

    # 输出高置信度样本数量
    print(f'高置信度样本数量: {len(high_conf_df)}')

    # 计算高置信度样本中正确标签的比例（如果有真实标签）
    if 'label' in high_conf_df.columns and 'predicted_label' in high_conf_df.columns:
        accuracy = (high_conf_df['label'] == high_conf_df['predicted_label']).mean()
        print(f'高置信度样本准确率: {accuracy:.4f}')

if __name__ == '__main__':
    main()