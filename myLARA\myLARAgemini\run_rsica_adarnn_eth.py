# run_adarnn_eth.py
import os
import re
from datetime import datetime, timedelta

import numpy as np
import pandas as pd

from eth_data_processor import ETHDataProcessor
from pytorch_adarnn import ADARNN
from qlib.data.dataset.handler import DataHandlerLP

from rsica_label import label_sup_order_simple,label_sup_order,label_sup_order_simple_1
from calculate_indicator import calculate_rsica

def main():
    # ------------------------------
    # 1. 配置区
    # ------------------------------
    USE_FEATURE_TYPE = "360"  # 可选: "158" 或 "360"
    instrument_name = 'ETHUSDT'
    current_directory = os.getcwd()
    eth_kline_filepath = f'{current_directory}/data/ETHUSDT_BINANCE_1m_2025-01-01_00_00_00_2025-05-31_00_00_00.csv'
    features_pkl_158 = eth_kline_filepath.replace('.csv', '_158_features.pkl')
    features_pkl_360 = eth_kline_filepath.replace('.csv', '_360_features.pkl')

    # 从文件名提取开始/结束日期并计算 8:1:1 划分
    match = re.search(
        r'_(\d{4}-\d{2}-\d{2})_\d{2}_\d{2}_\d{2}_(\d{4}-\d{2}-\d{2})_\d{2}_\d{2}_\d{2}',
        eth_kline_filepath
    )
    if not match:
        raise ValueError(f"无法从文件名中提取日期: {eth_kline_filepath}")
    data_start = datetime.strptime(match.group(1), '%Y-%m-%d')
    data_end   = datetime.strptime(match.group(2), '%Y-%m-%d')
    total_days = (data_end - data_start).days + 1
    train_days = int(total_days * 0.8)
    valid_days = int(total_days * 0.1)
    test_days  = total_days - train_days - valid_days

    train_start = data_start
    train_end   = data_start + timedelta(days=train_days - 1)
    valid_start = train_end   + timedelta(days=1)
    valid_end   = valid_start + timedelta(days=valid_days - 1)
    test_start  = valid_end   + timedelta(days=1)
    test_end    = data_end

    print(f"Train : {train_start} ~ {train_end}")
    print(f"Valid : {valid_start} ~ {valid_end}")
    print(f"Test  : {test_start} ~ {test_end}")

    # ------------------------------
    # 2. 特征加载或生成
    # ------------------------------
    # 先把原始 CSV 读一下（下面只是为了构造 ETHDataProcessor 需要的 ohlcv）
    raw_csv = pd.read_csv(eth_kline_filepath)
    # 确保 datetime 列
    raw_csv['datetime'] = pd.to_datetime(raw_csv['datetime'])
    raw_csv = raw_csv.set_index('datetime', drop=False).sort_index()
    # 构造 ETHDataProcessor 需要的标准 OHLCV DataFrame
    ohlcv_df = pd.DataFrame({
        'datetime': raw_csv.index,
        'open':     raw_csv['open'],
        'high':     raw_csv['high'],
        'low':      raw_csv['low'],
        'close':    raw_csv['close'],
        'volume':   raw_csv.get('volume', 0.0),
    })
    ohlcv_df = ohlcv_df.set_index('datetime')
    # 根据 USE_FEATURE_TYPE 决定加载/生成哪一套特征
    if USE_FEATURE_TYPE == "158":
        if os.path.exists(features_pkl_158):
            print(f"[INFO] 加载已有158特征文件 {features_pkl_158}")
            df_feat = pd.read_pickle(features_pkl_158)
            processor = ETHDataProcessor(df_feat, instrument_name=instrument_name)
            processor.feature_columns = [c for c in df_feat.columns if c.startswith('feature_')]
        else:
            print("[INFO] 从 CSV 计算 Alpha158 特征")
            processor = ETHDataProcessor(ohlcv_df, instrument_name=instrument_name)
            processor.generate_features_alpha158()
            pd.to_pickle(processor.df, features_pkl_158)
            print(f"[INFO] 158 特征已保存到 {features_pkl_158}")
    elif USE_FEATURE_TYPE == "360":
        if os.path.exists(features_pkl_360):
            print(f"[INFO] 加载已有360特征文件 {features_pkl_360}")
            df_feat = pd.read_pickle(features_pkl_360)
            processor = ETHDataProcessor(df_feat, instrument_name=instrument_name)
            processor.feature_columns = [c for c in df_feat.columns if c.startswith('feature_')]
        else:
            print("[INFO] 从 CSV 计算 Alpha360 特征")
            processor = ETHDataProcessor(ohlcv_df, instrument_name=instrument_name)
            processor.generate_features_alpha360()
            pd.to_pickle(processor.df, features_pkl_360)
            print(f"[INFO] 360 特征已保存到 {features_pkl_360}")
    else:
        raise ValueError(f"不支持的 USE_FEATURE_TYPE: {USE_FEATURE_TYPE}")

    # ------------------------------------------------------------------
    # 3. 生成信号标签 + RSI累积面积 + 过滤
    # ------------------------------------------------------------------
    print("[INFO] 生成信号标签 label_sup_order ...")
    labels = label_sup_order_simple_1(raw_csv.reset_index(drop=True))  # 返回一个 Series, index 对应 0..N-1
    # 因为 raw 的 index 是 datetime，我们要把 labels 和 features 对齐
    # 首先把 labels 放到一个按时间索引的 Series
    label_ser = pd.Series(labels.values, index=raw_csv.index, name='rsica_return')

    # 计算 RSI 累积面积
    print("[INFO] 计算 RSI_cumulative_area ...")
    rsi_ca = calculate_rsica(raw_csv.reset_index(drop=True), rsi_period=14, rsi_threshold=30)
    rsi_ca_ser = pd.Series(rsi_ca, index=raw_csv.index, name='RSI_cumulative_area')

    # 把标签和 RSI_cum_area 合并到特征表里
    df = processor.df.copy()
    df['rsica_return'] = label_ser
    df['RSI_cumulative_area'] = rsi_ca_ser

    # 只保留那些真正触发补单的行（label 不为空）
    df = df.dropna(subset=['rsica_return'])

    # 设置好用于 LARA 的 label 列名
    processor.df = df
    processor.feature_columns = [c for c in df.columns if c.startswith('feature_')]
    processor.label_column_name = 'rsica_return'

    # 2.3 清洗 + 切分
    processed = processor.get_processed_data()
    if processed.empty:
        print("[ERROR] 处理后无数据，退出")
        return

    # 切分成 LARA 格式的 MockDatasetH
    dataset = processor.split_data_for_lara(
        processed,
        train_start.strftime('%Y-%m-%d'),
        train_end.strftime('%Y-%m-%d'),
        valid_start.strftime('%Y-%m-%d'),
        valid_end.strftime('%Y-%m-%d'),
        test_start.strftime('%Y-%m-%d'),
        test_end.strftime('%Y-%m-%d'),
    )

    # ------------------------------
    # 3. ADARNN 模型训练 & 预测
    # ------------------------------
    # 3.1 超参数（可根据需要调整）
    adarnn_params = {
        "d_feat": int(len(processor.feature_columns)/60),
        "hidden_size": 64,
        "num_layers": 2,
        "dropout": 0.0,
        "n_epochs": 200,
        "pre_epoch": 40,
        "dw": 0.5,
        "loss_type": "cosine",
        "len_seq": 60,
        "len_win": 0,
        "lr": 1e-3,
        "metric": "loss",
        "batch_size": 800,
        "early_stop": 20,
        "optimizer": "adam",
        "loss": "mse",
        "n_splits": 2,
        "GPU": 0,
        "seed": 42,
    }

    print("[INFO] 初始化 ADARNN 模型")
    model = ADARNN(**adarnn_params)

    print("[INFO] 开始训练 ADARNN")
    model.fit(dataset)
    print("[INFO] 训练完成")

    print("[INFO] 对测试集做预测")
    preds = model.predict(dataset, segment="test")  # 返回 pd.Series，索引=(datetime,instrument)
    print(preds.head())

    # ------------------------------
    # 4. 简单评估
    # ------------------------------
    test_label_df = dataset.test_dict["label"]  # DataFrame，列名为 label_fwd_return_X
    # 重命名与 preds 对齐
    test_label_df = test_label_df.rename(columns={processor.label_column_name: "label"})
    # 合并
    df_ = pd.concat([preds.rename("score"), test_label_df["label"]], axis=1, join="inner")
    if df_.empty:
        print("[WARN] 无法合并预测与真实标签")
        return

    # 取 Top-N 计算命中率 & 平均收益
    N = min(1000, len(df_))
    top_df = df_.sort_values("score", ascending=False).iloc[:N].copy()
    # hit 定义为真实收益 >= 0
    top_df["hit"] = (top_df["label"] >= 0).astype(int)
    precision = top_df["hit"].mean()
    avg_ret   = top_df["label"].mean()
    print(f"[RESULT] Top {N} Precision: {precision:.2%}, Average Return: {avg_ret:.4%}")

    # 只统计 score 大于 0.035 的所有结果
    filtered_df = df_[df_["score"] < 0.035].copy()
    if filtered_df.shape[0] > 0:
        filtered_df["hit"] = (filtered_df["label"] >= 0).astype(int)
        precision_all = filtered_df["hit"].mean()
        avg_ret_all = filtered_df["label"].mean()
    else:
        precision_all = float("nan")
        avg_ret_all = float("nan")
    print(f"[RESULT] 所有 score>0.035 样本的整体精度: {precision_all:.2%}, 平均真实收益: {avg_ret_all:.4%}")

    # 如需保存结果，可取消下列注释
    # os.makedirs("results", exist_ok=True)
    # pd.concat([preds.rename("score"), test_label_df], axis=1).to_csv("results/adarnn_eth.csv")


if __name__ == "__main__":
    main()
