import pandas as pd
import talib
import talib.abstract as ta
import numpy as np
import pandas_ta as pta


def calculate_rsi_cumulative_area(df, rsi_period=14, rsi_threshold=30):
    """
    计算RSI积累面积
    """
    # 计算RSI
    rsi = talib.RSI(df['close_price'].values, timeperiod=rsi_period)
    
    # 初始化积累面积序列
    cumulative_area = np.zeros_like(rsi, dtype=float)
    current_area = 0
    
    # 计算积累面积
    for i in range(len(rsi)):
        if pd.isna(rsi[i]):
            cumulative_area[i] = 0
            continue
            
        if rsi[i] < rsi_threshold:
            # 积累面积 = RSI阈值 - 当前RSI值
            current_area += (rsi_threshold - rsi[i])
        else:
            # RSI超过阈值时重置积累面积
            current_area = 0
        
        cumulative_area[i] = current_area
    
    return rsi, cumulative_area

def calculate_smi(df, k_length=10, d_length=3):
    # 计算周期内最高价和最低价
    df['hh'] = df['high_price'].rolling(k_length).max()
    df['ll'] = df['low_price'].rolling(k_length).min()
    
    # 计算差值
    df['diff'] = df['hh'] - df['ll']
    df['rdiff'] = df['close_price'] - (df['hh'] + df['ll']) / 2

    # 双重EMA计算
    # 第一重EMA
    ema_rdiff1 = talib.EMA(df['rdiff'].values, timeperiod=d_length)
    ema_diff1 = talib.EMA(df['diff'].values, timeperiod=d_length)
    
    # 第二重EMA
    avgrel = talib.EMA(ema_rdiff1, timeperiod=d_length)
    avgdiff = talib.EMA(ema_diff1, timeperiod=d_length)

    # 计算SMI值
    with np.errstate(divide='ignore', invalid='ignore'):
        smi = np.divide(avgrel, (avgdiff / 2)) * 100
    smi = np.nan_to_num(smi, nan=0.0)  # 处理NaN和无穷大

    # 计算信号线
    signal = talib.EMA(smi, timeperiod=d_length)

    # 将结果添加到DataFrame
    df['smi'] = signal

    # 清理中间列
    df.drop(['hh', 'll', 'diff', 'rdiff'], axis=1, inplace=True)
    
    return df['smi']

def calculate_rsica(df, rsi_period=14, rsi_threshold=30):
    """
    计算RSI积累面积
    """
    # 计算RSI
    rsi = talib.RSI(df['close'].values, timeperiod=rsi_period)
    
    # 初始化积累面积序列
    cumulative_area = np.zeros_like(rsi, dtype=float)
    current_area = 0
    
    # 计算积累面积
    for i in range(len(rsi)):
        if pd.isna(rsi[i]):
            cumulative_area[i] = 0
            continue
            
        if rsi[i] < rsi_threshold:
            # 积累面积 = RSI阈值 - 当前RSI值
            current_area += (rsi_threshold - rsi[i])
        else:
            # RSI超过阈值时重置积累面积
            current_area = 0
        
        cumulative_area[i] = current_area
    
    return cumulative_area

def calculate_rsica_reverse(df, rsi_period=14, rsi_threshold=70):
    """
    计算RSI积累面积
    """
    # 计算RSI
    rsi = talib.RSI(df['close'].values, timeperiod=rsi_period)
    
    # 初始化积累面积序列
    cumulative_area = np.zeros_like(rsi, dtype=float)
    current_area = 0
    
    # 计算积累面积
    for i in range(len(rsi)):
        if pd.isna(rsi[i]):
            cumulative_area[i] = 0
            continue
            
        if rsi[i] > rsi_threshold:
            # 积累面积 = RSI阈值 - 当前RSI值
            current_area += (rsi[i] - rsi_threshold)
        else:
            # RSI超过阈值时重置积累面积
            current_area = 0
        
        cumulative_area[i] = current_area
    
    return cumulative_area

def calculate_ema8(df,timeperiod=8):
    """
    计算8日EMA
    """
    return talib.EMA(df['close_price'], timeperiod=timeperiod)

def calculate_ema50(df,timeperiod=50):
    """
    计算50日EMA
    """
    return talib.EMA(df['close_price'], timeperiod=timeperiod)

def calculate_ema100(df,timeperiod=100):
    """
    计算100日EMA
    """
    return talib.EMA(df['close_price'], timeperiod=timeperiod)

def calculate_ema200(df,timeperiod=200):
    """
    计算200日EMA
    """
    return talib.EMA(df['close_price'], timeperiod=timeperiod)


def calculate_crsi(df:pd.DataFrame):
    crsi_closechange = df['close_price'] / df['close_price'].shift(1)
    crsi_updown = np.where(crsi_closechange.gt(1), 1.0, np.where(crsi_closechange.lt(1), -1.0, 0.0))
    crsi=(talib.RSI(df['close_price'], timeperiod=3) + talib.RSI(crsi_updown, timeperiod=2) + talib.ROC(df['close_price'], 100)) / 3
    return crsi

# Williams %R
def williams_r(dataframe: pd.DataFrame, period: int = 14) -> pd.Series:
    """Williams %R, or just %R, is a technical analysis oscillator showing the current closing price in relation to the high and low
        of the past N days (for a given N). It was developed by a publisher and promoter of trading materials, Larry Williams.
        Its purpose is to tell whether a stock or commodity market is trading near the high or the low, or somewhere in between,
        of its recent trading range.
        The oscillator is on a negative scale, from −100 (lowest) up to 0 (highest).
    """

    highest_high = dataframe["high_price"].rolling(center=False, window=period).max()
    lowest_low = dataframe["low_price"].rolling(center=False, window=period).min()

    WR = pd.Series(
        (highest_high - dataframe['close_price']) / (highest_high - lowest_low),
        name=f"{period} Williams %R",
        )

    return WR * -100

def calculate_R_96(df):
    r96=williams_r(df, period=96)
    return r96

def calculate_R_480(df):
    r480=williams_r(df, period=480)
    return r480

def range_percent_change(dataframe: pd.DataFrame, method, length: int) -> float:
        """
        Rolling Percentage Change Maximum across interval.

        :param dataframe: DataFrame The original OHLC dataframe
        :param method: High to Low / Open to Close
        :param length: int The length to look back
        """
        if method == 'HL':
            return (dataframe['high_price'].rolling(length).max() - dataframe['low_price'].rolling(length).min()) / dataframe['low_price'].rolling(length).min()
        elif method == 'OC':
            return (dataframe['open_price'].rolling(length).max() - dataframe['close_price'].rolling(length).min()) / dataframe['close_price'].rolling(length).min()
        else:
            raise ValueError(f"Method {method} not defined!")


def calculate_H1_prc_change_5(df,periods=5):
    # Range percent change or Rolling Percentage Change Maximum across interval
    return range_percent_change(df, 'HL', length=periods)

def calculate_ROC(df,periods=21):
    return talib.ROC(df['close_price'], timeperiod=periods)

def calculate_RSI(df,periods=14):
    return talib.RSI(df['close'], timeperiod=periods)

# Chaikin Money Flow
def chaikin_money_flow(dataframe:pd.DataFrame, n=20, fillna=False) -> pd.Series:
    """Chaikin Money Flow (CMF)
    It measures the amount of Money Flow Volume over a specific period.
    http://stockcharts.com/school/doku.php?id=chart_school:technical_indicators:chaikin_money_flow_cmf
    Args:
        dataframe(pandas.Dataframe): dataframe containing ohlcv
        n(int): n period.
        fillna(bool): if fill nan values.
    Returns:
        pandas.Series: New feature generated.
    """
    mfv = ((dataframe['close_price'] - dataframe['low_price']) - (dataframe['high_price'] - dataframe['close_price'])) / (dataframe['high_price'] - dataframe['low_price'])
    mfv = mfv.fillna(0.0)  # float division by zero
    mfv *= dataframe['volume']
    cmf = (mfv.rolling(n, min_periods=0).sum()
           / dataframe['volume'].rolling(n, min_periods=0).sum())
    if fillna:
        cmf = cmf.replace([np.inf, -np.inf], np.nan).fillna(0)
    return pd.Series(cmf, name='cmf')


def calculate_CMF(df,periods=20):
    return chaikin_money_flow(df, n=periods)

def T3(dataframe, length=5):
    """
    T3 Average by HPotter on Tradingview
    https://www.tradingview.com/script/qzoC9H1I-T3-Average/
    """
    df = dataframe.copy()

    df['xe1'] = ta.EMA(df['close_price'], timeperiod=length)
    df['xe2'] = ta.EMA(df['xe1'], timeperiod=length)
    df['xe3'] = ta.EMA(df['xe2'], timeperiod=length)
    df['xe4'] = ta.EMA(df['xe3'], timeperiod=length)
    df['xe5'] = ta.EMA(df['xe4'], timeperiod=length)
    df['xe6'] = ta.EMA(df['xe5'], timeperiod=length)
    b = 0.7
    c1 = -b * b * b
    c2 = 3 * b * b + 3 * b * b * b
    c3 = -6 * b * b - 3 * b - 3 * b * b * b
    c4 = 1 + 3 * b + b * b * b + 3 * b * b
    df['T3Average'] = c1 * df['xe6'] + c2 * df['xe5'] + c3 * df['xe4'] + c4 * df['xe3']

    return df['T3Average']

def calculate_T3(df,periods=5):
    return T3(df, length=periods)

def calculate_EWO(dataframe, ema_length=5, ema2_length=35):
    df = dataframe.copy()
    ema1 = ta.EMA(df['close'], timeperiod=ema_length)
    ema2 = ta.EMA(df['close'], timeperiod=ema2_length)
    emadif = (ema1 - ema2) / df['low'] * 100
    return emadif

def calculate_LOW5(df:pd.DataFrame):
    return df['low_price'].shift().rolling(5).min()

def calculate_Safe_dump_50(df:pd.DataFrame):
    #derived from H1_prc_change_5
    df['low_5'] = df['low_price'].shift().rolling(5).min()
    df['hl_pct_change_5'] = calculate_H1_prc_change_5(df,periods=5)
    sd50=((df['hl_pct_change_5'] < 0.0004) | (df['close_price'] < df['low_5']) | (df['close_price'] > df['open_price'])).astype(int)
    #sd50=(df['hl_pct_change_5'] < 0.001371 ).astype(int)
    # 清理临时列
    df.drop(['low_5', 'hl_pct_change_5'], axis=1, inplace=True)
    return sd50

def calculate_weighted_price(df: pd.DataFrame):
    """计算VWAP(Volume Weighted Average Price)"""
    # 计算典型价格 (高价+低价+收盘价)/3
    df['typical_price'] = (df['high_price'] + df['low_price'] + df['close_price']) / 3
    # 计算典型价格 * 成交量
    df['price_volume'] = df['typical_price'] * df['volume']
    
    # 计算累计值
    cumulative_pv = df['price_volume'].cumsum()
    cumulative_volume = df['volume'].cumsum()
    
    # 计算VWAP
    vwap = cumulative_pv / cumulative_volume
    
    # 清理临时列
    df.drop(['typical_price', 'price_volume'], axis=1, inplace=True)
    
    return vwap

def calculate_adx(df: pd.DataFrame, period: int = 14):
    adx=talib.ADX(df['high_price'], df['low_price'], df['close_price'], timeperiod=period)
    plus_di=talib.PLUS_DI(df['high_price'], df['low_price'], df['close_price'], timeperiod=period)
    minus_di=talib.MINUS_DI(df['high_price'], df['low_price'], df['close_price'], timeperiod=period)
    return adx, plus_di, minus_di

def calculate_atr(df: pd.DataFrame, period: int = 14):
    atr=talib.ATR(df['high'], df['low'], df['close'], timeperiod=period)
    return atr

def calculate_bollinger_bands(df: pd.DataFrame, period: int = 20, std_dev: int = 2):
    upper, middle, lower = talib.BBANDS(df['close'], timeperiod=period, nbdevup=std_dev, nbdevdn=std_dev)
    return upper, middle, lower

def calculate_stochf(df: pd.DataFrame, fastk_period: int = 5, fastd_period: int = 3):
    fastk, fastd = talib.STOCHF(df['high_price'], df['low_price'], df['close_price'], fastk_period=fastk_period, fastd_period=fastd_period)
    return fastk, fastd


def calculate_ema(df,timeperiod=8):
    return talib.EMA(df['close_price'], timeperiod=timeperiod)

def calculate_macd(df: pd.DataFrame, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9):
    macd, signal, _ = talib.MACD(df['close_price'], fastperiod=fast_period, slowperiod=slow_period, signalperiod=signal_period)
    return macd, signal

# ---------------------------------------------

def rolling_weighted_mean(series, window=200, min_periods=None):
    min_periods = window if min_periods is None else min_periods
    try:
        return series.ewm(span=window, min_periods=min_periods).mean()
    except Exception as e:  # noqa: F841
        return pd.ewma(series, span=window, min_periods=min_periods)
# ---------------------------------------------
def calculate_hma_50(df, window=50, min_periods=None):
    min_periods = window if min_periods is None else min_periods
    ma = (2 * rolling_weighted_mean(df['close_price'], window / 2, min_periods)) - rolling_weighted_mean(
        df['close_price'], window, min_periods
    )
    return rolling_weighted_mean(ma, np.sqrt(window), min_periods)
# ---------------------------------------------
def calculate_cti (df,length=20):
    return pta.cti(df['close'], length=length)

def calculate_vwap(df,length=20):
    """
    计算滚动窗口内的VWAP (Volume Weighted Average Price)
    VWAP = 滚动窗口内的(价格*成交量)之和除以成交量之和，价格通常取 (高价+低价+收盘价)/3
    参数:
      df: 包含 'high', 'low', 'close', 'volume' 的DataFrame
      length: 整数，滚动窗口大小（默认为20）
    返回:
      一个pandas.Series，包含计算得到的VWAP序列
    """
    # 计算典型价格
    typical_price = (df['high'] + df['low'] + df['close']) / 3
    # 计算典型价格乘以成交量
    pv = typical_price * df['volume']
    # 计算滚动窗口内的累计 pv 与成交量
    rolling_pv = pv.rolling(window=length).sum()
    rolling_vol = df['volume'].rolling(window=length).sum()
    # 计算VWAP
    vwap = rolling_pv / rolling_vol
    return vwap

def calculate_vwapb(df, window_size=20, num_of_std=1):
    """
    计算VWAP Bands (成交量加权平均价格带)
    参数:
      df: 包含 'high', 'low', 'close', 'volume' 的DataFrame
      window_size: 用于计算VWAP和标准差的滚动窗口大小，默认为20
      num_of_std: 标准差倍数，默认为1
    返回:
      vwap下轨, VWAP, vwap上轨
    """
    df = df.copy()
    # 使用现有的calculate_vwap函数计算滚动VWAP
    df['vwap'] = calculate_vwap(df, length=window_size)
    rolling_std = df['vwap'].rolling(window=window_size).std()
    df['vwap_low'] = df['vwap'] - (rolling_std * num_of_std)
    df['vwap_high'] = df['vwap'] + (rolling_std * num_of_std)
    return df['vwap_low'], df['vwap'], df['vwap_high']

def calculate_IMIN(df, window_size=20):
    return df['low'].rolling(window=window_size).apply(lambda x: np.argmin(x) / window_size, raw=True)


def calculate_ctica(df, cti_threshold=-0.5):
    """
    计算CTI小于给定阈值的积累面积
    """
    cti = calculate_cti(df).values
    cumulative_area = np.zeros_like(cti, dtype=float)
    current_area = 0

    for i in range(len(cti)):
        if pd.isna(cti[i]):
            cumulative_area[i] = 0
            continue

        if cti[i] < cti_threshold:
            # 积累面积 = 阈值 - 当前CTI值
            current_area += (cti_threshold - cti[i])
        else:
            # CTI超过阈值时重置积累面积
            current_area = 0

        cumulative_area[i] = current_area

    return cumulative_area

def calculate_ctica_reverse(df, cti_threshold=0.5):
    cti = calculate_cti(df).values
    cumulative_area = np.zeros_like(cti, dtype=float)
    current_area = 0

    for i in range(len(cti)):
        if pd.isna(cti[i]):
            cumulative_area[i] = 0
            continue

        if cti[i] > cti_threshold:
            # 积累面积 = 阈值 - 当前CTI值
            current_area += (cti[i]-cti_threshold)
        else:
            # CTI超过阈值时重置积累面积
            current_area = 0

        cumulative_area[i] = current_area

    return cumulative_area