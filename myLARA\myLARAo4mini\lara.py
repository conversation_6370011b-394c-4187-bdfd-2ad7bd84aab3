# lara.py
import numpy as np
import pandas as pd
from utils import (
    sample_equal, itml_learn, build_hnsw, knn_filter,
    dual_label, evaluate
)
from sklearn.preprocessing import StandardScaler
import lightgbm as lgb

class LARA:
    def __init__(self, cfg):
        self.cfg = cfg
        self.scaler = StandardScaler()

    def fit(self, X, y, X_mid, y_mid):
        # 1) 多标签样本准备（已经在外部完成过一次 equal sample，可再做一次）
        Xb, yb, Xp, yp = sample_equal(X, y, self.cfg["dual"]["pcteject"])
        # 2) 特征标准化
        Xb = pd.DataFrame(self.scaler.fit_transform(Xb), columns=Xb.columns)
        Xp = pd.DataFrame(self.scaler.transform(Xp), columns=Xp.columns)
        # 3) Metric Learning
        if self.cfg["metric_learning"]["use_multi_label"]:
            # 构造多分类 label：这里 ret> thr -> 1, ret< -thr ->0, 其它->2
            ml_y = pd.cut(y, bins=[-np.inf, -self.cfg["dual"]["pcteject"], self.cfg["dual"]["pcteject"], np.inf],
                          labels=[0,2,1]).astype(int)
            itml = itml_learn(pd.concat([Xb, Xp]), np.concatenate([yb, np.full(len(yp),2)]),
                              self.cfg["metric_learning"]["num_constraints"],
                              self.cfg["metric_learning"]["sparsity_param"],
                              self.cfg["metric_learning"]["balance_param"])
            # 变换
            Xb = pd.DataFrame(itml.transform(Xb), columns=Xb.columns)
            Xp = pd.DataFrame(itml.transform(Xp), columns=Xp.columns)
        # 4) 合并 ping 数据
        X_train = pd.concat([Xb, Xp]).reset_index(drop=True)
        y_train = np.concatenate([yb, np.full(len(yp), 0)])
        # 5) KNN 过滤
        ann = build_hnsw(X_train, ef=200)
        mask = knn_filter(ann, X_train, y_train,
                          mode=self.cfg["knn"]["mode"],
                          k=self.cfg["knn"]["k"],
                          radius=self.cfg["knn"]["radius"])
        X_train, y_train = X_train[mask], y_train[mask]
        # 6) 初始 LightGBM 训练
        dtrain = lgb.Dataset(X_train, y_train)
        self.model = lgb.train(self.cfg["lgb"]["params"], 
                               dtrain, 
                               num_boost_round=self.cfg["lgb"]["num_boost_round"])
        # 7) dual-label 迭代
        if self.cfg["dual"]["enable"]:
            self.ensemble_models, self.changes = dual_label(
                X_train.values, y_train,
                self.model,
                self.cfg["lgb"]["params"],
                rounds=self.cfg["dual"]["rounds"],
                ratio=self.cfg["dual"]["ratio"]
            )
        else:
            self.ensemble_models = [self.model]

    def predict(self, X_test):
        X_test = pd.DataFrame(self.scaler.transform(X_test), columns=X_test.columns)
        # 如果使用 ensemble
        preds = []
        for m in self.ensemble_models:
            preds.append(m.predict(X_test))
        preds = np.vstack(preds).mean(axis=0)
        return preds
