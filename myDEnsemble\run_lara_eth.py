# run_lara_eth.py
import pandas as pd
import numpy as np
import os
from eth_data_processor import ETHDataProcessor # 确保 eth_data_processor.py 在同一目录或PYTHONPATH
from LARA import LARA # 确保 LARA.py 等模块已按第1部分调整并在同一目录或PYTHONPATH

from visualize_samples import visualize_samples

def main():
    # --- 1. 配置 ---
    instrument_name = 'ETHUSDT'
    # 假设您的ETH K线数据在一个CSV文件中
    eth_kline_filepath = 'data/ETHUSDT_BINANCE_1m_2025-01-01_00_00_00_2025-05-31_00_00_00.csv'
    features_pkl_filepath = eth_kline_filepath.replace('.csv', '_features.pkl')

    '''
    #------------------------------------------------------------
    # 示例：创建一个假的OHLCV数据用于演示
    date_rng = pd.date_range(start='2020-01-01', end='2023-12-31', freq='H')
    ohlcv_data = pd.DataFrame(date_rng, columns=['datetime'])
    # 设置初始open价格
    initial_open = 1500
    ohlcv_data['open'] = np.nan
    ohlcv_data['close'] = np.nan
    ohlcv_data['high'] = np.nan
    ohlcv_data['low'] = np.nan

    # 生成K线数据
    for i in range(len(ohlcv_data)):
        if i == 0:
            open_price = initial_open
        else:
            open_price = ohlcv_data.at[i-1, 'close']
        # 高斯分布波动
        delta = np.random.normal(loc=0, scale=5)  # 平均0，标准差20
        close_price = open_price + delta
        high_price = max(open_price, close_price) + abs(np.random.normal(loc=0, scale=2.5))
        low_price = min(open_price, close_price) - abs(np.random.normal(loc=0, scale=2.5))
        ohlcv_data.at[i, 'open'] = open_price
        ohlcv_data.at[i, 'close'] = close_price
        ohlcv_data.at[i, 'high'] = high_price
        ohlcv_data.at[i, 'low'] = low_price

    ohlcv_data['volume'] = np.abs(np.random.normal(loc=5000, scale=2000, size=len(ohlcv_data)))
    ohlcv_data = ohlcv_data.set_index('datetime')
    #------------------------------------------------------------
    '''
    '''
    #------------------------------------------------------------
    import matplotlib.pyplot as plt
    import seaborn as sns
    import mplfinance as mpf

    # --- 可视化部分 ---
    # 1. 展示一段K线图（前200根）
    sample_len = 200
    sample_data = ohlcv_data.iloc[:sample_len].copy()
    sample_data.index.name = 'Date'  # mplfinance 需要索引名为Date或datetime

    # mplfinance 需要列名为 open, high, low, close, volume
    mpf.plot(
        sample_data,
        type='candle',
        style='charles',
        title='前200根K线（蜡烛图）',
        ylabel='价格',
        ylabel_lower='成交量',
        volume=True,
        mav=(5, 10),  # 可选：添加均线
        tight_layout=True
    )

    # 2. 展示OHLCV各自的分布直方图
    ohlcv_cols = ['open', 'high', 'low', 'close', 'volume']
    plt.figure(figsize=(15, 8))
    for i, col in enumerate(ohlcv_cols, 1):
        plt.subplot(2, 3, i)
        sns.histplot(ohlcv_data[col], bins=50, kde=True)
        plt.title(f'{col.capitalize()} 分布')
    plt.tight_layout()
    plt.show()
    #------------------------------------------------------------
    '''

    # 按8:1:1划分数据集
    from datetime import datetime, timedelta
    # 从eth_kline_filepath字符串中提取日期
    import re
    match = re.search(r'_(\d{4}-\d{2}-\d{2})_\d{2}_\d{2}_\d{2}_(\d{4}-\d{2}-\d{2})_\d{2}_\d{2}_\d{2}', eth_kline_filepath)
    if match:
        data_start = datetime.strptime(match.group(1), '%Y-%m-%d')
        data_end = datetime.strptime(match.group(2), '%Y-%m-%d')
    else:
        raise ValueError(f"无法从文件名 {eth_kline_filepath} 中提取日期")
    total_days = (data_end - data_start).days + 1

    train_days = int(total_days * 0.8)
    valid_days = int(total_days * 0.0)
    test_days = total_days - train_days - valid_days

    train_start_date = data_start.strftime('%Y-%m-%d')
    train_end_date = (data_start + timedelta(days=train_days-1)).strftime('%Y-%m-%d')
    valid_start_date = (data_start + timedelta(days=train_days)).strftime('%Y-%m-%d')
    valid_end_date = (data_start + timedelta(days=train_days + valid_days - 1)).strftime('%Y-%m-%d')
    test_start_date = (data_start + timedelta(days=train_days + valid_days)).strftime('%Y-%m-%d')
    test_end_date = data_end.strftime('%Y-%m-%d')

    print(f"训练集: {train_start_date} ~ {train_end_date}")
    print(f"验证集: {valid_start_date} ~ {valid_end_date}")
    print(f"测试集: {test_start_date} ~ {test_end_date}")

    # LARA模型超参数 (参考PDF Table 5 和 LARA.py 默认值，需为ETH调整)
    # CSI300 和 ETH 的参数会有区别，以下是一些起点，您需要实验和调优
    '''
    lara_hyperparams = {
        'use_metric': 'True',        # 是否使用度量学习
        'use_multi_label': True,     # 度量学习是否使用多级标签
        'shuffle': True,             # 是否打乱度量学习的训练数据
        'preprocessing': 'None',     # 特征预处理方法 ('StandardScaler', 'MinMaxScaler', 'None', etc.)
        'metric_method': 'ITML_Supervised', # 度量学习算法
        'ping': "True",              # 是否使用 ping 数据 (数据增强)
        'knn_rnn': 'rnn',            # 'knn', 'rnn', or 'None'. K近邻或R近邻过滤
        'points': 40,               # K值 (for KNN/RNN). PDF Table 5: 90-150
        'radius': 60,                # R值 (for RNN). PDF Table 5: 30-100
        'use_dual': True,            # 是否使用双重标签迭代优化
        'dual_numbers': 9,           # 双重标签迭代次数. PDF Table 5 "k r": k is 7 or 9. LARA default 5.
        'dual_ratio': 0.01,          # 双重标签调整比率. PDF Table 5 "k r": r is 0.03-0.10. LARA default 0.01.
        # LightGBM参数在LARA类内部定义，但可以通过kwargs传递覆盖，但通常不需要
    }
    '''
    lara_hyperparams = {
        'use_metric': 'False',        # 是否使用度量学习
        'use_multi_label': True,     # 度量学习是否使用多级标签
        'shuffle': True,             # 是否打乱度量学习的训练数据
        'preprocessing': 'None',     # 特征预处理方法 ('StandardScaler', 'MinMaxScaler', 'None', etc.)
        'metric_method': 'LSML_Supervised', # 度量学习算法'LMNN':'ITML_Supervised':'SDML_Supervised':'LSML_Supervised':'MMC_Supervised':'NCA':'LFDA':'RCA_Supervised':'MLKR':'ITML':
        'ping': "True",              # 是否使用 ping 数据 (数据增强)
        'knn_rnn': 'None',            # 'knn', 'rnn', or 'None'. K近邻或R近邻过滤
        'points': 40,               # K值 (for KNN/RNN). PDF Table 5: 90-150
        'radius': 60,                # R值 (for RNN). PDF Table 5: 30-100
        'use_dual': False,            # 是否使用双重标签迭代优化
        'dual_numbers': 9,           # 双重标签迭代次数. PDF Table 5 "k r": k is 7 or 9. LARA default 5.
        'dual_ratio': 0.01,          # 双重标签调整比率. PDF Table 5 "k r": r is 0.03-0.10. LARA default 0.01.
        # LightGBM参数在LARA类内部定义，但可以通过kwargs传递覆盖，但通常不需要
    }
    # --- 2. 数据处理 ---
    print("Initializing ETH Data Processor...")
    if os.path.exists(features_pkl_filepath):
        print(f"检测到特征文件 {features_pkl_filepath}，直接加载，无需重复计算。")
        df = pd.read_pickle(features_pkl_filepath)
        processor = ETHDataProcessor(df, instrument_name=instrument_name)
        processor.feature_columns = [col for col in processor.df.columns if col.startswith('feature_')]
    else:
        eth_df = pd.read_csv(eth_kline_filepath) # 如果从CSV加载
        processor = ETHDataProcessor(eth_df, instrument_name=instrument_name)
        print("Generating features...")
        processor.generate_features_alpha158()
        pd.to_pickle(processor.df, features_pkl_filepath)
        print(f"特征已计算并保存到 {features_pkl_filepath}")

    print("Generating labels...")
    # 假设预测未来n个周期 (例如，如果数据是1小时K线，则预测未来n小时)
    # 这个值对模型性能影响很大
    prediction_horizon = 30
    processor.generate_labels(future_periods=prediction_horizon)

    print("Getting processed data...")
    processed_df = processor.get_processed_data()

    if processed_df.empty:
        print("No data available after processing. Exiting.")
        return

    print("Splitting data for LARA...")
    # 这个lara_dataset_input对象有一个 .prepare() 方法，LARA.fit会调用它
    lara_dataset_input = processor.split_data_for_lara(
        processed_df,
        train_start_date, train_end_date,
        valid_start_date, valid_end_date,
        test_start_date, test_end_date
    )
    # 更新LARA模型将使用的目标列名(self.out)
    # LARA.py 的 __init__ 之后, fit 方法中会从 y_train.columns[0] 获取 self.out
    # 我们需要确保 ETHDataProcessor 中生成的label列名与LARA期望的一致
    # ETHDataProcessor.label_column_name 已经设置了它

    # --- 3. LARA 模型训练与预测 ---
    print(f"Initializing LARA model with params: {lara_hyperparams}")
    lara_model = LARA(**lara_hyperparams)

    print("Fitting LARA model...")
    # LARA.py 的 fit 方法会内部处理 self.out 的赋值
    # 它会从 dataset.prepare 返回的第一个数据集(train)的label的第一个列名获取
    # 我们的 ETHDataProcessor.split_data_for_lara 已经确保了这一点
    lara_model.fit(lara_dataset_input)
    print("LARA model fitting complete.")

    print("Predicting with LARA model...")
    # LARA.predict 方法实际上不使用 dataset 参数来获取数据进行预测
    # 它直接返回在 fit 过程中对测试集生成的 self.predicted
    # 因此，可以传递一个None或者模拟的DatasetH对象
    predictions = lara_model.predict(dataset=None, segment="test") # segment参数在LARA.predict中也未使用

    print("Predictions received:")
    print(predictions.head())
    print(f"Number of predictions: {len(predictions)}")

    # --- 4. 结果保存与评估 (可选) ---
    # predictions 是一个 Pandas Series，索引是 (datetime, instrument)
    # 可以将其保存到CSV
    # output_dir = 'lara_eth_results'
    # os.makedirs(output_dir, exist_ok=True)
    # predictions.to_csv(os.path.join(output_dir, f'lara_eth_predictions_{instrument_name}.csv'))
    # print(f"Predictions saved to {output_dir}")

    # 进一步的评估：计算 top N 预测样本的 Precision、Win/Loss Ratio 和 Average Return
    test_labels_df = lara_dataset_input.test_dict["label"]
    # 确保列名与 predictions 的名称 'signal' 和 test_labels_df 中的收益率列名对应
    if not predictions.empty and not test_labels_df.empty:
        # 合并预测结果和测试集真实标签
        merged_df = pd.concat([pd.DataFrame(predictions), test_labels_df], axis=1, join='inner')
        if not merged_df.empty and processor.label_column_name in merged_df.columns:
            # 参数设置，与 main.py 中一致
            count_val = 1000 if len(predictions)>=1000 else len(predictions)
            thresh_val = 0.001
            # 默认预测列为第一列，真实标签列则为 processor.label_column_name
            signal_col = merged_df.columns[0]
            label_col = processor.label_column_name

            eval_df = merged_df.copy()
            eval_df['label'] = 0
            eval_df = eval_df.sort_values(by=[signal_col], ascending=False).iloc[:count_val]
            eval_df.loc[eval_df[label_col] >= thresh_val, 'label'] = 1

            prec = eval_df['label'].sum() / count_val

            win = eval_df.loc[eval_df[label_col] > 0, label_col].mean()
            loss = eval_df.loc[eval_df[label_col] < 0, label_col].mean()
            wlr = abs(win / loss) if loss and loss != 0 else float('nan')
            ar = eval_df[label_col].sum() / count_val

            print("Evaluation Metrics:")
            print(f"Precision: {prec:.2%}")
            print(f"Win/Loss Ratio: {wlr:.2f}")
            print(f"Average Return: {ar:.4%}")
            
            # 只统计 signal 列大于 0.6 的所有结果
            filtered_df = merged_df[merged_df[signal_col] > 0.75].copy()
            if not filtered_df.empty:
                filtered_df['label'] = 0
                filtered_df.loc[filtered_df[label_col] >= thresh_val, 'label'] = 1

                prec_all = filtered_df['label'].sum() / len(filtered_df) if len(filtered_df) > 0 else float('nan')
                win_all = filtered_df.loc[filtered_df[label_col] > 0, label_col].mean()
                loss_all = filtered_df.loc[filtered_df[label_col] < 0, label_col].mean()
                wlr_all = abs(win_all / loss_all) if loss_all and loss_all != 0 else float('nan')
                ar_all = filtered_df[label_col].sum() / len(filtered_df) if len(filtered_df) > 0 else float('nan')
            else:
                prec_all = float('nan')
                win_all = float('nan')
                loss_all = float('nan')
                wlr_all = float('nan')
                ar_all = float('nan')

            print("所有样本的整体评估指标：")
            print(f"Precision: {prec_all:.2%}")
            print(f"Win/Loss Ratio: {wlr_all:.2f}")
            print(f"Average Return: {ar_all:.4%}")
        else:
            print("Could not merge predictions and labels, or label column missing.")
    else:
        print("No predictions or test labels to evaluate.")
    '''
    #--------------------------------------------------------------
    # --- Valid Set Evaluation ---
    print("Predicting valid set with LARA model...")
    valid_predictions = lara_model.predict_valid(lara_dataset_input, segment="valid")
    print("Valid predictions received:")
    print(valid_predictions.head())
    print(f"Number of valid predictions: {len(valid_predictions)}")

    valid_labels_df = lara_dataset_input.valid_dict["label"]
    if not valid_predictions.empty and not valid_labels_df.empty:
        merged_valid_df = pd.concat([pd.DataFrame(valid_predictions), valid_labels_df], axis=1, join='inner')
        if not merged_valid_df.empty and processor.label_column_name in merged_valid_df.columns:
            count_val_valid = 1000 if len(valid_predictions) >= 1000 else len(valid_predictions)
            thresh_val_valid = 0.001
            signal_col_valid = merged_valid_df.columns[0]
            label_col_valid = processor.label_column_name

            eval_valid_df = merged_valid_df.copy()
            eval_valid_df['label'] = 0
            eval_valid_df = eval_valid_df.sort_values(by=[signal_col_valid], ascending=False).iloc[:count_val_valid]
            eval_valid_df.loc[eval_valid_df[label_col_valid] >= thresh_val_valid, 'label'] = 1

            prec_valid = eval_valid_df['label'].sum() / count_val_valid
            win_valid = eval_valid_df.loc[eval_valid_df[label_col_valid] > 0, label_col_valid].mean()
            loss_valid = eval_valid_df.loc[eval_valid_df[label_col_valid] < 0, label_col_valid].mean()
            wlr_valid = abs(win_valid / loss_valid) if loss_valid and loss_valid != 0 else float('nan')
            ar_valid = eval_valid_df[label_col_valid].sum() / count_val_valid

            print("Valid Set Evaluation Metrics:")
            print(f"Precision: {prec_valid:.2%}")
            print(f"Win/Loss Ratio: {wlr_valid:.2f}")
            print(f"Average Return: {ar_valid:.4%}")
            
            # 针对信号大于0.75的样本进行整体评估
            filtered_valid_df = merged_valid_df[merged_valid_df[signal_col_valid] > 0.75].copy()
            if not filtered_valid_df.empty:
                filtered_valid_df['label'] = 0
                filtered_valid_df.loc[filtered_valid_df[label_col_valid] >= thresh_val_valid, 'label'] = 1

                prec_all_valid = (filtered_valid_df['label'].sum() / len(filtered_valid_df)) if len(filtered_valid_df) > 0 else float('nan')
                win_all_valid = filtered_valid_df.loc[filtered_valid_df[label_col_valid] > 0, label_col_valid].mean()
                loss_all_valid = filtered_valid_df.loc[filtered_valid_df[label_col_valid] < 0, label_col_valid].mean()
                wlr_all_valid = abs(win_all_valid / loss_all_valid) if loss_all_valid and loss_all_valid != 0 else float('nan')
                ar_all_valid = (filtered_valid_df[label_col_valid].sum() / len(filtered_valid_df)) if len(filtered_valid_df) > 0 else float('nan')
            else:
                prec_all_valid = float('nan')
                win_all_valid = float('nan')
                loss_all_valid = float('nan')
                wlr_all_valid = float('nan')
                ar_all_valid = float('nan')

            print("Valid Set Overall Evaluation Metrics:")
            print(f"Precision: {prec_all_valid:.2%}")
            print(f"Win/Loss Ratio: {wlr_all_valid:.2f}")
            print(f"Average Return: {ar_all_valid:.4%}")
        else:
            print("Could not merge valid predictions and labels, or label column missing.")
    else:
        print("No valid predictions or labels to evaluate.")
    '''

if __name__ == '__main__':
    main()