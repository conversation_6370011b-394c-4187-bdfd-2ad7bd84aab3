#MCD stat for robust loss family cifar10
python MCD_stat.py -c './saved/models/cifar10/resnet34/MultiStepLR/CCELoss/sym/80/config_123.json' -d 1 \
--resume_path './saved/models/cifar10/resnet34/MultiStepLR/CCELoss/sym/80/model_best123.pth'
python MCD_stat.py -c './saved/models/cifar10/resnet34/MultiStepLR/CCELoss/sym/60/config_123.json' -d 1 \
--resume_path './saved/models/cifar10/resnet34/MultiStepLR/CCELoss/sym/60/model_best123.pth'
python MCD_stat.py -c './saved/models/cifar10/resnet34/MultiStepLR/CCELoss/sym/40/config_123.json' -d 1 \
--resume_path './saved/models/cifar10/resnet34/MultiStepLR/CCELoss/sym/40/model_best123.pth'
python MCD_stat.py -c './saved/models/cifar10/resnet34/MultiStepLR/CCELoss/sym/20/config_123.json' -d 1 \
--resume_path './saved/models/cifar10/resnet34/MultiStepLR/CCELoss/sym/20/model_best123.pth'
python MCD_stat.py -c './saved/models/cifar10/resnet34/MultiStepLR/ELRLoss/sym/80/config_123.json' -d 1 \
--resume_path './saved/models/cifar10/resnet34/MultiStepLR/ELRLoss/sym/80/model_best123.pth'
python MCD_stat.py -c './saved/models/cifar10/resnet34/MultiStepLR/ELRLoss/sym/60/config_123.json' -d 1 \
--resume_path './saved/models/cifar10/resnet34/MultiStepLR/ELRLoss/sym/60/model_best123.pth'
python MCD_stat.py -c './saved/models/cifar10/resnet34/MultiStepLR/CCELoss/sym/40/config_123.json' -d 1 \
--resume_path './saved/models/cifar10/resnet34/MultiStepLR/CCELoss/sym/40/model_best123.pth'
python MCD_stat.py -c './saved/models/cifar10/resnet34/MultiStepLR/ELRLoss/sym/20/config_123.json' -d 1 \
--resume_path './saved/models/cifar10/resnet34/MultiStepLR/ELRLoss/sym/20/model_best123.pth'
python MCD_stat.py -c './saved/models/cifar10/resnet34/MultiStepLR/GCELoss/sym/80/config_123.json' -d 1 \
--resume_path './saved/models/cifar10/resnet34/MultiStepLR/GCELoss/sym/80/model_best123.pth'
python MCD_stat.py -c './saved/models/cifar10/resnet34/MultiStepLR/GCELoss/sym/60/config_123.json' -d 1 \
--resume_path './saved/models/cifar10/resnet34/MultiStepLR/GCELoss/sym/60/model_best123.pth'
python MCD_stat.py -c './saved/models/cifar10/resnet34/MultiStepLR/GCELoss/sym/40/config_123.json' -d 1 \
--resume_path './saved/models/cifar10/resnet34/MultiStepLR/GCELoss/sym/40/model_best123.pth'
python MCD_stat.py -c './saved/models/cifar10/resnet34/MultiStepLR/GCELoss/sym/20/config_123.json' -d 1 \
--resume_path './saved/models/cifar10/resnet34/MultiStepLR/GCELoss/sym/20/model_best123.pth'
python MCD_stat.py -c './saved/models/cifar10/resnet34/MultiStepLR/SCELoss/sym/80/config_123.json' -d 1 \
--resume_path './saved/models/cifar10/resnet34/MultiStepLR/SCELoss/sym/80/model_best123.pth'
python MCD_stat.py -c './saved/models/cifar10/resnet34/MultiStepLR/SCELoss/sym/60/config_123.json' -d 1 \
--resume_path './saved/models/cifar10/resnet34/MultiStepLR/SCELoss/sym/60/model_best123.pth'
python MCD_stat.py -c './saved/models/cifar10/resnet34/MultiStepLR/SCELoss/sym/40/config_123.json' -d 1 \
--resume_path './saved/models/cifar10/resnet34/MultiStepLR/SCELoss/sym/40/model_best123.pth'
python MCD_stat.py -c './saved/models/cifar10/resnet34/MultiStepLR/SCELoss/sym/20/config_123.json' -d 1 \
--resume_path './saved/models/cifar10/resnet34/MultiStepLR/SCELoss/sym/20/model_best123.pth'